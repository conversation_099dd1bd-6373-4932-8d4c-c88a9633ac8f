using FluentAssertions;
using WorkflowEngine.Application.Interfaces;
using WorkflowEngine.Domain.Entities;
using WorkflowEngine.Domain.Enums;

namespace WorkflowEngine.Tests.Integration;

public class ActivityExecutionIntegrationTests : IntegrationTestBase
{
    [Fact]
    public async Task ExecuteTimerActivity_ShouldCompleteSuccessfully()
    {
        // Arrange
        var executionService = GetService<IWorkflowExecutionService>();
        var workflowInstance = await CreateTestWorkflowInstanceAsync();
        
        // Get timer activity and step
        var activityRepo = GetService<IActivityRepository>();
        var timerActivity = await activityRepo.GetByIdAsync(TestTenantId, TestTimerActivityId);
        
        var stepRepo = GetService<IWorkflowDefinitionRepository>();
        var definition = await stepRepo.GetByIdAsync(TestTenantId, TestWorkflowDefinitionId);
        var timerStep = definition!.Steps!.First(s => s.ActivityId == TestTimerActivityId);

        // Act
        var result = await executionService.ExecuteStepAsync(workflowInstance, timerStep);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.Status.Should().Be(WorkflowInstanceStatus.Running);
        result.IsCompleted.Should().BeTrue();
    }

    [Fact]
    public async Task ExecuteDecisionActivity_ShouldEvaluateConditions()
    {
        // Arrange
        var executionService = GetService<IWorkflowExecutionService>();
        var variableService = GetService<IWorkflowVariableService>();
        var workflowInstance = await CreateTestWorkflowInstanceAsync();
        
        // Set up test variable for decision
        await variableService.SetVariableAsync(workflowInstance.Id, "test", "true");
        
        // Get decision activity and step
        var stepRepo = GetService<IWorkflowDefinitionRepository>();
        var definition = await stepRepo.GetByIdAsync(TestTenantId, TestWorkflowDefinitionId);
        var decisionStep = definition!.Steps!.First(s => s.ActivityId == TestDecisionActivityId);

        // Act
        var result = await executionService.ExecuteStepAsync(workflowInstance, decisionStep);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.Status.Should().Be(WorkflowInstanceStatus.Running);
    }

    [Fact]
    public async Task ExecuteActivityWithRetry_ShouldRetryOnFailure()
    {
        // Arrange
        var executionService = GetService<IWorkflowExecutionService>();
        var workflowInstance = await CreateTestWorkflowInstanceAsync();
        
        // Create a failing HTTP activity
        var failingActivity = new Activity
        {
            Id = Guid.NewGuid(),
            TenantId = TestTenantId,
            Name = "Failing HTTP Activity",
            Type = ActivityType.HttpRequest,
            Configuration = """{"url": "https://invalid-url-that-will-fail.com", "method": "GET"}""",
            IsActive = true
        };

        DbContext.Activities.Add(failingActivity);
        await DbContext.SaveChangesAsync();

        var failingStep = new WorkflowStep
        {
            Id = Guid.NewGuid(),
            TenantId = TestTenantId,
            WorkflowDefinitionId = TestWorkflowDefinitionId,
            ActivityId = failingActivity.Id,
            Name = "Failing Step",
            Type = StepType.Activity,
            Order = 99,
            IsRequired = true
        };

        // Act
        var result = await executionService.ExecuteStepAsync(workflowInstance, failingStep);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeFalse();
        result.Status.Should().Be(WorkflowInstanceStatus.Failed);
        result.ErrorMessage.Should().NotBeNullOrEmpty();
    }

    [Fact]
    public async Task GetNextStepAsync_ShouldReturnCorrectNextStep()
    {
        // Arrange
        var executionService = GetService<IWorkflowExecutionService>();
        var workflowInstance = await CreateTestWorkflowInstanceAsync();
        
        var stepRepo = GetService<IWorkflowDefinitionRepository>();
        var definition = await stepRepo.GetByIdAsync(TestTenantId, TestWorkflowDefinitionId);
        var firstStep = definition!.Steps!.OrderBy(s => s.Order).First();

        // Act
        var nextStep = await executionService.GetNextStepAsync(workflowInstance, firstStep);

        // Assert
        nextStep.Should().NotBeNull();
        nextStep!.Id.Should().Be(TestStep2Id);
        nextStep.Order.Should().Be(2);
    }

    [Fact]
    public async Task ShouldProgressToNextStepAsync_ShouldReturnTrueWhenStepCompleted()
    {
        // Arrange
        var executionService = GetService<IWorkflowExecutionService>();
        var activityExecutionRepo = GetService<IActivityExecutionRepository>();
        var workflowInstance = await CreateTestWorkflowInstanceAsync();
        
        var stepRepo = GetService<IWorkflowDefinitionRepository>();
        var definition = await stepRepo.GetByIdAsync(TestTenantId, TestWorkflowDefinitionId);
        var firstStep = definition!.Steps!.OrderBy(s => s.Order).First();

        // Create a completed activity execution
        var completedExecution = new ActivityExecution
        {
            TenantId = TestTenantId,
            WorkflowInstanceId = workflowInstance.Id,
            WorkflowStepId = firstStep.Id,
            ActivityId = firstStep.ActivityId,
            Status = ExecutionStatus.Completed,
            StartedAt = DateTime.UtcNow.AddMinutes(-1),
            CompletedAt = DateTime.UtcNow
        };

        await activityExecutionRepo.AddAsync(completedExecution);
        await activityExecutionRepo.SaveChangesAsync();

        // Act
        var shouldProgress = await executionService.ShouldProgressToNextStepAsync(workflowInstance, firstStep);

        // Assert
        shouldProgress.Should().BeTrue();
    }

    [Fact]
    public async Task ShouldProgressToNextStepAsync_ShouldReturnFalseWhenStepNotCompleted()
    {
        // Arrange
        var executionService = GetService<IWorkflowExecutionService>();
        var workflowInstance = await CreateTestWorkflowInstanceAsync();
        
        var stepRepo = GetService<IWorkflowDefinitionRepository>();
        var definition = await stepRepo.GetByIdAsync(TestTenantId, TestWorkflowDefinitionId);
        var firstStep = definition!.Steps!.OrderBy(s => s.Order).First();

        // Act (no activity execution exists)
        var shouldProgress = await executionService.ShouldProgressToNextStepAsync(workflowInstance, firstStep);

        // Assert
        shouldProgress.Should().BeFalse();
    }

    [Fact]
    public async Task UpdateInstanceProgressAsync_ShouldUpdateInstanceCorrectly()
    {
        // Arrange
        var executionService = GetService<IWorkflowExecutionService>();
        var instanceRepo = GetService<IWorkflowInstanceRepository>();
        var workflowInstance = await CreateTestWorkflowInstanceAsync();
        
        var result = new WorkflowExecutionResult
        {
            IsSuccess = true,
            Status = WorkflowInstanceStatus.Running,
            CurrentStepId = TestStep2Id,
            OutputData = """{"result": "success"}""",
            IsCompleted = false
        };

        // Act
        await executionService.UpdateInstanceProgressAsync(workflowInstance, result);

        // Assert
        var updatedInstance = await instanceRepo.GetByIdAsync(TestTenantId, workflowInstance.Id);
        updatedInstance.Should().NotBeNull();
        updatedInstance!.Status.Should().Be(WorkflowInstanceStatus.Running);
        updatedInstance.CurrentStepId.Should().Be(TestStep2Id);
    }

    [Fact]
    public async Task ExecuteActivityAsync_ShouldCreateActivityExecutionRecord()
    {
        // Arrange
        var executionService = GetService<IWorkflowExecutionService>();
        var activityExecutionRepo = GetService<IActivityExecutionRepository>();
        var workflowInstance = await CreateTestWorkflowInstanceAsync();
        
        var activityRepo = GetService<IActivityRepository>();
        var timerActivity = await activityRepo.GetByIdAsync(TestTenantId, TestTimerActivityId);

        var stepRepo = GetService<IWorkflowDefinitionRepository>();
        var definition = await stepRepo.GetByIdAsync(TestTenantId, TestWorkflowDefinitionId);
        var timerStep = definition!.Steps!.First(s => s.ActivityId == TestTimerActivityId);

        var context = new ActivityExecutionContext
        {
            Activity = timerActivity!,
            WorkflowInstance = workflowInstance,
            WorkflowStep = timerStep,
            Variables = new Dictionary<string, object> { { "test", "value" } },
            InputData = """{"input": "test"}"""
        };

        // Act
        var result = await executionService.ExecuteActivityAsync(context);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.Status.Should().Be(ExecutionStatus.Completed);

        // Verify activity execution record was created
        var executions = await activityExecutionRepo.GetByWorkflowInstanceAsync(TestTenantId, workflowInstance.Id);
        executions.Should().NotBeEmpty();
        executions.Should().Contain(e => e.ActivityId == TestTimerActivityId);
    }

    [Fact]
    public async Task ExecuteStepAsync_WithInvalidActivity_ShouldReturnFailure()
    {
        // Arrange
        var executionService = GetService<IWorkflowExecutionService>();
        var workflowInstance = await CreateTestWorkflowInstanceAsync();
        
        var invalidStep = new WorkflowStep
        {
            Id = Guid.NewGuid(),
            TenantId = TestTenantId,
            WorkflowDefinitionId = TestWorkflowDefinitionId,
            ActivityId = Guid.NewGuid(), // Non-existent activity
            Name = "Invalid Step",
            Type = StepType.Activity,
            Order = 99,
            IsRequired = true
        };

        // Act
        var result = await executionService.ExecuteStepAsync(workflowInstance, invalidStep);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeFalse();
        result.Status.Should().Be(WorkflowInstanceStatus.Failed);
        result.ErrorMessage.Should().Contain("not found");
    }
}
