using WorkflowEngine.Domain.Entities;
using WorkflowEngine.Domain.Enums;

namespace WorkflowEngine.Application.Interfaces;

public interface IActivityExecutor
{
    Task<ActivityExecutionResult> ExecuteAsync(ActivityExecutionContext context, CancellationToken cancellationToken = default);
    bool CanExecute(ActivityType activityType);
    Task<bool> ValidateConfigurationAsync(string? configuration, CancellationToken cancellationToken = default);
}

public class ActivityExecutionContext
{
    public required Activity Activity { get; set; }
    public required WorkflowInstance WorkflowInstance { get; set; }
    public required WorkflowStep WorkflowStep { get; set; }
    public string? InputData { get; set; }
    public Dictionary<string, object>? Variables { get; set; }
    public Dictionary<string, object>? Configuration { get; set; }
    public int RetryCount { get; set; }
    public CancellationToken CancellationToken { get; set; }
}

public class ActivityExecutionResult
{
    public bool IsSuccess { get; set; }
    public string? OutputData { get; set; }
    public string? ErrorMessage { get; set; }
    public string? StackTrace { get; set; }
    public ExecutionStatus Status { get; set; }
    public Dictionary<string, object>? Variables { get; set; }
    public TimeSpan? Duration { get; set; }
    public DateTime? NextRetryAt { get; set; }
    public bool ShouldRetry { get; set; }
    public bool RequiresUserInput { get; set; }
    public DateTime? ResumeAt { get; set; }
    
    public static ActivityExecutionResult Success(string? outputData = null, Dictionary<string, object>? variables = null)
    {
        return new ActivityExecutionResult
        {
            IsSuccess = true,
            Status = ExecutionStatus.Completed,
            OutputData = outputData,
            Variables = variables
        };
    }
    
    public static ActivityExecutionResult Failure(string errorMessage, string? stackTrace = null, bool shouldRetry = false, DateTime? nextRetryAt = null)
    {
        return new ActivityExecutionResult
        {
            IsSuccess = false,
            Status = ExecutionStatus.Failed,
            ErrorMessage = errorMessage,
            StackTrace = stackTrace,
            ShouldRetry = shouldRetry,
            NextRetryAt = nextRetryAt
        };
    }
    
    public static ActivityExecutionResult Pending(DateTime? resumeAt = null)
    {
        return new ActivityExecutionResult
        {
            IsSuccess = true,
            Status = ExecutionStatus.Pending,
            ResumeAt = resumeAt
        };
    }
    
    public static ActivityExecutionResult WaitingForInput()
    {
        return new ActivityExecutionResult
        {
            IsSuccess = true,
            Status = ExecutionStatus.Pending,
            RequiresUserInput = true
        };
    }
}
