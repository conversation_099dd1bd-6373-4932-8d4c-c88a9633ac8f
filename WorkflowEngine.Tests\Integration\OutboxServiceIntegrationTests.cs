using FluentAssertions;
using WorkflowEngine.Application.Interfaces;
using WorkflowEngine.Domain.Entities;

namespace WorkflowEngine.Tests.Integration;

public class OutboxServiceIntegrationTests : IntegrationTestBase
{
    [Fact]
    public async Task PublishEventAsync_ShouldCreateOutboxEvent()
    {
        // Arrange
        var outboxService = GetService<IOutboxService>();
        var eventData = new { Message = "Test event", Timestamp = DateTime.UtcNow };

        // Act
        var outboxEvent = await outboxService.PublishEventAsync("TestEvent", eventData);

        // Assert
        outboxEvent.Should().NotBeNull();
        outboxEvent.Id.Should().NotBeEmpty();
        outboxEvent.TenantId.Should().Be(TestTenantId);
        outboxEvent.EventType.Should().Be("TestEvent");
        outboxEvent.Status.Should().Be(OutboxEventStatus.Pending);
        outboxEvent.CorrelationId.Should().NotBeNullOrEmpty();
        outboxEvent.EventData.Should().NotBeNullOrEmpty();
    }

    [Fact]
    public async Task PublishWorkflowEventAsync_ShouldCreateWorkflowOutboxEvent()
    {
        // Arrange
        var outboxService = GetService<IOutboxService>();
        var workflowInstance = await CreateTestWorkflowInstanceAsync();
        var eventData = new { InstanceId = workflowInstance.Id, Status = "Started" };

        // Act
        var outboxEvent = await outboxService.PublishWorkflowEventAsync(
            "WorkflowInstanceStarted", 
            eventData, 
            workflowInstance.Id, 
            TestStep1Id);

        // Assert
        outboxEvent.Should().NotBeNull();
        outboxEvent.EventType.Should().Be("WorkflowInstanceStarted");
        outboxEvent.WorkflowInstanceId.Should().Be(workflowInstance.Id);
        outboxEvent.WorkflowStepId.Should().Be(TestStep1Id);
        outboxEvent.Priority.Should().Be(1); // Higher priority for workflow events
    }

    [Fact]
    public async Task GetPendingEventsAsync_ShouldReturnPendingEvents()
    {
        // Arrange
        var outboxService = GetService<IOutboxService>();
        
        // Create multiple events
        await outboxService.PublishEventAsync("Event1", new { Data = "Test1" });
        await outboxService.PublishEventAsync("Event2", new { Data = "Test2" });
        await outboxService.PublishEventAsync("Event3", new { Data = "Test3" });

        // Act
        var pendingEvents = await outboxService.GetPendingEventsAsync(10);

        // Assert
        pendingEvents.Should().HaveCount(3);
        pendingEvents.Should().OnlyContain(e => e.Status == OutboxEventStatus.Pending);
        pendingEvents.Should().OnlyContain(e => e.TenantId == TestTenantId);
    }

    [Fact]
    public async Task ProcessEventAsync_ShouldMarkEventAsProcessing()
    {
        // Arrange
        var outboxService = GetService<IOutboxService>();
        var outboxEvent = await outboxService.PublishEventAsync("TestEvent", new { Data = "Test" });

        // Act
        var processedEvent = await outboxService.ProcessEventAsync(outboxEvent);

        // Assert
        processedEvent.Status.Should().Be(OutboxEventStatus.Processing);
    }

    [Fact]
    public async Task MarkEventAsCompletedAsync_ShouldCompleteEvent()
    {
        // Arrange
        var outboxService = GetService<IOutboxService>();
        var outboxEvent = await outboxService.PublishEventAsync("TestEvent", new { Data = "Test" });

        // Act
        await outboxService.MarkEventAsCompletedAsync(outboxEvent.Id);

        // Assert
        var completedEvent = await outboxService.GetEventByIdAsync(outboxEvent.Id);
        completedEvent.Should().NotBeNull();
        completedEvent!.Status.Should().Be(OutboxEventStatus.Completed);
        completedEvent.ProcessedAt.Should().NotBeNull();
        completedEvent.ErrorMessage.Should().BeNull();
    }

    [Fact]
    public async Task MarkEventAsFailedAsync_ShouldFailEventWithRetry()
    {
        // Arrange
        var outboxService = GetService<IOutboxService>();
        var outboxEvent = await outboxService.PublishEventAsync("TestEvent", new { Data = "Test" });

        // Act
        await outboxService.MarkEventAsFailedAsync(outboxEvent.Id, "Test error", "Test stack trace");

        // Assert
        var failedEvent = await outboxService.GetEventByIdAsync(outboxEvent.Id);
        failedEvent.Should().NotBeNull();
        failedEvent!.Status.Should().Be(OutboxEventStatus.Failed);
        failedEvent.RetryCount.Should().Be(1);
        failedEvent.ErrorMessage.Should().Be("Test error");
        failedEvent.StackTrace.Should().Be("Test stack trace");
        failedEvent.NextRetryAt.Should().NotBeNull();
    }

    [Fact]
    public async Task GetEventsForRetryAsync_ShouldReturnRetryableEvents()
    {
        // Arrange
        var outboxService = GetService<IOutboxService>();
        var outboxEvent = await outboxService.PublishEventAsync("TestEvent", new { Data = "Test" });

        // Mark as failed with retry
        await outboxService.MarkEventAsFailedAsync(outboxEvent.Id, "Test error");

        // Act
        var retryEvents = await outboxService.GetEventsForRetryAsync(DateTime.UtcNow.AddMinutes(5), 10);

        // Assert
        retryEvents.Should().HaveCount(1);
        retryEvents.First().Id.Should().Be(outboxEvent.Id);
        retryEvents.First().Status.Should().Be(OutboxEventStatus.Failed);
    }

    [Fact]
    public async Task ScheduleRetryAsync_ShouldScheduleEventForRetry()
    {
        // Arrange
        var outboxService = GetService<IOutboxService>();
        var outboxEvent = await outboxService.PublishEventAsync("TestEvent", new { Data = "Test" });
        var retryTime = DateTime.UtcNow.AddMinutes(10);

        // Act
        await outboxService.ScheduleRetryAsync(outboxEvent.Id, retryTime);

        // Assert
        var scheduledEvent = await outboxService.GetEventByIdAsync(outboxEvent.Id);
        scheduledEvent.Should().NotBeNull();
        scheduledEvent!.NextRetryAt.Should().BeCloseTo(retryTime, TimeSpan.FromSeconds(1));
        scheduledEvent.Status.Should().Be(OutboxEventStatus.Failed);
    }

    [Fact]
    public async Task GetEventsByCorrelationIdAsync_ShouldReturnRelatedEvents()
    {
        // Arrange
        var outboxService = GetService<IOutboxService>();
        var correlationId = Guid.NewGuid().ToString();

        // Create events with same correlation ID
        await outboxService.PublishEventAsync("Event1", new { Data = "Test1" }, correlationId);
        await outboxService.PublishEventAsync("Event2", new { Data = "Test2" }, correlationId);
        await outboxService.PublishEventAsync("Event3", new { Data = "Test3" }); // Different correlation ID

        // Act
        var relatedEvents = await outboxService.GetEventsByCorrelationIdAsync(correlationId);

        // Assert
        relatedEvents.Should().HaveCount(2);
        relatedEvents.Should().OnlyContain(e => e.CorrelationId == correlationId);
    }

    [Fact]
    public async Task GetEventsByWorkflowInstanceAsync_ShouldReturnWorkflowEvents()
    {
        // Arrange
        var outboxService = GetService<IOutboxService>();
        var workflowInstance = await CreateTestWorkflowInstanceAsync();

        // Create workflow events
        await outboxService.PublishWorkflowEventAsync("Event1", new { Data = "Test1" }, workflowInstance.Id);
        await outboxService.PublishWorkflowEventAsync("Event2", new { Data = "Test2" }, workflowInstance.Id);
        await outboxService.PublishEventAsync("Event3", new { Data = "Test3" }); // Non-workflow event

        // Act
        var workflowEvents = await outboxService.GetEventsByWorkflowInstanceAsync(workflowInstance.Id);

        // Assert
        workflowEvents.Should().HaveCount(2);
        workflowEvents.Should().OnlyContain(e => e.WorkflowInstanceId == workflowInstance.Id);
    }

    [Fact]
    public async Task CancelEventAsync_ShouldCancelEvent()
    {
        // Arrange
        var outboxService = GetService<IOutboxService>();
        var outboxEvent = await outboxService.PublishEventAsync("TestEvent", new { Data = "Test" });

        // Act
        await outboxService.CancelEventAsync(outboxEvent.Id, "Test cancellation");

        // Assert
        var cancelledEvent = await outboxService.GetEventByIdAsync(outboxEvent.Id);
        cancelledEvent.Should().NotBeNull();
        cancelledEvent!.Status.Should().Be(OutboxEventStatus.Cancelled);
        cancelledEvent.ErrorMessage.Should().Be("Cancelled: Test cancellation");
        cancelledEvent.ProcessedAt.Should().NotBeNull();
    }

    [Fact]
    public async Task PublishEventsAsync_ShouldCreateMultipleEvents()
    {
        // Arrange
        var outboxService = GetService<IOutboxService>();
        var events = new[]
        {
            new OutboxEvent { EventType = "Event1", EventData = """{"data": "test1"}""" },
            new OutboxEvent { EventType = "Event2", EventData = """{"data": "test2"}""" },
            new OutboxEvent { EventType = "Event3", EventData = """{"data": "test3"}""" }
        };

        // Act
        await outboxService.PublishEventsAsync(events);

        // Assert
        var pendingEvents = await outboxService.GetPendingEventsAsync(10);
        pendingEvents.Should().HaveCount(3);
        pendingEvents.Should().OnlyContain(e => e.TenantId == TestTenantId);
        pendingEvents.Should().OnlyContain(e => e.Status == OutboxEventStatus.Pending);
        pendingEvents.Should().OnlyContain(e => !string.IsNullOrEmpty(e.CorrelationId));
    }
}
