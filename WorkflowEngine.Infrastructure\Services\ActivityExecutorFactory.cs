using Microsoft.Extensions.Logging;
using WorkflowEngine.Application.Interfaces;
using WorkflowEngine.Domain.Enums;

namespace WorkflowEngine.Infrastructure.Services;

public class ActivityExecutorFactory : IActivityExecutorFactory
{
    private readonly Dictionary<ActivityType, IActivityExecutor> _executorsByType;
    private readonly Dictionary<string, IActivityExecutor> _executorsByName;
    private readonly ILogger<ActivityExecutorFactory> _logger;

    public ActivityExecutorFactory(ILogger<ActivityExecutorFactory> logger)
    {
        _executorsByType = new Dictionary<ActivityType, IActivityExecutor>();
        _executorsByName = new Dictionary<string, IActivityExecutor>();
        _logger = logger;
    }

    public IActivityExecutor? GetExecutor(ActivityType activityType)
    {
        _executorsByType.TryGetValue(activityType, out var executor);
        return executor;
    }

    public IActivityExecutor? GetExecutor(string activityTypeName)
    {
        _executorsByName.TryGetValue(activityTypeName, out var executor);
        return executor;
    }

    public IEnumerable<IActivityExecutor> GetAllExecutors()
    {
        return _executorsByType.Values.Distinct();
    }

    public void RegisterExecutor(ActivityType activityType, IActivityExecutor executor)
    {
        _executorsByType[activityType] = executor;
        _logger.LogInformation("Registered activity executor for type {ActivityType}", activityType);
    }

    public void RegisterExecutor(string activityTypeName, IActivityExecutor executor)
    {
        _executorsByName[activityTypeName] = executor;
        _logger.LogInformation("Registered activity executor for type name {ActivityTypeName}", activityTypeName);
    }

    public bool IsSupported(ActivityType activityType)
    {
        return _executorsByType.ContainsKey(activityType);
    }

    public bool IsSupported(string activityTypeName)
    {
        return _executorsByName.ContainsKey(activityTypeName);
    }
}
