using Microsoft.EntityFrameworkCore;
using WorkflowEngine.Application.Interfaces;
using WorkflowEngine.Domain.Entities;
using WorkflowEngine.Domain.Enums;
using WorkflowEngine.Infrastructure.Data;

namespace WorkflowEngine.Infrastructure.Repositories;

public class ActivityExecutionRepository : BaseTenantRepository<ActivityExecution>, IActivityExecutionRepository
{
    public ActivityExecutionRepository(WorkflowDbContext context) : base(context)
    {
    }

    public async Task<IEnumerable<ActivityExecution>> GetByWorkflowInstanceAsync(Guid tenantId, Guid workflowInstanceId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(ae => ae.TenantId == tenantId && ae.WorkflowInstanceId == workflowInstanceId)
            .OrderBy(ae => ae.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<ActivityExecution>> GetByStatusAsync(Guid tenantId, ExecutionStatus status, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(ae => ae.TenantId == tenantId && ae.Status == status)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<ActivityExecution>> GetPendingExecutionsAsync(DateTime beforeTime, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(ae => ae.Status == ExecutionStatus.Pending && ae.CreatedAt <= beforeTime)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<ActivityExecution>> GetFailedExecutionsForRetryAsync(DateTime beforeTime, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(ae => ae.Status == ExecutionStatus.Failed && 
                        ae.NextRetryAt != null && 
                        ae.NextRetryAt <= beforeTime &&
                        ae.RetryCount < ae.MaxRetries)
            .ToListAsync(cancellationToken);
    }

    public async Task<ActivityExecution?> GetLatestExecutionAsync(Guid tenantId, Guid workflowInstanceId, Guid workflowStepId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(ae => ae.TenantId == tenantId && 
                        ae.WorkflowInstanceId == workflowInstanceId && 
                        ae.WorkflowStepId == workflowStepId)
            .OrderByDescending(ae => ae.CreatedAt)
            .FirstOrDefaultAsync(cancellationToken);
    }
}
