[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[/] NAME:Build Scalable Multi-Tenant BPM Workflow Engine DESCRIPTION:Complete implementation of a scalable, multi-tenant BPM workflow engine backend using .NET 8 Web API and PostgreSQL, following Clean Architecture principles across 5 phases
--[x] NAME:Phase 1: Core Architecture Setup DESCRIPTION:Establish the foundational architecture including project structure, entities, database schema, and basic workflow engine
---[x] NAME:Initialize .NET 8 Web API Project Structure DESCRIPTION:Create new .NET 8 Web API project with Clean Architecture folder structure (API, Application, Domain, Infrastructure layers)
---[x] NAME:Configure Project Dependencies DESCRIPTION:Add and configure NuGet packages for EF Core, PostgreSQL, JWT authentication, and other core dependencies
---[x] NAME:Create Domain Entities DESCRIPTION:Define core domain entities: Tenant, WorkflowDefinition, WorkflowInstance, WorkflowStep, Activity, ActivityExecution with proper relationships
---[x] NAME:Design Database Schema with EF Core DESCRIPTION:Configure EF Core DbContext, entity configurations, and multi-tenant data model with row-level filtering
---[x] NAME:Create and Run Initial Migrations DESCRIPTION:Generate EF Core migrations for the database schema and test database creation
---[x] NAME:Implement Repository Pattern DESCRIPTION:Create repository interfaces and base implementations for data access with multi-tenant support
---[x] NAME:Set Up Tenant Context Management DESCRIPTION:Implement tenant context service and multi-tenancy infrastructure for data isolation
---[x] NAME:Create IWorkflowEngine Interface DESCRIPTION:Define workflow engine interface with methods for starting instances, progressing steps, resuming on events, and persisting state
---[x] NAME:Implement Basic Workflow Engine DESCRIPTION:Create initial workflow engine implementation with core state management and persistence logic
---[x] NAME:Write Unit Tests for Domain Layer DESCRIPTION:Create comprehensive unit tests for domain entities, value objects, and business logic
--[/] NAME:Phase 2: Workflow Execution Logic DESCRIPTION:Implement core workflow execution capabilities including activities, background processing, and event handling
---[x] NAME:Implement Core Activity Types DESCRIPTION:Create activity implementations for Delay/Timer, HTTP Request, Decision (If/Else), and Script execution with proper interfaces
---[x] NAME:Create Background Processor Service DESCRIPTION:Implement IHostedService for background workflow execution with proper lifecycle management
---[x] NAME:Implement Workflow Step Execution Logic DESCRIPTION:Build the core logic for executing workflow steps, handling transitions, and managing execution context
---[x] NAME:Add Retry and Failure Handling DESCRIPTION:Implement retry policies, failure handling mechanisms, and dead letter queue for failed workflow steps
---[ ] NAME:Implement Outbox Pattern DESCRIPTION:Create outbox pattern implementation for reliable asynchronous event processing and message delivery
---[ ] NAME:Create Workflow Progression Logic DESCRIPTION:Implement workflow state transitions, condition evaluation, and next step determination logic
---[ ] NAME:Write Integration Tests for Execution DESCRIPTION:Create integration tests for workflow execution, activity processing, and background service functionality
--[ ] NAME:Phase 3: API & Multi-Tenant Access DESCRIPTION:Build REST APIs with authentication, authorization, and tenant isolation for workflow management
---[ ] NAME:Set Up JWT Authentication Infrastructure DESCRIPTION:Configure JWT authentication, token validation, and security middleware for API endpoints
---[ ] NAME:Create Tenant Resolver Middleware DESCRIPTION:Implement middleware to resolve tenant context from requests and ensure proper tenant isolation
---[ ] NAME:Implement Workflow Definition APIs DESCRIPTION:Create REST endpoints for creating, updating, versioning, and managing workflow definitions
---[ ] NAME:Create Workflow Instance Management APIs DESCRIPTION:Build APIs for starting workflow instances, monitoring progress, and managing instance lifecycle
---[ ] NAME:Add Human Task Completion APIs DESCRIPTION:Implement endpoints for advancing/completing human tasks and handling user interactions
---[ ] NAME:Implement Status and History Query APIs DESCRIPTION:Create APIs for querying workflow instance status, execution history, and audit trails
---[ ] NAME:Add Authorization Policies DESCRIPTION:Implement role-based authorization policies and ensure proper tenant data isolation in APIs
---[ ] NAME:Write API Integration Tests DESCRIPTION:Create comprehensive integration tests for all API endpoints with multi-tenant scenarios
--[ ] NAME:Phase 4: Monitoring, Logging & Resilience DESCRIPTION:Add comprehensive observability, logging, metrics, and health monitoring capabilities
---[ ] NAME:Set Up Serilog Structured Logging DESCRIPTION:Configure Serilog with structured logging, log enrichment, and appropriate sinks for the application
---[ ] NAME:Implement OpenTelemetry Tracing DESCRIPTION:Add OpenTelemetry instrumentation for distributed tracing across workflow execution and API calls
---[ ] NAME:Add Prometheus Metrics Endpoints DESCRIPTION:Implement custom metrics for workflow execution, performance counters, and business metrics with Prometheus export
---[ ] NAME:Create Health Checks and Readiness Probes DESCRIPTION:Implement health check endpoints for database connectivity, external dependencies, and service readiness
---[ ] NAME:Add Error Handling and Resilience Patterns DESCRIPTION:Implement global error handling, circuit breakers, and resilience patterns for external service calls
--[ ] NAME:Phase 5: Extensibility DESCRIPTION:Implement plugin architecture, dynamic schema binding, and scripting support for extensibility
---[ ] NAME:Design Plugin Architecture for Activities DESCRIPTION:Create extensible plugin system for custom activity types with proper interfaces and discovery mechanisms
---[ ] NAME:Implement Dynamic JSON Schema Binding DESCRIPTION:Add support for dynamic JSON schema validation and binding for workflow inputs and outputs
---[ ] NAME:Add C# Expression/Roslyn Scripting Support DESCRIPTION:Implement scripting capabilities using C# expressions or Roslyn for dynamic workflow logic
---[ ] NAME:Create Extensibility Documentation DESCRIPTION:Write comprehensive documentation and examples for plugin development and extensibility features
---[ ] NAME:Final Integration Testing DESCRIPTION:Perform end-to-end testing of the complete system with all features integrated