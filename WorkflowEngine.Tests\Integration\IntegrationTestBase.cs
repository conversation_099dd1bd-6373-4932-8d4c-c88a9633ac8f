using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using WorkflowEngine.Application.Interfaces;
using WorkflowEngine.Domain.Entities;
using WorkflowEngine.Domain.Enums;
using WorkflowEngine.Infrastructure.Data;
using WorkflowEngine.Infrastructure.Services;
using WorkflowEngine.Infrastructure;

namespace WorkflowEngine.Tests.Integration;

public abstract class IntegrationTestBase : IDisposable
{
    protected readonly ServiceProvider ServiceProvider;
    protected readonly WorkflowDbContext DbContext;
    protected readonly Guid TestTenantId;

    protected IntegrationTestBase()
    {
        TestTenantId = Guid.NewGuid();

        var services = new ServiceCollection();

        // Add logging
        services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Debug));

        // Add in-memory database
        services.AddDbContext<WorkflowDbContext>(options =>
            options.UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString()));

        // Add test tenant context
        services.AddScoped<ITenantContext>(provider => new TestTenantContext(TestTenantId));

        // Add infrastructure services (without database and HTTP context)
        AddInfrastructureServicesForTesting(services);

        ServiceProvider = services.BuildServiceProvider();
        DbContext = ServiceProvider.GetRequiredService<WorkflowDbContext>();

        // Ensure database is created
        DbContext.Database.EnsureCreated();

        // Seed test data
        SeedTestData();
    }

    protected virtual void SeedTestData()
    {
        // Create test tenant
        var tenant = new Tenant
        {
            Id = TestTenantId,
            Name = "Test Tenant",
            Description = "Test tenant for integration tests",
            IsActive = true
        };

        DbContext.Tenants.Add(tenant);

        // Create test activities
        var timerActivity = new Activity
        {
            Id = Guid.NewGuid(),
            TenantId = TestTenantId,
            Name = "Test Timer Activity",
            Type = ActivityType.Timer,
            Configuration = """{"delaySeconds": 1}""",
            IsActive = true
        };

        var httpActivity = new Activity
        {
            Id = Guid.NewGuid(),
            TenantId = TestTenantId,
            Name = "Test HTTP Activity",
            Type = ActivityType.HttpRequest,
            Configuration = """{"url": "https://httpbin.org/get", "method": "GET"}""",
            IsActive = true
        };

        var decisionActivity = new Activity
        {
            Id = Guid.NewGuid(),
            TenantId = TestTenantId,
            Name = "Test Decision Activity",
            Type = ActivityType.Decision,
            Configuration = """{"conditions": [{"variableName": "test", "operator": "Equals", "value": "true"}]}""",
            IsActive = true
        };

        DbContext.Activities.AddRange(timerActivity, httpActivity, decisionActivity);

        // Create test workflow definition
        var workflowDefinition = new WorkflowDefinition
        {
            Id = Guid.NewGuid(),
            TenantId = TestTenantId,
            Name = "Test Workflow",
            Description = "Test workflow for integration tests",
            Version = 1,
            Status = WorkflowStatus.Published,
            IsActive = true
        };

        DbContext.WorkflowDefinitions.Add(workflowDefinition);

        // Create test workflow steps
        var step1 = new WorkflowStep
        {
            Id = Guid.NewGuid(),
            TenantId = TestTenantId,
            WorkflowDefinitionId = workflowDefinition.Id,
            ActivityId = timerActivity.Id,
            Name = "Timer Step",
            Description = "Timer step for testing",
            Type = StepType.Activity,
            Order = 1,
            IsRequired = true
        };

        var step2 = new WorkflowStep
        {
            Id = Guid.NewGuid(),
            TenantId = TestTenantId,
            WorkflowDefinitionId = workflowDefinition.Id,
            ActivityId = httpActivity.Id,
            Name = "HTTP Step",
            Description = "HTTP step for testing",
            Type = StepType.Activity,
            Order = 2,
            IsRequired = true
        };

        var step3 = new WorkflowStep
        {
            Id = Guid.NewGuid(),
            TenantId = TestTenantId,
            WorkflowDefinitionId = workflowDefinition.Id,
            ActivityId = decisionActivity.Id,
            Name = "Decision Step",
            Description = "Decision step for testing",
            Type = StepType.Decision,
            Order = 3,
            IsRequired = true
        };

        // Set up step progression
        step1.NextStepId = step2.Id;
        step2.NextStepId = step3.Id;

        DbContext.WorkflowSteps.AddRange(step1, step2, step3);

        // Update workflow definition with steps
        workflowDefinition.Steps = new List<WorkflowStep> { step1, step2, step3 };

        DbContext.SaveChanges();

        // Store references for tests
        TestWorkflowDefinitionId = workflowDefinition.Id;
        TestTimerActivityId = timerActivity.Id;
        TestHttpActivityId = httpActivity.Id;
        TestDecisionActivityId = decisionActivity.Id;
        TestStep1Id = step1.Id;
        TestStep2Id = step2.Id;
        TestStep3Id = step3.Id;
    }

    protected Guid TestWorkflowDefinitionId { get; private set; }
    protected Guid TestTimerActivityId { get; private set; }
    protected Guid TestHttpActivityId { get; private set; }
    protected Guid TestDecisionActivityId { get; private set; }
    protected Guid TestStep1Id { get; private set; }
    protected Guid TestStep2Id { get; private set; }
    protected Guid TestStep3Id { get; private set; }

    protected T GetService<T>() where T : notnull
    {
        return ServiceProvider.GetRequiredService<T>();
    }

    protected async Task<WorkflowInstance> CreateTestWorkflowInstanceAsync(string? inputData = null)
    {
        var workflowEngine = GetService<IWorkflowEngine>();
        return await workflowEngine.StartInstanceAsync(TestWorkflowDefinitionId, inputData, "Test Instance");
    }

    private static void AddInfrastructureServicesForTesting(IServiceCollection services)
    {
        // Repositories
        services.AddScoped<ITenantRepository, WorkflowEngine.Infrastructure.Repositories.TenantRepository>();
        services.AddScoped<IWorkflowDefinitionRepository, WorkflowEngine.Infrastructure.Repositories.WorkflowDefinitionRepository>();
        services.AddScoped<IWorkflowInstanceRepository, WorkflowEngine.Infrastructure.Repositories.WorkflowInstanceRepository>();
        services.AddScoped<IActivityRepository, WorkflowEngine.Infrastructure.Repositories.ActivityRepository>();
        services.AddScoped<IActivityExecutionRepository, WorkflowEngine.Infrastructure.Repositories.ActivityExecutionRepository>();
        services.AddScoped<IDeadLetterQueueRepository, WorkflowEngine.Infrastructure.Repositories.DeadLetterQueueRepository>();
        services.AddScoped<IOutboxEventRepository, WorkflowEngine.Infrastructure.Repositories.OutboxEventRepository>();

        // Workflow Services
        services.AddScoped<IWorkflowEngine, WorkflowEngine.Infrastructure.Services.WorkflowEngine>();
        services.AddScoped<IWorkflowVariableService, WorkflowEngine.Infrastructure.Services.WorkflowVariableService>();
        services.AddScoped<IWorkflowExecutionService, WorkflowEngine.Infrastructure.Services.WorkflowExecutionService>();
        services.AddScoped<IWorkflowStepTransitionService, WorkflowEngine.Infrastructure.Services.WorkflowStepTransitionService>();
        services.AddScoped<IRetryPolicyService, WorkflowEngine.Infrastructure.Services.RetryPolicyService>();
        services.AddScoped<IDeadLetterQueueService, WorkflowEngine.Infrastructure.Services.DeadLetterQueueService>();
        services.AddScoped<IOutboxService, WorkflowEngine.Infrastructure.Services.OutboxService>();

        // Activity Executors
        services.AddScoped<WorkflowEngine.Infrastructure.Activities.TimerActivityExecutor>();
        services.AddScoped<WorkflowEngine.Infrastructure.Activities.HttpRequestActivityExecutor>();
        services.AddScoped<WorkflowEngine.Infrastructure.Activities.DecisionActivityExecutor>();
        services.AddScoped<WorkflowEngine.Infrastructure.Activities.ScriptActivityExecutor>();

        // HTTP Client for HTTP Request Activity
        services.AddHttpClient<WorkflowEngine.Infrastructure.Activities.HttpRequestActivityExecutor>();

        // Activity Executor Factory with registered executors
        services.AddSingleton<IActivityExecutorFactory>(provider =>
        {
            var factory = new WorkflowEngine.Infrastructure.Services.ActivityExecutorFactory(provider.GetRequiredService<ILogger<WorkflowEngine.Infrastructure.Services.ActivityExecutorFactory>>());

            // Register activity executors
            var timerExecutor = provider.GetRequiredService<WorkflowEngine.Infrastructure.Activities.TimerActivityExecutor>();
            var httpExecutor = provider.GetRequiredService<WorkflowEngine.Infrastructure.Activities.HttpRequestActivityExecutor>();
            var decisionExecutor = provider.GetRequiredService<WorkflowEngine.Infrastructure.Activities.DecisionActivityExecutor>();
            var scriptExecutor = provider.GetRequiredService<WorkflowEngine.Infrastructure.Activities.ScriptActivityExecutor>();

            factory.RegisterExecutor(ActivityType.Timer, timerExecutor);
            factory.RegisterExecutor(ActivityType.HttpRequest, httpExecutor);
            factory.RegisterExecutor(ActivityType.Decision, decisionExecutor);
            factory.RegisterExecutor(ActivityType.Script, scriptExecutor);

            return factory;
        });

        // Outbox Event Handlers
        services.AddScoped<IOutboxEventHandler, WorkflowEngine.Infrastructure.Services.OutboxEventHandlers.WorkflowInstanceStartedEventHandler>();
        services.AddScoped<IOutboxEventHandler, WorkflowEngine.Infrastructure.Services.OutboxEventHandlers.WorkflowInstanceCompletedEventHandler>();
        services.AddScoped<IOutboxEventHandler, WorkflowEngine.Infrastructure.Services.OutboxEventHandlers.WorkflowStepCompletedEventHandler>();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
        ServiceProvider?.Dispose();
    }
}

public class TestTenantContext : ITenantContext
{
    public TestTenantContext(Guid tenantId, Guid? userId = null)
    {
        TenantId = tenantId;
        UserId = userId ?? Guid.NewGuid();
    }

    public Guid TenantId { get; set; }
    public Guid? UserId { get; set; }
}
