﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "WorkflowEngine.API", "WorkflowEngine.API\WorkflowEngine.API.csproj", "{9EB56CAA-A43D-4541-AA3D-8DDF4B708570}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "WorkflowEngine.Domain", "WorkflowEngine.Domain\WorkflowEngine.Domain.csproj", "{8192BBD0-308D-4D49-B33A-691B60C854A6}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "WorkflowEngine.Application", "WorkflowEngine.Application\WorkflowEngine.Application.csproj", "{0D720E41-EB8F-4A3B-9BAB-F76D3B552F00}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "WorkflowEngine.Infrastructure", "WorkflowEngine.Infrastructure\WorkflowEngine.Infrastructure.csproj", "{D438AC31-DDE0-498B-AFA7-53711510A511}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "WorkflowEngine.Tests", "WorkflowEngine.Tests\WorkflowEngine.Tests.csproj", "{885A9997-D34C-452B-B67C-9032BBC8B602}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{9EB56CAA-A43D-4541-AA3D-8DDF4B708570}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9EB56CAA-A43D-4541-AA3D-8DDF4B708570}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9EB56CAA-A43D-4541-AA3D-8DDF4B708570}.Debug|x64.ActiveCfg = Debug|Any CPU
		{9EB56CAA-A43D-4541-AA3D-8DDF4B708570}.Debug|x64.Build.0 = Debug|Any CPU
		{9EB56CAA-A43D-4541-AA3D-8DDF4B708570}.Debug|x86.ActiveCfg = Debug|Any CPU
		{9EB56CAA-A43D-4541-AA3D-8DDF4B708570}.Debug|x86.Build.0 = Debug|Any CPU
		{9EB56CAA-A43D-4541-AA3D-8DDF4B708570}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9EB56CAA-A43D-4541-AA3D-8DDF4B708570}.Release|Any CPU.Build.0 = Release|Any CPU
		{9EB56CAA-A43D-4541-AA3D-8DDF4B708570}.Release|x64.ActiveCfg = Release|Any CPU
		{9EB56CAA-A43D-4541-AA3D-8DDF4B708570}.Release|x64.Build.0 = Release|Any CPU
		{9EB56CAA-A43D-4541-AA3D-8DDF4B708570}.Release|x86.ActiveCfg = Release|Any CPU
		{9EB56CAA-A43D-4541-AA3D-8DDF4B708570}.Release|x86.Build.0 = Release|Any CPU
		{8192BBD0-308D-4D49-B33A-691B60C854A6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8192BBD0-308D-4D49-B33A-691B60C854A6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8192BBD0-308D-4D49-B33A-691B60C854A6}.Debug|x64.ActiveCfg = Debug|Any CPU
		{8192BBD0-308D-4D49-B33A-691B60C854A6}.Debug|x64.Build.0 = Debug|Any CPU
		{8192BBD0-308D-4D49-B33A-691B60C854A6}.Debug|x86.ActiveCfg = Debug|Any CPU
		{8192BBD0-308D-4D49-B33A-691B60C854A6}.Debug|x86.Build.0 = Debug|Any CPU
		{8192BBD0-308D-4D49-B33A-691B60C854A6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8192BBD0-308D-4D49-B33A-691B60C854A6}.Release|Any CPU.Build.0 = Release|Any CPU
		{8192BBD0-308D-4D49-B33A-691B60C854A6}.Release|x64.ActiveCfg = Release|Any CPU
		{8192BBD0-308D-4D49-B33A-691B60C854A6}.Release|x64.Build.0 = Release|Any CPU
		{8192BBD0-308D-4D49-B33A-691B60C854A6}.Release|x86.ActiveCfg = Release|Any CPU
		{8192BBD0-308D-4D49-B33A-691B60C854A6}.Release|x86.Build.0 = Release|Any CPU
		{0D720E41-EB8F-4A3B-9BAB-F76D3B552F00}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0D720E41-EB8F-4A3B-9BAB-F76D3B552F00}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0D720E41-EB8F-4A3B-9BAB-F76D3B552F00}.Debug|x64.ActiveCfg = Debug|Any CPU
		{0D720E41-EB8F-4A3B-9BAB-F76D3B552F00}.Debug|x64.Build.0 = Debug|Any CPU
		{0D720E41-EB8F-4A3B-9BAB-F76D3B552F00}.Debug|x86.ActiveCfg = Debug|Any CPU
		{0D720E41-EB8F-4A3B-9BAB-F76D3B552F00}.Debug|x86.Build.0 = Debug|Any CPU
		{0D720E41-EB8F-4A3B-9BAB-F76D3B552F00}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0D720E41-EB8F-4A3B-9BAB-F76D3B552F00}.Release|Any CPU.Build.0 = Release|Any CPU
		{0D720E41-EB8F-4A3B-9BAB-F76D3B552F00}.Release|x64.ActiveCfg = Release|Any CPU
		{0D720E41-EB8F-4A3B-9BAB-F76D3B552F00}.Release|x64.Build.0 = Release|Any CPU
		{0D720E41-EB8F-4A3B-9BAB-F76D3B552F00}.Release|x86.ActiveCfg = Release|Any CPU
		{0D720E41-EB8F-4A3B-9BAB-F76D3B552F00}.Release|x86.Build.0 = Release|Any CPU
		{D438AC31-DDE0-498B-AFA7-53711510A511}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D438AC31-DDE0-498B-AFA7-53711510A511}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D438AC31-DDE0-498B-AFA7-53711510A511}.Debug|x64.ActiveCfg = Debug|Any CPU
		{D438AC31-DDE0-498B-AFA7-53711510A511}.Debug|x64.Build.0 = Debug|Any CPU
		{D438AC31-DDE0-498B-AFA7-53711510A511}.Debug|x86.ActiveCfg = Debug|Any CPU
		{D438AC31-DDE0-498B-AFA7-53711510A511}.Debug|x86.Build.0 = Debug|Any CPU
		{D438AC31-DDE0-498B-AFA7-53711510A511}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D438AC31-DDE0-498B-AFA7-53711510A511}.Release|Any CPU.Build.0 = Release|Any CPU
		{D438AC31-DDE0-498B-AFA7-53711510A511}.Release|x64.ActiveCfg = Release|Any CPU
		{D438AC31-DDE0-498B-AFA7-53711510A511}.Release|x64.Build.0 = Release|Any CPU
		{D438AC31-DDE0-498B-AFA7-53711510A511}.Release|x86.ActiveCfg = Release|Any CPU
		{D438AC31-DDE0-498B-AFA7-53711510A511}.Release|x86.Build.0 = Release|Any CPU
		{885A9997-D34C-452B-B67C-9032BBC8B602}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{885A9997-D34C-452B-B67C-9032BBC8B602}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{885A9997-D34C-452B-B67C-9032BBC8B602}.Debug|x64.ActiveCfg = Debug|Any CPU
		{885A9997-D34C-452B-B67C-9032BBC8B602}.Debug|x64.Build.0 = Debug|Any CPU
		{885A9997-D34C-452B-B67C-9032BBC8B602}.Debug|x86.ActiveCfg = Debug|Any CPU
		{885A9997-D34C-452B-B67C-9032BBC8B602}.Debug|x86.Build.0 = Debug|Any CPU
		{885A9997-D34C-452B-B67C-9032BBC8B602}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{885A9997-D34C-452B-B67C-9032BBC8B602}.Release|Any CPU.Build.0 = Release|Any CPU
		{885A9997-D34C-452B-B67C-9032BBC8B602}.Release|x64.ActiveCfg = Release|Any CPU
		{885A9997-D34C-452B-B67C-9032BBC8B602}.Release|x64.Build.0 = Release|Any CPU
		{885A9997-D34C-452B-B67C-9032BBC8B602}.Release|x86.ActiveCfg = Release|Any CPU
		{885A9997-D34C-452B-B67C-9032BBC8B602}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal
