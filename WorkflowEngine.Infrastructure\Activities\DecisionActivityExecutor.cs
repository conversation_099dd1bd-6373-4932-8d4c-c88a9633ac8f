using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Text.RegularExpressions;
using WorkflowEngine.Application.Interfaces;
using WorkflowEngine.Domain.Enums;

namespace WorkflowEngine.Infrastructure.Activities;

public class DecisionActivityExecutor : BaseActivityExecutor
{
    public DecisionActivityExecutor(ILogger<DecisionActivityExecutor> logger) : base(logger)
    {
    }

    public override bool CanExecute(ActivityType activityType)
    {
        return activityType == ActivityType.Decision;
    }

    public override async Task<ActivityExecutionResult> ExecuteAsync(ActivityExecutionContext context, CancellationToken cancellationToken = default)
    {
        var startTime = DateTime.UtcNow;
        
        try
        {
            _logger.LogInformation("Executing decision activity for instance {InstanceId}, step {StepId}", 
                context.WorkflowInstance.Id, context.WorkflowStep.Id);

            var config = ParseConfiguration(context.Activity.Configuration);
            
            // Get decision configuration
            var conditions = GetConfigurationValue<List<DecisionCondition>>(config, "conditions", new List<DecisionCondition>());
            var defaultOutcome = GetConfigurationValue<string>(config, "defaultOutcome", "false");
            var evaluationType = GetConfigurationValue<string>(config, "evaluationType", "simple"); // simple, expression, script

            if (!conditions.Any())
            {
                return CreateFailureResult("Decision activity must have at least one condition");
            }

            string outcome = defaultOutcome;
            string? matchedCondition = null;

            // Evaluate conditions in order
            foreach (var condition in conditions)
            {
                bool conditionResult = false;

                switch (evaluationType.ToLowerInvariant())
                {
                    case "simple":
                        conditionResult = await EvaluateSimpleCondition(condition, context.Variables);
                        break;
                    case "expression":
                        conditionResult = await EvaluateExpressionCondition(condition, context.Variables);
                        break;
                    case "script":
                        conditionResult = await EvaluateScriptCondition(condition, context.Variables);
                        break;
                    default:
                        _logger.LogWarning("Unknown evaluation type: {EvaluationType}", evaluationType);
                        conditionResult = await EvaluateSimpleCondition(condition, context.Variables);
                        break;
                }

                if (conditionResult)
                {
                    outcome = condition.Outcome;
                    matchedCondition = condition.Expression;
                    _logger.LogDebug("Condition matched: {Expression} -> {Outcome}", condition.Expression, outcome);
                    break;
                }
            }

            var duration = DateTime.UtcNow - startTime;
            
            var outputData = JsonSerializer.Serialize(new
            {
                outcome = outcome,
                matchedCondition = matchedCondition,
                evaluationType = evaluationType,
                evaluatedAt = DateTime.UtcNow,
                conditionsEvaluated = conditions.Count
            });

            // Set outcome as a variable for the workflow
            var variables = new Dictionary<string, object>
            {
                ["decision_outcome"] = outcome,
                ["decision_matched_condition"] = matchedCondition ?? string.Empty
            };

            _logger.LogInformation("Decision completed with outcome: {Outcome}", outcome);
            return CreateSuccessResult(outputData, variables, duration);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing decision activity for instance {InstanceId}", context.WorkflowInstance.Id);
            return CreateFailureResult($"Decision activity failed: {ex.Message}", ex, shouldRetry: false);
        }
    }

    private async Task<bool> EvaluateSimpleCondition(DecisionCondition condition, Dictionary<string, object>? variables)
    {
        try
        {
            // Simple condition format: "variableName operator value"
            // Examples: "status == 'approved'", "count > 10", "enabled == true"
            
            var expression = condition.Expression;
            if (variables != null)
            {
                expression = SubstituteVariables(expression, variables);
            }

            // Parse simple expressions
            var patterns = new[]
            {
                @"^(.+?)\s*(==|!=|>=|<=|>|<)\s*(.+)$",
                @"^(.+?)\s+(equals|not_equals|greater_than|less_than|contains)\s+(.+)$"
            };

            foreach (var pattern in patterns)
            {
                var match = Regex.Match(expression.Trim(), pattern, RegexOptions.IgnoreCase);
                if (match.Success)
                {
                    var left = match.Groups[1].Value.Trim();
                    var op = match.Groups[2].Value.Trim().ToLowerInvariant();
                    var right = match.Groups[3].Value.Trim();

                    return EvaluateComparison(left, op, right);
                }
            }

            // If no pattern matches, try to evaluate as boolean
            if (bool.TryParse(expression, out var boolResult))
            {
                return boolResult;
            }

            _logger.LogWarning("Could not parse simple condition: {Expression}", condition.Expression);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error evaluating simple condition: {Expression}", condition.Expression);
            return false;
        }
    }

    private bool EvaluateComparison(string left, string op, string right)
    {
        // Remove quotes from string values
        left = left.Trim('"', '\'');
        right = right.Trim('"', '\'');

        switch (op)
        {
            case "==" or "equals":
                return string.Equals(left, right, StringComparison.OrdinalIgnoreCase);
            
            case "!=" or "not_equals":
                return !string.Equals(left, right, StringComparison.OrdinalIgnoreCase);
            
            case "contains":
                return left.Contains(right, StringComparison.OrdinalIgnoreCase);
            
            case ">" or "greater_than":
                return CompareNumeric(left, right) > 0;
            
            case "<" or "less_than":
                return CompareNumeric(left, right) < 0;
            
            case ">=" or "greater_than_or_equal":
                return CompareNumeric(left, right) >= 0;
            
            case "<=" or "less_than_or_equal":
                return CompareNumeric(left, right) <= 0;
            
            default:
                _logger.LogWarning("Unknown comparison operator: {Operator}", op);
                return false;
        }
    }

    private int CompareNumeric(string left, string right)
    {
        if (double.TryParse(left, out var leftNum) && double.TryParse(right, out var rightNum))
        {
            return leftNum.CompareTo(rightNum);
        }
        
        // Fallback to string comparison
        return string.Compare(left, right, StringComparison.OrdinalIgnoreCase);
    }

    private async Task<bool> EvaluateExpressionCondition(DecisionCondition condition, Dictionary<string, object>? variables)
    {
        // Placeholder for expression evaluation using a library like NCalc
        // For now, fall back to simple evaluation
        return await EvaluateSimpleCondition(condition, variables);
    }

    private async Task<bool> EvaluateScriptCondition(DecisionCondition condition, Dictionary<string, object>? variables)
    {
        // Placeholder for script evaluation using Roslyn or similar
        // For now, fall back to simple evaluation
        return await EvaluateSimpleCondition(condition, variables);
    }

    public override async Task<bool> ValidateConfigurationAsync(string? configuration, CancellationToken cancellationToken = default)
    {
        if (!await base.ValidateConfigurationAsync(configuration, cancellationToken))
        {
            return false;
        }

        var config = ParseConfiguration(configuration);
        if (config == null)
        {
            return false;
        }

        // Validate conditions
        var conditions = GetConfigurationValue<List<DecisionCondition>>(config, "conditions", new List<DecisionCondition>());
        if (!conditions.Any())
        {
            _logger.LogWarning("Decision activity must have at least one condition");
            return false;
        }

        foreach (var condition in conditions)
        {
            if (string.IsNullOrEmpty(condition.Expression) || string.IsNullOrEmpty(condition.Outcome))
            {
                _logger.LogWarning("Decision condition must have both expression and outcome");
                return false;
            }
        }

        return true;
    }
}

public class DecisionCondition
{
    public string Expression { get; set; } = string.Empty;
    public string Outcome { get; set; } = string.Empty;
    public string? Description { get; set; }
}
