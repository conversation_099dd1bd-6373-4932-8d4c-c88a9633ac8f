using WorkflowEngine.Domain.Entities;
using WorkflowEngine.Domain.Enums;

namespace WorkflowEngine.Application.Interfaces;

public interface IActivityService
{
    // CRUD Operations
    Task<Activity> CreateAsync(Activity activity, CancellationToken cancellationToken = default);
    Task<Activity?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<IEnumerable<Activity>> GetAllAsync(CancellationToken cancellationToken = default);
    Task<Activity> UpdateAsync(Activity activity, CancellationToken cancellationToken = default);
    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);

    // Activity-specific operations
    Task<Activity?> GetByNameAndVersionAsync(string name, string version, CancellationToken cancellationToken = default);
    Task<IEnumerable<Activity>> GetByTypeAsync(ActivityType type, CancellationToken cancellationToken = default);
    Task<IEnumerable<Activity>> GetActiveActivitiesAsync(CancellationToken cancellationToken = default);
    Task<bool> ValidateConfigurationAsync(Guid activityId, string? configuration, CancellationToken cancellationToken = default);
    Task<Activity> CreateNewVersionAsync(Guid activityId, CancellationToken cancellationToken = default);
    Task<bool> CanDeleteAsync(Guid activityId, CancellationToken cancellationToken = default);
}
