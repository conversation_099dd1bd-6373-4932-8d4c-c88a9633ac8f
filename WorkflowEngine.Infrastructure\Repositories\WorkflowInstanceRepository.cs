using Microsoft.EntityFrameworkCore;
using WorkflowEngine.Application.Interfaces;
using WorkflowEngine.Domain.Entities;
using WorkflowEngine.Domain.Enums;
using WorkflowEngine.Infrastructure.Data;

namespace WorkflowEngine.Infrastructure.Repositories;

public class WorkflowInstanceRepository : BaseTenantRepository<WorkflowInstance>, IWorkflowInstanceRepository
{
    public WorkflowInstanceRepository(WorkflowDbContext context) : base(context)
    {
    }

    public async Task<IEnumerable<WorkflowInstance>> GetByStatusAsync(Guid tenantId, WorkflowInstanceStatus status, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(wi => wi.TenantId == tenantId && wi.Status == status)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<WorkflowInstance>> GetByWorkflowDefinitionAsync(Guid tenantId, Guid workflowDefinitionId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(wi => wi.TenantId == tenantId && wi.WorkflowDefinitionId == workflowDefinitionId)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<WorkflowInstance>> GetPendingExecutionAsync(DateTime beforeTime, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(wi => wi.Status == WorkflowInstanceStatus.Running && 
                        wi.NextExecutionTime != null && 
                        wi.NextExecutionTime <= beforeTime)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<WorkflowInstance>> GetRunningInstancesAsync(Guid tenantId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(wi => wi.TenantId == tenantId && wi.Status == WorkflowInstanceStatus.Running)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<WorkflowInstance>> GetInstancesForRetryAsync(DateTime beforeTime, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(wi => wi.Status == WorkflowInstanceStatus.Failed && 
                        wi.NextExecutionTime != null && 
                        wi.NextExecutionTime <= beforeTime &&
                        wi.RetryCount < 3) // Max retry limit
            .ToListAsync(cancellationToken);
    }
}
