using Microsoft.EntityFrameworkCore;
using WorkflowEngine.Application.Interfaces;
using WorkflowEngine.Domain.Entities;
using WorkflowEngine.Domain.Enums;
using WorkflowEngine.Infrastructure.Data;

namespace WorkflowEngine.Infrastructure.Repositories;

public class WorkflowDefinitionRepository : BaseTenantRepository<WorkflowDefinition>, IWorkflowDefinitionRepository
{
    public WorkflowDefinitionRepository(WorkflowDbContext context) : base(context)
    {
    }

    public async Task<WorkflowDefinition?> GetByNameAndVersionAsync(Guid tenantId, string name, int version, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(wd => wd.Steps)
            .FirstOrDefaultAsync(wd => wd.TenantId == tenantId && wd.Name == name && wd.Version == version, cancellationToken);
    }

    public async Task<WorkflowDefinition?> GetLatestVersionAsync(Guid tenantId, string name, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(wd => wd.Steps)
            .Where(wd => wd.TenantId == tenantId && wd.Name == name)
            .OrderByDescending(wd => wd.Version)
            .FirstOrDefaultAsync(cancellationToken);
    }

    public async Task<IEnumerable<WorkflowDefinition>> GetByStatusAsync(Guid tenantId, WorkflowStatus status, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(wd => wd.TenantId == tenantId && wd.Status == status)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<WorkflowDefinition>> GetActiveDefinitionsAsync(Guid tenantId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(wd => wd.TenantId == tenantId && wd.IsActive && wd.Status == WorkflowStatus.Published)
            .ToListAsync(cancellationToken);
    }

    public async Task<int> GetNextVersionAsync(Guid tenantId, string name, CancellationToken cancellationToken = default)
    {
        var maxVersion = await _dbSet
            .Where(wd => wd.TenantId == tenantId && wd.Name == name)
            .MaxAsync(wd => (int?)wd.Version, cancellationToken);
        
        return (maxVersion ?? 0) + 1;
    }
}
