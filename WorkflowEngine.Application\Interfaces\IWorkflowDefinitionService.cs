using WorkflowEngine.Domain.Entities;
using WorkflowEngine.Domain.Enums;

namespace WorkflowEngine.Application.Interfaces;

public interface IWorkflowDefinitionService
{
    // CRUD Operations
    Task<WorkflowDefinition> CreateAsync(WorkflowDefinition workflowDefinition, CancellationToken cancellationToken = default);
    Task<WorkflowDefinition?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<IEnumerable<WorkflowDefinition>> GetAllAsync(CancellationToken cancellationToken = default);
    Task<WorkflowDefinition> UpdateAsync(WorkflowDefinition workflowDefinition, CancellationToken cancellationToken = default);
    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);

    // Definition Management
    Task<WorkflowDefinition> CreateDefinitionAsync(string name, string? description = null, CancellationToken cancellationToken = default);
    Task<WorkflowDefinition> UpdateDefinitionAsync(Guid definitionId, string? description = null, string? jsonDefinition = null, CancellationToken cancellationToken = default);
    Task<WorkflowDefinition> PublishDefinitionAsync(Guid definitionId, CancellationToken cancellationToken = default);
    Task<WorkflowDefinition> CreateNewVersionAsync(Guid definitionId, CancellationToken cancellationToken = default);
    
    // Definition Retrieval
    Task<WorkflowDefinition?> GetDefinitionAsync(Guid definitionId, CancellationToken cancellationToken = default);
    Task<WorkflowDefinition?> GetDefinitionAsync(string name, int? version = null, CancellationToken cancellationToken = default);
    Task<WorkflowDefinition?> GetLatestDefinitionAsync(string name, CancellationToken cancellationToken = default);
    Task<IEnumerable<WorkflowDefinition>> GetDefinitionsAsync(WorkflowStatus? status = null, CancellationToken cancellationToken = default);
    Task<IEnumerable<WorkflowDefinition>> GetActiveDefinitionsAsync(CancellationToken cancellationToken = default);
    
    // Step Management
    Task<WorkflowStep> AddStepAsync(Guid definitionId, string stepName, Guid activityId, int order, CancellationToken cancellationToken = default);
    Task<WorkflowStep> UpdateStepAsync(Guid stepId, string? stepName = null, string? configuration = null, CancellationToken cancellationToken = default);
    Task RemoveStepAsync(Guid stepId, CancellationToken cancellationToken = default);
    Task<WorkflowStep> SetNextStepAsync(Guid stepId, Guid? nextStepId, CancellationToken cancellationToken = default);
    Task<WorkflowStep> SetFailureStepAsync(Guid stepId, Guid? failureStepId, CancellationToken cancellationToken = default);
    
    // Validation
    Task<WorkflowValidationResult> ValidateDefinitionAsync(Guid definitionId, CancellationToken cancellationToken = default);
    Task<bool> CanDeleteDefinitionAsync(Guid definitionId, CancellationToken cancellationToken = default);
    
    // Import/Export
    Task<WorkflowDefinition> ImportDefinitionAsync(string jsonDefinition, CancellationToken cancellationToken = default);
    Task<string> ExportDefinitionAsync(Guid definitionId, CancellationToken cancellationToken = default);
}

public class WorkflowValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    
    public void AddError(string error)
    {
        Errors.Add(error);
        IsValid = false;
    }
    
    public void AddWarning(string warning)
    {
        Warnings.Add(warning);
    }
    
    public static WorkflowValidationResult Valid()
    {
        return new WorkflowValidationResult { IsValid = true };
    }
    
    public static WorkflowValidationResult Invalid(params string[] errors)
    {
        var result = new WorkflowValidationResult { IsValid = false };
        result.Errors.AddRange(errors);
        return result;
    }
}
