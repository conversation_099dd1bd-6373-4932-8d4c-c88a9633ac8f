using System.Security.Claims;
using WorkflowEngine.API.Models.Auth;
using WorkflowEngine.Domain.Entities;

namespace WorkflowEngine.API.Services;

public interface IJwtService
{
    string GenerateToken(User user, Tenant tenant, List<string> roles);
    string GenerateRefreshToken();
    ClaimsPrincipal? GetPrincipalFromExpiredToken(string token);
    bool ValidateToken(string token);
    DateTime GetTokenExpiration(string token);
}

public class JwtClaims
{
    public const string UserId = "user_id";
    public const string TenantId = "tenant_id";
    public const string Email = "email";
    public const string FirstName = "first_name";
    public const string LastName = "last_name";
    public const string Roles = "roles";
    public const string TenantName = "tenant_name";
    public const string TenantSlug = "tenant_slug";
}
