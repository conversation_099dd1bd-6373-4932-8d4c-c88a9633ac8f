using WorkflowEngine.Domain.Common;

namespace WorkflowEngine.Domain.Entities;

public class OutboxEvent : BaseEntity, ITenantEntity
{
    public Guid TenantId { get; set; }
    public string EventType { get; set; } = string.Empty;
    public string EventData { get; set; } = string.Empty;
    public string? CorrelationId { get; set; }
    public string? CausationId { get; set; }
    public Guid? WorkflowInstanceId { get; set; }
    public Guid? WorkflowStepId { get; set; }
    public Guid? ActivityExecutionId { get; set; }
    public OutboxEventStatus Status { get; set; } = OutboxEventStatus.Pending;
    public int RetryCount { get; set; } = 0;
    public int MaxRetries { get; set; } = 3;
    public DateTime? NextRetryAt { get; set; }
    public DateTime? ProcessedAt { get; set; }
    public string? ErrorMessage { get; set; }
    public string? StackTrace { get; set; }
    public Dictionary<string, string>? Headers { get; set; }
    public int Priority { get; set; } = 0; // Higher number = higher priority

    // Navigation properties
    public virtual Tenant Tenant { get; set; } = null!;
    public virtual WorkflowInstance? WorkflowInstance { get; set; }
    public virtual WorkflowStep? WorkflowStep { get; set; }
    public virtual ActivityExecution? ActivityExecution { get; set; }
}

public enum OutboxEventStatus
{
    Pending = 0,
    Processing = 1,
    Completed = 2,
    Failed = 3,
    Cancelled = 4
}
