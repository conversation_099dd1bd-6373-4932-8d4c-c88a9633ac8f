using System.ComponentModel.DataAnnotations;

namespace WorkflowEngine.API.DTOs.WorkflowDefinition;

public class CreateWorkflowDefinitionRequest
{
    [Required]
    [StringLength(200, MinimumLength = 1)]
    public string Name { get; set; } = string.Empty;

    [StringLength(1000)]
    public string Description { get; set; } = string.Empty;

    [Required]
    [Range(1, int.MaxValue)]
    public int Version { get; set; } = 1;

    public bool IsActive { get; set; } = true;

    public Dictionary<string, object>? Configuration { get; set; }

    [Required]
    public List<CreateWorkflowStepRequest> Steps { get; set; } = new();
}

public class CreateWorkflowStepRequest
{
    [Required]
    [StringLength(200, MinimumLength = 1)]
    public string Name { get; set; } = string.Empty;

    [StringLength(1000)]
    public string Description { get; set; } = string.Empty;

    [Required]
    public string Type { get; set; } = string.Empty; // Start, Activity, Decision, End

    [Required]
    [Range(0, int.MaxValue)]
    public int Order { get; set; }

    public Dictionary<string, object>? Configuration { get; set; }

    public List<string>? NextStepNames { get; set; }

    public string? FailureStepName { get; set; }

    // Activity-specific properties
    public Guid? ActivityId { get; set; }

    public Dictionary<string, object>? ActivityConfiguration { get; set; }

    // Decision-specific properties
    public List<CreateDecisionConditionRequest>? Conditions { get; set; }
}

public class CreateDecisionConditionRequest
{
    [Required]
    [StringLength(200, MinimumLength = 1)]
    public string Field { get; set; } = string.Empty;

    [Required]
    [StringLength(50, MinimumLength = 1)]
    public string Operator { get; set; } = string.Empty; // Equals, NotEquals, GreaterThan, etc.

    public object? Value { get; set; }

    [Required]
    [StringLength(200, MinimumLength = 1)]
    public string NextStepName { get; set; } = string.Empty;
}
