using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WorkflowEngine.API.DTOs.Tenant;
using WorkflowEngine.Application.Interfaces;
using WorkflowEngine.Domain.Entities;
using WorkflowEngine.Infrastructure.Data;

namespace WorkflowEngine.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class TenantController : ControllerBase
{
    private readonly ITenantService _tenantService;
    private readonly ITenantContext _tenantContext;
    private readonly ILogger<TenantController> _logger;

    public TenantController(
        ITenantService tenantService,
        ITenantContext tenantContext,
        ILogger<TenantController> logger)
    {
        _tenantService = tenantService;
        _tenantContext = tenantContext;
        _logger = logger;
    }

    /// <summary>
    /// Create a new tenant with admin user
    /// </summary>
    [HttpPost]
    [AllowAnonymous] // Allow anonymous access for tenant creation
    public async Task<ActionResult<TenantResponse>> CreateTenant([FromBody] CreateTenantRequest request)
    {
        try
        {
            var tenant = await _tenantService.CreateTenantWithAdminAsync(
                request.Name,
                request.Slug,
                request.Description,
                request.Settings,
                request.AdminEmail,
                request.AdminFirstName,
                request.AdminLastName,
                request.AdminPassword);

            var response = await MapToTenantResponse(tenant);
            
            _logger.LogInformation("Created tenant {TenantId} with slug {TenantSlug}", tenant.Id, tenant.Slug);
            
            return CreatedAtAction(nameof(GetTenant), new { id = tenant.Id }, response);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating tenant");
            return StatusCode(500, new { message = "An error occurred while creating the tenant" });
        }
    }

    /// <summary>
    /// Get tenant by ID
    /// </summary>
    [HttpGet("{id:guid}")]
    [Authorize(Roles = "Admin,TenantAdmin")]
    public async Task<ActionResult<TenantResponse>> GetTenant(Guid id)
    {
        try
        {
            // Check if user can access this tenant
            if (_tenantContext.TenantId != id && !User.IsInRole("Admin"))
            {
                return Forbid();
            }

            var tenant = await _tenantService.GetTenantByIdAsync(id);
            if (tenant == null)
            {
                return NotFound();
            }

            var response = await MapToTenantResponse(tenant);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tenant {TenantId}", id);
            return StatusCode(500, new { message = "An error occurred while retrieving the tenant" });
        }
    }

    /// <summary>
    /// Get tenant by slug
    /// </summary>
    [HttpGet("by-slug/{slug}")]
    [AllowAnonymous]
    public async Task<ActionResult<TenantResponse>> GetTenantBySlug(string slug)
    {
        try
        {
            var tenant = await _tenantService.GetTenantBySlugAsync(slug);
            if (tenant == null)
            {
                return NotFound();
            }

            var response = await MapToTenantResponse(tenant);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tenant by slug {TenantSlug}", slug);
            return StatusCode(500, new { message = "An error occurred while retrieving the tenant" });
        }
    }

    /// <summary>
    /// Get current tenant information
    /// </summary>
    [HttpGet("current")]
    public async Task<ActionResult<TenantResponse>> GetCurrentTenant()
    {
        try
        {
            var tenant = await _tenantService.GetCurrentTenantAsync();
            if (tenant == null)
            {
                return NotFound();
            }

            var response = await MapToTenantResponse(tenant);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting current tenant");
            return StatusCode(500, new { message = "An error occurred while retrieving the current tenant" });
        }
    }

    /// <summary>
    /// Get all tenants (Admin only)
    /// </summary>
    [HttpGet]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<IEnumerable<TenantListResponse>>> GetAllTenants()
    {
        try
        {
            var tenants = await _tenantService.GetAllTenantsAsync();
            var responses = new List<TenantListResponse>();

            foreach (var tenant in tenants)
            {
                responses.Add(await MapToTenantListResponse(tenant));
            }

            return Ok(responses);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all tenants");
            return StatusCode(500, new { message = "An error occurred while retrieving tenants" });
        }
    }

    /// <summary>
    /// Update tenant information
    /// </summary>
    [HttpPut("{id:guid}")]
    [Authorize(Roles = "Admin,TenantAdmin")]
    public async Task<ActionResult<TenantResponse>> UpdateTenant(Guid id, [FromBody] UpdateTenantRequest request)
    {
        try
        {
            // Check if user can access this tenant
            if (_tenantContext.TenantId != id && !User.IsInRole("Admin"))
            {
                return Forbid();
            }

            var tenant = await _tenantService.UpdateTenantAsync(
                id,
                request.Name,
                request.Description,
                request.IsActive,
                request.Settings);

            var response = await MapToTenantResponse(tenant);
            
            _logger.LogInformation("Updated tenant {TenantId}", id);
            
            return Ok(response);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating tenant {TenantId}", id);
            return StatusCode(500, new { message = "An error occurred while updating the tenant" });
        }
    }

    /// <summary>
    /// Delete tenant (Admin only)
    /// </summary>
    [HttpDelete("{id:guid}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> DeleteTenant(Guid id)
    {
        try
        {
            var success = await _tenantService.DeleteTenantAsync(id);
            if (!success)
            {
                return NotFound();
            }

            _logger.LogInformation("Deleted tenant {TenantId}", id);
            
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting tenant {TenantId}", id);
            return StatusCode(500, new { message = "An error occurred while deleting the tenant" });
        }
    }

    /// <summary>
    /// Activate tenant (Admin only)
    /// </summary>
    [HttpPost("{id:guid}/activate")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> ActivateTenant(Guid id)
    {
        try
        {
            var success = await _tenantService.ActivateTenantAsync(id);
            if (!success)
            {
                return NotFound();
            }

            _logger.LogInformation("Activated tenant {TenantId}", id);
            
            return Ok(new { message = "Tenant activated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error activating tenant {TenantId}", id);
            return StatusCode(500, new { message = "An error occurred while activating the tenant" });
        }
    }

    /// <summary>
    /// Deactivate tenant (Admin only)
    /// </summary>
    [HttpPost("{id:guid}/deactivate")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> DeactivateTenant(Guid id)
    {
        try
        {
            var success = await _tenantService.DeactivateTenantAsync(id);
            if (!success)
            {
                return NotFound();
            }

            _logger.LogInformation("Deactivated tenant {TenantId}", id);
            
            return Ok(new { message = "Tenant deactivated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deactivating tenant {TenantId}", id);
            return StatusCode(500, new { message = "An error occurred while deactivating the tenant" });
        }
    }

    /// <summary>
    /// Get tenant statistics
    /// </summary>
    [HttpGet("{id:guid}/stats")]
    [Authorize(Roles = "Admin,TenantAdmin")]
    public async Task<ActionResult<TenantStatsResponse>> GetTenantStats(Guid id, [FromQuery] DateTime? fromDate = null, [FromQuery] DateTime? toDate = null)
    {
        try
        {
            // Check if user can access this tenant
            if (_tenantContext.TenantId != id && !User.IsInRole("Admin"))
            {
                return Forbid();
            }

            var stats = await _tenantService.GetTenantStatsAsync(id, fromDate, toDate);
            var tenant = await _tenantService.GetTenantByIdAsync(id);

            if (tenant == null)
            {
                return NotFound();
            }

            var response = new TenantStatsResponse
            {
                TenantId = tenant.Id,
                TenantName = tenant.Name,
                TenantSlug = tenant.Slug,
                TotalUsers = (int)(stats.GetValueOrDefault("UserCount", 0)),
                ActiveUsers = (int)(stats.GetValueOrDefault("UserCount", 0)), // TODO: Implement active user count
                InactiveUsers = 0,
                TotalWorkflowDefinitions = (int)(stats.GetValueOrDefault("WorkflowDefinitionCount", 0)),
                ActiveWorkflowDefinitions = (int)(stats.GetValueOrDefault("WorkflowDefinitionCount", 0)),
                TotalWorkflowInstances = (int)(stats.GetValueOrDefault("TotalWorkflowInstances", 0)),
                RunningWorkflowInstances = (int)(stats.GetValueOrDefault("RunningWorkflowInstances", 0)),
                CompletedWorkflowInstances = (int)(stats.GetValueOrDefault("CompletedWorkflowInstances", 0)),
                FailedWorkflowInstances = (int)(stats.GetValueOrDefault("FailedWorkflowInstances", 0)),
                SuspendedWorkflowInstances = 0,
                TotalActivities = 0,
                TotalActivityExecutions = 0,
                SuccessfulActivityExecutions = 0,
                FailedActivityExecutions = 0,
                AverageWorkflowDuration = (double)(stats.GetValueOrDefault("AverageWorkflowDurationMinutes", 0.0)),
                WorkflowSuccessRate = (double)(stats.GetValueOrDefault("WorkflowSuccessRate", 0.0)),
                ActivitySuccessRate = 0.0,
                TotalStorageUsed = 0,
                DeadLetterQueueCount = 0,
                OutboxEventCount = 0,
                StatsPeriodStart = (DateTime)(stats.GetValueOrDefault("StatsPeriodStart", DateTime.UtcNow.AddDays(-30))),
                StatsPeriodEnd = (DateTime)(stats.GetValueOrDefault("StatsPeriodEnd", DateTime.UtcNow)),
                GeneratedAt = (DateTime)(stats.GetValueOrDefault("GeneratedAt", DateTime.UtcNow))
            };

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tenant stats for {TenantId}", id);
            return StatusCode(500, new { message = "An error occurred while retrieving tenant statistics" });
        }
    }

    private async Task<TenantResponse> MapToTenantResponse(Tenant tenant)
    {
        return new TenantResponse
        {
            Id = tenant.Id,
            Name = tenant.Name,
            Slug = tenant.Slug,
            Description = tenant.Description,
            IsActive = tenant.IsActive,
            Settings = tenant.Settings,
            CreatedAt = tenant.CreatedAt,
            UpdatedAt = tenant.UpdatedAt,
            CreatedBy = tenant.CreatedBy,
            UpdatedBy = tenant.UpdatedBy,
            UserCount = await _tenantService.GetTenantUserCountAsync(tenant.Id),
            WorkflowDefinitionCount = await _tenantService.GetTenantWorkflowDefinitionCountAsync(tenant.Id),
            ActiveWorkflowInstanceCount = await _tenantService.GetTenantActiveWorkflowInstanceCountAsync(tenant.Id)
        };
    }

    private async Task<TenantListResponse> MapToTenantListResponse(Tenant tenant)
    {
        return new TenantListResponse
        {
            Id = tenant.Id,
            Name = tenant.Name,
            Slug = tenant.Slug,
            Description = tenant.Description,
            IsActive = tenant.IsActive,
            CreatedAt = tenant.CreatedAt,
            UpdatedAt = tenant.UpdatedAt,
            UserCount = await _tenantService.GetTenantUserCountAsync(tenant.Id),
            WorkflowDefinitionCount = await _tenantService.GetTenantWorkflowDefinitionCountAsync(tenant.Id),
            ActiveWorkflowInstanceCount = await _tenantService.GetTenantActiveWorkflowInstanceCountAsync(tenant.Id)
        };
    }
}
