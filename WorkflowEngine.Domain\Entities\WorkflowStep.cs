using WorkflowEngine.Domain.Common;
using WorkflowEngine.Domain.Enums;

namespace WorkflowEngine.Domain.Entities;

public class WorkflowStep : BaseEntity, ITenantEntity
{
    public Guid TenantId { get; set; }
    public Guid WorkflowDefinitionId { get; set; }
    public Guid ActivityId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int Order { get; set; }
    public StepType Type { get; set; }
    public string? Configuration { get; set; }
    public string? Conditions { get; set; }
    public Guid? NextStepId { get; set; }
    public Guid? FailureStepId { get; set; }
    public bool IsRequired { get; set; } = true;
    public int TimeoutSeconds { get; set; } = 300;

    // Navigation properties
    public virtual Tenant Tenant { get; set; } = null!;
    public virtual WorkflowDefinition WorkflowDefinition { get; set; } = null!;
    public virtual Activity Activity { get; set; } = null!;
    public virtual WorkflowStep? NextStep { get; set; }
    public virtual WorkflowStep? FailureStep { get; set; }
    public virtual ICollection<WorkflowStep> PreviousSteps { get; set; } = new List<WorkflowStep>();
    public virtual ICollection<WorkflowStep> FailurePreviousSteps { get; set; } = new List<WorkflowStep>();
    public virtual ICollection<ActivityExecution> ActivityExecutions { get; set; } = new List<ActivityExecution>();
}
