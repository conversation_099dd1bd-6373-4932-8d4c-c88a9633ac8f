using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using WorkflowEngine.Application.Interfaces;
using WorkflowEngine.Domain.Entities;
using WorkflowEngine.Domain.Enums;
using WorkflowEngine.Infrastructure.Data;

namespace WorkflowEngine.Infrastructure.Services;

public class ActivityService : IActivityService
{
    private readonly IActivityRepository _activityRepository;
    private readonly ITenantContext _tenantContext;
    private readonly ILogger<ActivityService> _logger;

    public ActivityService(
        IActivityRepository activityRepository,
        ITenantContext tenantContext,
        ILogger<ActivityService> logger)
    {
        _activityRepository = activityRepository;
        _tenantContext = tenantContext;
        _logger = logger;
    }

    public async Task<Activity> CreateAsync(Activity activity, CancellationToken cancellationToken = default)
    {
        activity.TenantId = _tenantContext.TenantId;
        
        // Check if activity with same name and version already exists
        var existing = await _activityRepository.GetByNameAndVersionAsync(
            _tenantContext.TenantId, activity.Name, activity.Version ?? "1.0.0", cancellationToken);
        
        if (existing != null)
        {
            throw new InvalidOperationException($"Activity with name '{activity.Name}' and version '{activity.Version}' already exists");
        }

        await _activityRepository.AddAsync(activity, cancellationToken);
        await _activityRepository.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Created activity {ActivityId} with name {Name} and version {Version}",
            activity.Id, activity.Name, activity.Version);

        return activity;
    }

    public async Task<Activity?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _activityRepository.GetByIdAsync(_tenantContext.TenantId, id, cancellationToken);
    }

    public async Task<IEnumerable<Activity>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        return await _activityRepository.GetAllAsync(_tenantContext.TenantId, cancellationToken);
    }

    public async Task<Activity> UpdateAsync(Activity activity, CancellationToken cancellationToken = default)
    {
        var existing = await _activityRepository.GetByIdAsync(_tenantContext.TenantId, activity.Id, cancellationToken);
        if (existing == null)
        {
            throw new InvalidOperationException($"Activity with ID {activity.Id} not found");
        }

        // Update properties
        existing.Name = activity.Name;
        existing.Description = activity.Description;
        existing.Configuration = activity.Configuration;
        existing.InputSchema = activity.InputSchema;
        existing.OutputSchema = activity.OutputSchema;
        existing.IsActive = activity.IsActive;
        existing.AssemblyName = activity.AssemblyName;
        existing.ClassName = activity.ClassName;

        await _activityRepository.UpdateAsync(existing, cancellationToken);
        await _activityRepository.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Updated activity {ActivityId}", activity.Id);

        return existing;
    }

    public async Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var activity = await _activityRepository.GetByIdAsync(_tenantContext.TenantId, id, cancellationToken);
        if (activity == null)
        {
            throw new InvalidOperationException($"Activity with ID {id} not found");
        }

        // Check if activity can be deleted (not used in any workflow steps)
        if (!await CanDeleteAsync(id, cancellationToken))
        {
            throw new InvalidOperationException("Cannot delete activity that is being used in workflow definitions");
        }

        await _activityRepository.DeleteAsync(activity, cancellationToken);
        await _activityRepository.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Deleted activity {ActivityId}", id);
    }

    public async Task<Activity?> GetByNameAndVersionAsync(string name, string version, CancellationToken cancellationToken = default)
    {
        return await _activityRepository.GetByNameAndVersionAsync(_tenantContext.TenantId, name, version, cancellationToken);
    }

    public async Task<IEnumerable<Activity>> GetByTypeAsync(ActivityType type, CancellationToken cancellationToken = default)
    {
        return await _activityRepository.GetByTypeAsync(_tenantContext.TenantId, type, cancellationToken);
    }

    public async Task<IEnumerable<Activity>> GetActiveActivitiesAsync(CancellationToken cancellationToken = default)
    {
        return await _activityRepository.GetActiveActivitiesAsync(_tenantContext.TenantId, cancellationToken);
    }

    public async Task<bool> ValidateConfigurationAsync(Guid activityId, string? configuration, CancellationToken cancellationToken = default)
    {
        var activity = await _activityRepository.GetByIdAsync(_tenantContext.TenantId, activityId, cancellationToken);
        if (activity == null)
        {
            return false;
        }

        // Basic validation - could be enhanced with schema validation
        if (string.IsNullOrEmpty(configuration))
        {
            return true; // Empty configuration is valid
        }

        try
        {
            // Try to parse as JSON if it's a JSON configuration
            if (configuration.TrimStart().StartsWith("{") || configuration.TrimStart().StartsWith("["))
            {
                System.Text.Json.JsonDocument.Parse(configuration);
            }
            return true;
        }
        catch
        {
            return false;
        }
    }

    public async Task<Activity> CreateNewVersionAsync(Guid activityId, CancellationToken cancellationToken = default)
    {
        var existingActivity = await _activityRepository.GetByIdAsync(_tenantContext.TenantId, activityId, cancellationToken);
        if (existingActivity == null)
        {
            throw new InvalidOperationException($"Activity with ID {activityId} not found");
        }

        // Parse current version and increment
        var currentVersion = existingActivity.Version ?? "1.0.0";
        var versionParts = currentVersion.Split('.');
        if (versionParts.Length >= 2 && int.TryParse(versionParts[1], out var minorVersion))
        {
            versionParts[1] = (minorVersion + 1).ToString();
        }
        else
        {
            versionParts = new[] { "1", "1", "0" };
        }

        var newVersion = string.Join(".", versionParts);

        var newActivity = new Activity
        {
            TenantId = _tenantContext.TenantId,
            Name = existingActivity.Name,
            Description = existingActivity.Description,
            Type = existingActivity.Type,
            Configuration = existingActivity.Configuration,
            InputSchema = existingActivity.InputSchema,
            OutputSchema = existingActivity.OutputSchema,
            IsActive = true,
            Version = newVersion,
            AssemblyName = existingActivity.AssemblyName,
            ClassName = existingActivity.ClassName
        };

        await _activityRepository.AddAsync(newActivity, cancellationToken);
        await _activityRepository.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Created new version {Version} of activity {ActivityName} with ID {NewActivityId}",
            newVersion, existingActivity.Name, newActivity.Id);

        return newActivity;
    }

    public async Task<bool> CanDeleteAsync(Guid activityId, CancellationToken cancellationToken = default)
    {
        // Check if activity is used in any workflow steps
        // This would require access to workflow step repository
        // For now, return true - this should be implemented properly
        await Task.CompletedTask;
        return true;
    }
}
