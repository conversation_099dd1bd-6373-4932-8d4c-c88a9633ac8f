using Microsoft.Extensions.Logging;
using System.Text.Json;
using WorkflowEngine.Application.Interfaces;
using WorkflowEngine.Domain.Entities;

namespace WorkflowEngine.Infrastructure.Services.OutboxEventHandlers;

public class WorkflowInstanceStartedEventHandler : IOutboxEventHandler
{
    private readonly ILogger<WorkflowInstanceStartedEventHandler> _logger;

    public string EventType => "WorkflowInstanceStarted";

    public WorkflowInstanceStartedEventHandler(ILogger<WorkflowInstanceStartedEventHandler> logger)
    {
        _logger = logger;
    }

    public async Task<bool> CanHandleAsync(OutboxEvent outboxEvent, CancellationToken cancellationToken = default)
    {
        return outboxEvent.EventType == EventType && outboxEvent.WorkflowInstanceId.HasValue;
    }

    public async Task<OutboxEventHandlerResult> HandleAsync(OutboxEvent outboxEvent, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Processing workflow instance started event for instance {InstanceId}", 
                outboxEvent.WorkflowInstanceId);

            // Parse event data
            var eventData = JsonSerializer.Deserialize<WorkflowInstanceStartedEventData>(outboxEvent.EventData);
            if (eventData == null)
            {
                return OutboxEventHandlerResult.Failure("Invalid event data format", false);
            }

            // Here you could:
            // - Send notifications
            // - Update external systems
            // - Trigger webhooks
            // - Log to audit systems
            // - Update metrics/monitoring

            _logger.LogInformation("Workflow instance {InstanceId} started for definition {DefinitionId} by {StartedBy}", 
                eventData.InstanceId, eventData.WorkflowDefinitionId, eventData.StartedBy);

            return OutboxEventHandlerResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling workflow instance started event {EventId}", outboxEvent.Id);
            return OutboxEventHandlerResult.Failure($"Handler error: {ex.Message}", true, null, ex.StackTrace);
        }
    }
}

public class WorkflowInstanceCompletedEventHandler : IOutboxEventHandler
{
    private readonly ILogger<WorkflowInstanceCompletedEventHandler> _logger;

    public string EventType => "WorkflowInstanceCompleted";

    public WorkflowInstanceCompletedEventHandler(ILogger<WorkflowInstanceCompletedEventHandler> logger)
    {
        _logger = logger;
    }

    public async Task<bool> CanHandleAsync(OutboxEvent outboxEvent, CancellationToken cancellationToken = default)
    {
        return outboxEvent.EventType == EventType && outboxEvent.WorkflowInstanceId.HasValue;
    }

    public async Task<OutboxEventHandlerResult> HandleAsync(OutboxEvent outboxEvent, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Processing workflow instance completed event for instance {InstanceId}", 
                outboxEvent.WorkflowInstanceId);

            var eventData = JsonSerializer.Deserialize<WorkflowInstanceCompletedEventData>(outboxEvent.EventData);
            if (eventData == null)
            {
                return OutboxEventHandlerResult.Failure("Invalid event data format", false);
            }

            _logger.LogInformation("Workflow instance {InstanceId} completed with status {Status}. Duration: {Duration}", 
                eventData.InstanceId, eventData.Status, eventData.Duration);

            return OutboxEventHandlerResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling workflow instance completed event {EventId}", outboxEvent.Id);
            return OutboxEventHandlerResult.Failure($"Handler error: {ex.Message}", true, null, ex.StackTrace);
        }
    }
}

public class WorkflowStepCompletedEventHandler : IOutboxEventHandler
{
    private readonly ILogger<WorkflowStepCompletedEventHandler> _logger;

    public string EventType => "WorkflowStepCompleted";

    public WorkflowStepCompletedEventHandler(ILogger<WorkflowStepCompletedEventHandler> logger)
    {
        _logger = logger;
    }

    public async Task<bool> CanHandleAsync(OutboxEvent outboxEvent, CancellationToken cancellationToken = default)
    {
        return outboxEvent.EventType == EventType && 
               outboxEvent.WorkflowInstanceId.HasValue && 
               outboxEvent.WorkflowStepId.HasValue;
    }

    public async Task<OutboxEventHandlerResult> HandleAsync(OutboxEvent outboxEvent, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Processing workflow step completed event for instance {InstanceId}, step {StepId}", 
                outboxEvent.WorkflowInstanceId, outboxEvent.WorkflowStepId);

            var eventData = JsonSerializer.Deserialize<WorkflowStepCompletedEventData>(outboxEvent.EventData);
            if (eventData == null)
            {
                return OutboxEventHandlerResult.Failure("Invalid event data format", false);
            }

            _logger.LogDebug("Workflow step {StepId} ({StepName}) completed for instance {InstanceId} with status {Status}", 
                eventData.StepId, eventData.StepName, eventData.InstanceId, eventData.Status);

            return OutboxEventHandlerResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling workflow step completed event {EventId}", outboxEvent.Id);
            return OutboxEventHandlerResult.Failure($"Handler error: {ex.Message}", true, null, ex.StackTrace);
        }
    }
}

// Event data models
public class WorkflowInstanceStartedEventData
{
    public Guid InstanceId { get; set; }
    public Guid WorkflowDefinitionId { get; set; }
    public string? InstanceName { get; set; }
    public string? StartedBy { get; set; }
    public DateTime StartedAt { get; set; }
    public string? InputData { get; set; }
}

public class WorkflowInstanceCompletedEventData
{
    public Guid InstanceId { get; set; }
    public Guid WorkflowDefinitionId { get; set; }
    public string? InstanceName { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime CompletedAt { get; set; }
    public TimeSpan Duration { get; set; }
    public string? OutputData { get; set; }
    public string? ErrorMessage { get; set; }
}

public class WorkflowStepCompletedEventData
{
    public Guid InstanceId { get; set; }
    public Guid StepId { get; set; }
    public string StepName { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime CompletedAt { get; set; }
    public TimeSpan Duration { get; set; }
    public string? OutputData { get; set; }
    public string? ErrorMessage { get; set; }
}
