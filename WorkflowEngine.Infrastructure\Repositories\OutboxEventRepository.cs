using Microsoft.EntityFrameworkCore;
using WorkflowEngine.Application.Interfaces;
using WorkflowEngine.Domain.Entities;
using WorkflowEngine.Infrastructure.Data;

namespace WorkflowEngine.Infrastructure.Repositories;

public class OutboxEventRepository : BaseRepository<OutboxEvent>, IOutboxEventRepository
{
    public OutboxEventRepository(WorkflowDbContext context) : base(context)
    {
    }

    public async Task<IEnumerable<OutboxEvent>> GetPendingEventsAsync(Guid tenantId, int maxEvents = 100, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(e => e.TenantId == tenantId && e.Status == OutboxEventStatus.Pending)
            .OrderBy(e => e.Priority)
            .ThenBy(e => e.CreatedAt)
            .Take(maxEvents)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<OutboxEvent>> GetEventsForRetryAsync(Guid tenantId, DateTime retryBefore, int maxEvents = 100, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(e => e.TenantId == tenantId && 
                       e.Status == OutboxEventStatus.Failed && 
                       e.RetryCount < e.MaxRetries &&
                       e.NextRetryAt.HasValue && 
                       e.NextRetryAt.Value <= retryBefore)
            .OrderBy(e => e.Priority)
            .ThenBy(e => e.NextRetryAt)
            .Take(maxEvents)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<OutboxEvent>> GetEventsByStatusAsync(Guid tenantId, OutboxEventStatus status, int maxEvents = 100, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(e => e.TenantId == tenantId && e.Status == status)
            .OrderByDescending(e => e.CreatedAt)
            .Take(maxEvents)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<OutboxEvent>> GetEventsByCorrelationIdAsync(Guid tenantId, string correlationId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(e => e.TenantId == tenantId && e.CorrelationId == correlationId)
            .OrderBy(e => e.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<OutboxEvent>> GetEventsByWorkflowInstanceAsync(Guid tenantId, Guid workflowInstanceId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(e => e.TenantId == tenantId && e.WorkflowInstanceId == workflowInstanceId)
            .OrderBy(e => e.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<OutboxEvent>> GetEventsByTypeAsync(Guid tenantId, string eventType, int maxEvents = 100, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(e => e.TenantId == tenantId && e.EventType == eventType)
            .OrderByDescending(e => e.CreatedAt)
            .Take(maxEvents)
            .ToListAsync(cancellationToken);
    }

    public async Task<OutboxEvent?> GetByIdAsync(Guid tenantId, Guid eventId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .FirstOrDefaultAsync(e => e.TenantId == tenantId && e.Id == eventId, cancellationToken);
    }

    public async Task<int> GetPendingEventCountAsync(Guid tenantId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .CountAsync(e => e.TenantId == tenantId && e.Status == OutboxEventStatus.Pending, cancellationToken);
    }

    public async Task<int> GetFailedEventCountAsync(Guid tenantId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .CountAsync(e => e.TenantId == tenantId && e.Status == OutboxEventStatus.Failed, cancellationToken);
    }
}
