<?xml version="1.0"?>
<doc>
    <assembly>
        <name>WorkflowEngine.API</name>
    </assembly>
    <members>
        <member name="M:WorkflowEngine.API.Controllers.AuthController.Login(WorkflowEngine.API.Models.Auth.LoginRequest)">
            <summary>
            Authenticate user and return JWT token
            </summary>
            <param name="request">Login credentials</param>
            <returns>Authentication response with <PERSON><PERSON><PERSON> token</returns>
        </member>
        <member name="M:WorkflowEngine.API.Controllers.AuthController.Register(WorkflowEngine.API.Models.Auth.RegisterRequest)">
            <summary>
            Register new user and tenant
            </summary>
            <param name="request">Registration details</param>
            <returns>Authentication response with JW<PERSON> token</returns>
        </member>
        <member name="M:WorkflowEngine.API.Controllers.AuthController.RefreshToken(System.String)">
            <summary>
            Refresh JWT token using refresh token
            </summary>
            <param name="refreshToken">Refresh token</param>
            <returns>New authentication response with JWT token</returns>
        </member>
        <member name="M:WorkflowEngine.API.Controllers.AuthController.RevokeToken(System.String)">
            <summary>
            Revoke refresh token (logout)
            </summary>
            <param name="refreshToken">Refresh token to revoke</param>
            <returns>Success status</returns>
        </member>
        <member name="M:WorkflowEngine.API.Controllers.AuthController.GetCurrentUser">
            <summary>
            Get current user information
            </summary>
            <returns>Current user details</returns>
        </member>
        <member name="M:WorkflowEngine.API.Controllers.TenantController.CreateTenant(WorkflowEngine.API.DTOs.Tenant.CreateTenantRequest)">
            <summary>
            Create a new tenant with admin user
            </summary>
        </member>
        <member name="M:WorkflowEngine.API.Controllers.TenantController.GetTenant(System.Guid)">
            <summary>
            Get tenant by ID
            </summary>
        </member>
        <member name="M:WorkflowEngine.API.Controllers.TenantController.GetTenantBySlug(System.String)">
            <summary>
            Get tenant by slug
            </summary>
        </member>
        <member name="M:WorkflowEngine.API.Controllers.TenantController.GetCurrentTenant">
            <summary>
            Get current tenant information
            </summary>
        </member>
        <member name="M:WorkflowEngine.API.Controllers.TenantController.GetAllTenants">
            <summary>
            Get all tenants (Admin only)
            </summary>
        </member>
        <member name="M:WorkflowEngine.API.Controllers.TenantController.UpdateTenant(System.Guid,WorkflowEngine.API.DTOs.Tenant.UpdateTenantRequest)">
            <summary>
            Update tenant information
            </summary>
        </member>
        <member name="M:WorkflowEngine.API.Controllers.TenantController.DeleteTenant(System.Guid)">
            <summary>
            Delete tenant (Admin only)
            </summary>
        </member>
        <member name="M:WorkflowEngine.API.Controllers.TenantController.ActivateTenant(System.Guid)">
            <summary>
            Activate tenant (Admin only)
            </summary>
        </member>
        <member name="M:WorkflowEngine.API.Controllers.TenantController.DeactivateTenant(System.Guid)">
            <summary>
            Deactivate tenant (Admin only)
            </summary>
        </member>
        <member name="M:WorkflowEngine.API.Controllers.TenantController.GetTenantStats(System.Guid,System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            Get tenant statistics
            </summary>
        </member>
    </members>
</doc>
