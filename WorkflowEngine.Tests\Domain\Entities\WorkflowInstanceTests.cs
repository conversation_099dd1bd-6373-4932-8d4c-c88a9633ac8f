using FluentAssertions;
using WorkflowEngine.Domain.Entities;
using WorkflowEngine.Domain.Enums;

namespace WorkflowEngine.Tests.Domain.Entities;

public class WorkflowInstanceTests
{
    [Fact]
    public void WorkflowInstance_Should_Initialize_With_Default_Values()
    {
        // Act
        var instance = new WorkflowInstance();

        // Assert
        instance.Id.Should().NotBeEmpty();
        instance.TenantId.Should().BeEmpty();
        instance.WorkflowDefinitionId.Should().BeEmpty();
        instance.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        instance.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        instance.Name.Should().BeEmpty();
        instance.Status.Should().Be(WorkflowInstanceStatus.NotStarted);
        instance.CurrentStepId.Should().BeNull();
        instance.InputData.Should().BeNull();
        instance.OutputData.Should().BeNull();
        instance.Variables.Should().BeNull();
        instance.StartedAt.Should().BeNull();
        instance.CompletedAt.Should().BeNull();
        instance.ErrorMessage.Should().BeNull();
        instance.RetryCount.Should().Be(0);
        instance.NextExecutionTime.Should().BeNull();
        instance.ActivityExecutions.Should().NotBeNull().And.BeEmpty();
    }

    [Fact]
    public void WorkflowInstance_Should_Allow_Setting_Properties()
    {
        // Arrange
        var instance = new WorkflowInstance();
        var tenantId = Guid.NewGuid();
        var workflowDefinitionId = Guid.NewGuid();
        var currentStepId = Guid.NewGuid();
        var startedAt = DateTime.UtcNow.AddMinutes(-10);
        var completedAt = DateTime.UtcNow;
        var nextExecutionTime = DateTime.UtcNow.AddHours(1);

        // Act
        instance.TenantId = tenantId;
        instance.WorkflowDefinitionId = workflowDefinitionId;
        instance.Name = "Test Instance";
        instance.Status = WorkflowInstanceStatus.Running;
        instance.CurrentStepId = currentStepId;
        instance.InputData = "input";
        instance.OutputData = "output";
        instance.Variables = "{}";
        instance.StartedAt = startedAt;
        instance.CompletedAt = completedAt;
        instance.ErrorMessage = "error";
        instance.RetryCount = 3;
        instance.NextExecutionTime = nextExecutionTime;

        // Assert
        instance.TenantId.Should().Be(tenantId);
        instance.WorkflowDefinitionId.Should().Be(workflowDefinitionId);
        instance.Name.Should().Be("Test Instance");
        instance.Status.Should().Be(WorkflowInstanceStatus.Running);
        instance.CurrentStepId.Should().Be(currentStepId);
        instance.InputData.Should().Be("input");
        instance.OutputData.Should().Be("output");
        instance.Variables.Should().Be("{}");
        instance.StartedAt.Should().Be(startedAt);
        instance.CompletedAt.Should().Be(completedAt);
        instance.ErrorMessage.Should().Be("error");
        instance.RetryCount.Should().Be(3);
        instance.NextExecutionTime.Should().Be(nextExecutionTime);
    }

    [Theory]
    [InlineData(WorkflowInstanceStatus.NotStarted)]
    [InlineData(WorkflowInstanceStatus.Running)]
    [InlineData(WorkflowInstanceStatus.Suspended)]
    [InlineData(WorkflowInstanceStatus.Completed)]
    [InlineData(WorkflowInstanceStatus.Failed)]
    [InlineData(WorkflowInstanceStatus.Cancelled)]
    [InlineData(WorkflowInstanceStatus.TimedOut)]
    public void WorkflowInstance_Should_Accept_All_Status_Values(WorkflowInstanceStatus status)
    {
        // Arrange
        var instance = new WorkflowInstance();

        // Act
        instance.Status = status;

        // Assert
        instance.Status.Should().Be(status);
    }

    [Theory]
    [InlineData(0)]
    [InlineData(1)]
    [InlineData(5)]
    [InlineData(10)]
    public void WorkflowInstance_Should_Accept_Valid_Retry_Counts(int retryCount)
    {
        // Arrange
        var instance = new WorkflowInstance();

        // Act
        instance.RetryCount = retryCount;

        // Assert
        instance.RetryCount.Should().Be(retryCount);
    }

    [Fact]
    public void WorkflowInstance_Should_Allow_Null_Optional_Properties()
    {
        // Arrange
        var instance = new WorkflowInstance();

        // Act & Assert
        instance.CurrentStepId = null;
        instance.InputData = null;
        instance.OutputData = null;
        instance.Variables = null;
        instance.StartedAt = null;
        instance.CompletedAt = null;
        instance.ErrorMessage = null;
        instance.NextExecutionTime = null;

        instance.CurrentStepId.Should().BeNull();
        instance.InputData.Should().BeNull();
        instance.OutputData.Should().BeNull();
        instance.Variables.Should().BeNull();
        instance.StartedAt.Should().BeNull();
        instance.CompletedAt.Should().BeNull();
        instance.ErrorMessage.Should().BeNull();
        instance.NextExecutionTime.Should().BeNull();
    }
}
