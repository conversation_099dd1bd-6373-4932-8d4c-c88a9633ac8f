using System.ComponentModel.DataAnnotations;

namespace WorkflowEngine.API.DTOs.Tenant;

public class UpdateTenantRequest
{
    [Required]
    [StringLength(200, MinimumLength = 2)]
    public string Name { get; set; } = string.Empty;

    [StringLength(1000)]
    public string? Description { get; set; }

    public bool IsActive { get; set; } = true;

    public Dictionary<string, object>? Settings { get; set; }
}
