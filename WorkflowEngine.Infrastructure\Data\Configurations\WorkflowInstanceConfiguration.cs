using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WorkflowEngine.Domain.Entities;
using WorkflowEngine.Domain.Enums;

namespace WorkflowEngine.Infrastructure.Data.Configurations;

public class WorkflowInstanceConfiguration : IEntityTypeConfiguration<WorkflowInstance>
{
    public void Configure(EntityTypeBuilder<WorkflowInstance> builder)
    {
        builder.ToTable("WorkflowInstances");

        builder.HasKey(wi => wi.Id);

        builder.Property(wi => wi.Name)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(wi => wi.Status)
            .HasConversion<int>()
            .HasDefaultValue(WorkflowInstanceStatus.NotStarted);

        builder.Property(wi => wi.InputData)
            .HasColumnType("jsonb");

        builder.Property(wi => wi.OutputData)
            .HasColumnType("jsonb");

        builder.Property(wi => wi.Variables)
            .HasColumnType("jsonb");

        builder.Property(wi => wi.ErrorMessage)
            .HasMaxLength(2000);

        builder.HasIndex(wi => wi.TenantId);
        builder.HasIndex(wi => wi.WorkflowDefinitionId);
        builder.HasIndex(wi => wi.Status);
        builder.HasIndex(wi => wi.NextExecutionTime);

        // Navigation properties
        builder.HasOne(wi => wi.Tenant)
            .WithMany(t => t.WorkflowInstances)
            .HasForeignKey(wi => wi.TenantId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(wi => wi.WorkflowDefinition)
            .WithMany(wd => wd.Instances)
            .HasForeignKey(wi => wi.WorkflowDefinitionId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(wi => wi.CurrentStep)
            .WithMany()
            .HasForeignKey(wi => wi.CurrentStepId)
            .OnDelete(DeleteBehavior.SetNull);

        builder.HasMany(wi => wi.ActivityExecutions)
            .WithOne(ae => ae.WorkflowInstance)
            .HasForeignKey(ae => ae.WorkflowInstanceId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
