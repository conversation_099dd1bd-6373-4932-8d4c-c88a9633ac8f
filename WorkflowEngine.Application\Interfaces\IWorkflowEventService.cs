using WorkflowEngine.Domain.Entities;

namespace WorkflowEngine.Application.Interfaces;

public interface IWorkflowEventService
{
    // Event Publishing
    Task PublishEventAsync(WorkflowEvent workflowEvent, CancellationToken cancellationToken = default);
    Task PublishInstanceEventAsync(Guid instanceId, string eventName, string? eventData = null, CancellationToken cancellationToken = default);
    Task PublishStepEventAsync(Guid instanceId, Guid stepId, string eventName, string? eventData = null, CancellationToken cancellationToken = default);
    
    // Event Subscription
    Task SubscribeToEventAsync(string eventName, Func<WorkflowEvent, Task> handler, CancellationToken cancellationToken = default);
    Task UnsubscribeFromEventAsync(string eventName, Func<WorkflowEvent, Task> handler, CancellationToken cancellationToken = default);
    
    // Event Processing
    Task<bool> ProcessEventAsync(WorkflowEvent workflowEvent, CancellationToken cancellationToken = default);
    Task ProcessPendingEventsAsync(CancellationToken cancellationToken = default);
    
    // Event History
    Task<IEnumerable<WorkflowEvent>> GetInstanceEventsAsync(Guid instanceId, CancellationToken cancellationToken = default);
    Task<IEnumerable<WorkflowEvent>> GetEventsAsync(string? eventName = null, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default);
}

public class WorkflowEvent
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public Guid TenantId { get; set; }
    public string EventName { get; set; } = string.Empty;
    public string? EventData { get; set; }
    public Guid? InstanceId { get; set; }
    public Guid? StepId { get; set; }
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public string? Source { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
    public bool IsProcessed { get; set; }
    public DateTime? ProcessedAt { get; set; }
    public string? ProcessingError { get; set; }
}
