using WorkflowEngine.Domain.Entities;
using WorkflowEngine.Domain.Enums;

namespace WorkflowEngine.Application.Interfaces;

public interface IDeadLetterQueueService
{
    Task<DeadLetterQueue> AddToDeadLetterQueueAsync(ActivityExecution execution, DeadLetterReason reason, string? errorMessage = null, CancellationToken cancellationToken = default);
    Task<DeadLetterQueue> AddToDeadLetterQueueAsync(WorkflowInstance instance, DeadLetterReason reason, string? errorMessage = null, CancellationToken cancellationToken = default);
    Task<IEnumerable<DeadLetterQueue>> GetPendingItemsAsync(int maxItems = 100, CancellationToken cancellationToken = default);
    Task<IEnumerable<DeadLetterQueue>> GetItemsByStatusAsync(DeadLetterStatus status, int maxItems = 100, CancellationToken cancellationToken = default);
    Task<DeadLetterQueue?> GetItemAsync(Guid id, CancellationToken cancellationToken = default);
    Task<bool> RequeueItemAsync(Guid id, string? notes = null, CancellationToken cancellationToken = default);
    Task<bool> ResolveItemAsync(Guid id, string resolution, string? notes = null, CancellationToken cancellationToken = default);
    Task<bool> IgnoreItemAsync(Guid id, string? notes = null, CancellationToken cancellationToken = default);
    Task<DeadLetterQueueStats> GetStatsAsync(CancellationToken cancellationToken = default);
    Task<bool> PurgeOldItemsAsync(TimeSpan olderThan, CancellationToken cancellationToken = default);
}

public class DeadLetterQueueStats
{
    public int PendingCount { get; set; }
    public int InvestigationRequiredCount { get; set; }
    public int ResolvedCount { get; set; }
    public int IgnoredCount { get; set; }
    public int TotalCount { get; set; }
    public DateTime? OldestItemDate { get; set; }
    public Dictionary<DeadLetterReason, int> ReasonCounts { get; set; } = new();
}
