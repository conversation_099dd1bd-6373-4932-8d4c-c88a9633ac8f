using WorkflowEngine.Domain.Entities;

namespace WorkflowEngine.Application.Interfaces;

public interface IOutboxEventRepository : IRepository<OutboxEvent>
{
    Task<IEnumerable<OutboxEvent>> GetPendingEventsAsync(Guid tenantId, int maxEvents = 100, CancellationToken cancellationToken = default);
    Task<IEnumerable<OutboxEvent>> GetEventsForRetryAsync(Guid tenantId, DateTime retryBefore, int maxEvents = 100, CancellationToken cancellationToken = default);
    Task<IEnumerable<OutboxEvent>> GetEventsByStatusAsync(Guid tenantId, OutboxEventStatus status, int maxEvents = 100, CancellationToken cancellationToken = default);
    Task<IEnumerable<OutboxEvent>> GetEventsByCorrelationIdAsync(Guid tenantId, string correlationId, CancellationToken cancellationToken = default);
    Task<IEnumerable<OutboxEvent>> GetEventsByWorkflowInstanceAsync(Guid tenantId, Guid workflowInstanceId, CancellationToken cancellationToken = default);
    Task<IEnumerable<OutboxEvent>> GetEventsByTypeAsync(Guid tenantId, string eventType, int maxEvents = 100, CancellationToken cancellationToken = default);
    Task<OutboxEvent?> GetByIdAsync(Guid tenantId, Guid eventId, CancellationToken cancellationToken = default);
    Task<int> GetPendingEventCountAsync(Guid tenantId, CancellationToken cancellationToken = default);
    Task<int> GetFailedEventCountAsync(Guid tenantId, CancellationToken cancellationToken = default);
}
