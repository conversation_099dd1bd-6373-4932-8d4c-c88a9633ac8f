using Microsoft.Extensions.Logging;
using System.Text.Json;
using WorkflowEngine.Application.Interfaces;
using WorkflowEngine.Domain.Entities;
using WorkflowEngine.Domain.Enums;
using WorkflowEngine.Infrastructure.Data;

namespace WorkflowEngine.Infrastructure.Services;

public class RetryPolicyService : IRetryPolicyService
{
    private readonly IActivityRepository _activityRepository;
    private readonly ITenantContext _tenantContext;
    private readonly ILogger<RetryPolicyService> _logger;

    // Default retry policy
    private static readonly RetryPolicy DefaultPolicy = new()
    {
        Name = "Default",
        MaxRetries = 3,
        InitialDelay = TimeSpan.FromMinutes(1),
        MaxDelay = TimeSpan.FromHours(1),
        BackoffStrategy = RetryBackoffStrategy.Exponential,
        BackoffMultiplier = 2.0,
        RetryOnTimeout = true,
        RetryOnHttpError = true,
        RetryOnSystemError = false
    };

    public RetryPolicyService(
        IActivityRepository activityRepository,
        ITenantContext tenantContext,
        ILogger<RetryPolicyService> logger)
    {
        _activityRepository = activityRepository;
        _tenantContext = tenantContext;
        _logger = logger;
    }

    public async Task<RetryPolicy?> GetRetryPolicyAsync(Guid activityId, CancellationToken cancellationToken = default)
    {
        try
        {
            var activity = await _activityRepository.GetByIdAsync(_tenantContext.TenantId, activityId, cancellationToken);
            if (activity?.Configuration == null)
            {
                return DefaultPolicy;
            }

            // Try to extract retry policy from activity configuration
            var config = JsonSerializer.Deserialize<Dictionary<string, object>>(activity.Configuration);
            if (config?.TryGetValue("retryPolicy", out var retryPolicyObj) == true)
            {
                if (retryPolicyObj is JsonElement jsonElement)
                {
                    return JsonSerializer.Deserialize<RetryPolicy>(jsonElement.GetRawText()) ?? DefaultPolicy;
                }
            }

            return DefaultPolicy;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting retry policy for activity {ActivityId}", activityId);
            return DefaultPolicy;
        }
    }

    public async Task<RetryPolicy?> GetRetryPolicyAsync(string policyName, CancellationToken cancellationToken = default)
    {
        // For now, return default policy. In a full implementation, this would query a RetryPolicy repository
        return DefaultPolicy;
    }

    public async Task<RetryDecision> ShouldRetryAsync(ActivityExecution execution, Exception? exception = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var policy = await GetRetryPolicyAsync(execution.ActivityId, cancellationToken);
            if (policy == null)
            {
                return RetryDecision.NoRetry("No retry policy found");
            }

            // Check if max retries exceeded
            if (execution.RetryCount >= policy.MaxRetries)
            {
                return RetryDecision.DeadLetter($"Max retries ({policy.MaxRetries}) exceeded");
            }

            // Check if exception is retryable
            if (exception != null && !await IsRetryableExceptionAsync(exception, policy, cancellationToken))
            {
                return RetryDecision.DeadLetter($"Non-retryable exception: {exception.GetType().Name}");
            }

            // Calculate next retry time
            var nextRetryAt = await CalculateNextRetryTimeAsync(execution, policy, cancellationToken);
            var delay = nextRetryAt - DateTime.UtcNow;

            _logger.LogInformation("Scheduling retry for execution {ExecutionId} at {NextRetryAt} (attempt {RetryCount}/{MaxRetries})",
                execution.Id, nextRetryAt, execution.RetryCount + 1, policy.MaxRetries);

            return RetryDecision.Retry(nextRetryAt, delay, $"Retry attempt {execution.RetryCount + 1}/{policy.MaxRetries}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error determining retry decision for execution {ExecutionId}", execution.Id);
            return RetryDecision.NoRetry($"Error in retry decision: {ex.Message}");
        }
    }

    public async Task<DateTime> CalculateNextRetryTimeAsync(ActivityExecution execution, RetryPolicy policy, CancellationToken cancellationToken = default)
    {
        var baseDelay = policy.InitialDelay;
        var retryCount = execution.RetryCount;

        var delay = policy.BackoffStrategy switch
        {
            RetryBackoffStrategy.Fixed => baseDelay,
            RetryBackoffStrategy.Linear => TimeSpan.FromMilliseconds(baseDelay.TotalMilliseconds * (retryCount + 1)),
            RetryBackoffStrategy.Exponential => TimeSpan.FromMilliseconds(baseDelay.TotalMilliseconds * Math.Pow(policy.BackoffMultiplier, retryCount)),
            RetryBackoffStrategy.ExponentialWithJitter => CalculateExponentialWithJitter(baseDelay, retryCount, policy.BackoffMultiplier),
            _ => baseDelay
        };

        // Cap the delay at max delay
        if (delay > policy.MaxDelay)
        {
            delay = policy.MaxDelay;
        }

        return DateTime.UtcNow.Add(delay);
    }

    public async Task<bool> IsRetryableExceptionAsync(Exception exception, RetryPolicy policy, CancellationToken cancellationToken = default)
    {
        var exceptionType = exception.GetType().Name;

        // Check non-retryable exceptions first
        if (!string.IsNullOrEmpty(policy.NonRetryableExceptions))
        {
            try
            {
                var nonRetryableTypes = JsonSerializer.Deserialize<string[]>(policy.NonRetryableExceptions);
                if (nonRetryableTypes?.Contains(exceptionType) == true)
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error parsing non-retryable exceptions for policy {PolicyName}", policy.Name);
            }
        }

        // Check retryable exceptions
        if (!string.IsNullOrEmpty(policy.RetryableExceptions))
        {
            try
            {
                var retryableTypes = JsonSerializer.Deserialize<string[]>(policy.RetryableExceptions);
                if (retryableTypes?.Contains(exceptionType) == true)
                {
                    return true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error parsing retryable exceptions for policy {PolicyName}", policy.Name);
            }
        }

        // Default behavior based on exception type
        return exception switch
        {
            TimeoutException => policy.RetryOnTimeout,
            HttpRequestException => policy.RetryOnHttpError,
            TaskCanceledException => policy.RetryOnTimeout,
            OperationCanceledException => false, // Don't retry cancellation
            ArgumentException => false, // Don't retry argument errors
            InvalidOperationException => false, // Don't retry invalid operations
            _ => policy.RetryOnSystemError
        };
    }

    public async Task<RetryPolicy> CreateRetryPolicyAsync(string name, int maxRetries, TimeSpan initialDelay, CancellationToken cancellationToken = default)
    {
        return new RetryPolicy
        {
            TenantId = _tenantContext.TenantId,
            Name = name,
            MaxRetries = maxRetries,
            InitialDelay = initialDelay,
            MaxDelay = TimeSpan.FromHours(1),
            BackoffStrategy = RetryBackoffStrategy.Exponential,
            BackoffMultiplier = 2.0,
            RetryOnTimeout = true,
            RetryOnHttpError = true,
            RetryOnSystemError = false,
            IsActive = true
        };
    }

    public async Task UpdateRetryPolicyAsync(RetryPolicy policy, CancellationToken cancellationToken = default)
    {
        // In a full implementation, this would update the policy in the database
        _logger.LogInformation("Updated retry policy {PolicyName}", policy.Name);
    }

    private static TimeSpan CalculateExponentialWithJitter(TimeSpan baseDelay, int retryCount, double multiplier)
    {
        var exponentialDelay = baseDelay.TotalMilliseconds * Math.Pow(multiplier, retryCount);
        
        // Add jitter (±25% random variation)
        var random = new Random();
        var jitter = 0.75 + (random.NextDouble() * 0.5); // 0.75 to 1.25
        
        return TimeSpan.FromMilliseconds(exponentialDelay * jitter);
    }
}
