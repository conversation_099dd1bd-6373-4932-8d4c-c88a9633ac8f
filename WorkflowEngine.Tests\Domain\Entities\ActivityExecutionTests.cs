using FluentAssertions;
using WorkflowEngine.Domain.Entities;
using WorkflowEngine.Domain.Enums;

namespace WorkflowEngine.Tests.Domain.Entities;

public class ActivityExecutionTests
{
    [Fact]
    public void ActivityExecution_Should_Initialize_With_Default_Values()
    {
        // Act
        var execution = new ActivityExecution();

        // Assert
        execution.Id.Should().NotBeEmpty();
        execution.TenantId.Should().BeEmpty();
        execution.WorkflowInstanceId.Should().BeEmpty();
        execution.WorkflowStepId.Should().BeEmpty();
        execution.ActivityId.Should().BeEmpty();
        execution.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        execution.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        execution.Status.Should().Be(ExecutionStatus.Pending);
        execution.InputData.Should().BeNull();
        execution.OutputData.Should().BeNull();
        execution.ErrorMessage.Should().BeNull();
        execution.StackTrace.Should().BeNull();
        execution.StartedAt.Should().BeNull();
        execution.CompletedAt.Should().BeNull();
        execution.RetryCount.Should().Be(0);
        execution.MaxRetries.Should().Be(3);
        execution.NextRetryAt.Should().BeNull();
        execution.Duration.Should().BeNull();
    }

    [Fact]
    public void ActivityExecution_Should_Allow_Setting_Properties()
    {
        // Arrange
        var execution = new ActivityExecution();
        var tenantId = Guid.NewGuid();
        var workflowInstanceId = Guid.NewGuid();
        var workflowStepId = Guid.NewGuid();
        var activityId = Guid.NewGuid();
        var startedAt = DateTime.UtcNow.AddMinutes(-5);
        var completedAt = DateTime.UtcNow;
        var nextRetryAt = DateTime.UtcNow.AddMinutes(10);
        var duration = TimeSpan.FromMinutes(5);

        // Act
        execution.TenantId = tenantId;
        execution.WorkflowInstanceId = workflowInstanceId;
        execution.WorkflowStepId = workflowStepId;
        execution.ActivityId = activityId;
        execution.Status = ExecutionStatus.Completed;
        execution.InputData = "input";
        execution.OutputData = "output";
        execution.ErrorMessage = "error";
        execution.StackTrace = "stack trace";
        execution.StartedAt = startedAt;
        execution.CompletedAt = completedAt;
        execution.RetryCount = 2;
        execution.MaxRetries = 5;
        execution.NextRetryAt = nextRetryAt;
        execution.Duration = duration;

        // Assert
        execution.TenantId.Should().Be(tenantId);
        execution.WorkflowInstanceId.Should().Be(workflowInstanceId);
        execution.WorkflowStepId.Should().Be(workflowStepId);
        execution.ActivityId.Should().Be(activityId);
        execution.Status.Should().Be(ExecutionStatus.Completed);
        execution.InputData.Should().Be("input");
        execution.OutputData.Should().Be("output");
        execution.ErrorMessage.Should().Be("error");
        execution.StackTrace.Should().Be("stack trace");
        execution.StartedAt.Should().Be(startedAt);
        execution.CompletedAt.Should().Be(completedAt);
        execution.RetryCount.Should().Be(2);
        execution.MaxRetries.Should().Be(5);
        execution.NextRetryAt.Should().Be(nextRetryAt);
        execution.Duration.Should().Be(duration);
    }

    [Theory]
    [InlineData(ExecutionStatus.Pending)]
    [InlineData(ExecutionStatus.Running)]
    [InlineData(ExecutionStatus.Completed)]
    [InlineData(ExecutionStatus.Failed)]
    [InlineData(ExecutionStatus.Cancelled)]
    [InlineData(ExecutionStatus.Skipped)]
    [InlineData(ExecutionStatus.Retrying)]
    [InlineData(ExecutionStatus.TimedOut)]
    public void ActivityExecution_Should_Accept_All_Execution_Status_Values(ExecutionStatus status)
    {
        // Arrange
        var execution = new ActivityExecution();

        // Act
        execution.Status = status;

        // Assert
        execution.Status.Should().Be(status);
    }

    [Theory]
    [InlineData(0, 0)]
    [InlineData(1, 3)]
    [InlineData(3, 3)]
    [InlineData(5, 10)]
    public void ActivityExecution_Should_Accept_Valid_Retry_Configurations(int retryCount, int maxRetries)
    {
        // Arrange
        var execution = new ActivityExecution();

        // Act
        execution.RetryCount = retryCount;
        execution.MaxRetries = maxRetries;

        // Assert
        execution.RetryCount.Should().Be(retryCount);
        execution.MaxRetries.Should().Be(maxRetries);
    }

    [Fact]
    public void ActivityExecution_Should_Calculate_Duration_Correctly()
    {
        // Arrange
        var execution = new ActivityExecution();
        var startTime = DateTime.UtcNow;
        var endTime = startTime.AddMinutes(10);
        var expectedDuration = TimeSpan.FromMinutes(10);

        // Act
        execution.StartedAt = startTime;
        execution.CompletedAt = endTime;
        execution.Duration = expectedDuration;

        // Assert
        execution.Duration.Should().Be(expectedDuration);
    }

    [Fact]
    public void ActivityExecution_Should_Allow_Null_Optional_Properties()
    {
        // Arrange
        var execution = new ActivityExecution();

        // Act & Assert
        execution.InputData = null;
        execution.OutputData = null;
        execution.ErrorMessage = null;
        execution.StackTrace = null;
        execution.StartedAt = null;
        execution.CompletedAt = null;
        execution.NextRetryAt = null;
        execution.Duration = null;

        execution.InputData.Should().BeNull();
        execution.OutputData.Should().BeNull();
        execution.ErrorMessage.Should().BeNull();
        execution.StackTrace.Should().BeNull();
        execution.StartedAt.Should().BeNull();
        execution.CompletedAt.Should().BeNull();
        execution.NextRetryAt.Should().BeNull();
        execution.Duration.Should().BeNull();
    }
}
