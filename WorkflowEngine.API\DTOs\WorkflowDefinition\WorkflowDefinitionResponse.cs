namespace WorkflowEngine.API.DTOs.WorkflowDefinition;

public class WorkflowDefinitionResponse
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int Version { get; set; }
    public bool IsActive { get; set; }
    public Dictionary<string, object>? Configuration { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public string? CreatedBy { get; set; }
    public string? UpdatedBy { get; set; }
    public List<WorkflowStepResponse> Steps { get; set; } = new();
    public WorkflowDefinitionStatsResponse? Stats { get; set; }
}

public class WorkflowDefinitionListResponse
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int Version { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public string? CreatedBy { get; set; }
    public string? UpdatedBy { get; set; }
    public int StepCount { get; set; }
    public int InstanceCount { get; set; }
    public int ActiveInstanceCount { get; set; }
}

public class WorkflowStepResponse
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public int Order { get; set; }
    public Dictionary<string, object>? Configuration { get; set; }
    public List<string> NextStepNames { get; set; } = new();
    public string? FailureStepName { get; set; }
    public Guid? ActivityId { get; set; }
    public string? ActivityName { get; set; }
    public string? ActivityType { get; set; }
    public Dictionary<string, object>? ActivityConfiguration { get; set; }
    public List<DecisionConditionResponse> Conditions { get; set; } = new();
}

public class DecisionConditionResponse
{
    public Guid Id { get; set; }
    public string Field { get; set; } = string.Empty;
    public string Operator { get; set; } = string.Empty;
    public object? Value { get; set; }
    public string NextStepName { get; set; } = string.Empty;
}

public class WorkflowDefinitionStatsResponse
{
    public int TotalInstances { get; set; }
    public int ActiveInstances { get; set; }
    public int CompletedInstances { get; set; }
    public int FailedInstances { get; set; }
    public int CancelledInstances { get; set; }
    public double AverageExecutionTimeMinutes { get; set; }
    public double SuccessRate { get; set; }
    public DateTime? LastExecuted { get; set; }
    public DateTime? LastCompleted { get; set; }
}
