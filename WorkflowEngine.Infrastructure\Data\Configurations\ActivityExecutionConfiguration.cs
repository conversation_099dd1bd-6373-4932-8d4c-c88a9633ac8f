using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WorkflowEngine.Domain.Entities;
using WorkflowEngine.Domain.Enums;

namespace WorkflowEngine.Infrastructure.Data.Configurations;

public class ActivityExecutionConfiguration : IEntityTypeConfiguration<ActivityExecution>
{
    public void Configure(EntityTypeBuilder<ActivityExecution> builder)
    {
        builder.ToTable("ActivityExecutions");

        builder.HasKey(ae => ae.Id);

        builder.Property(ae => ae.Status)
            .HasConversion<int>()
            .HasDefaultValue(ExecutionStatus.Pending);

        builder.Property(ae => ae.InputData)
            .HasColumnType("jsonb");

        builder.Property(ae => ae.OutputData)
            .HasColumnType("jsonb");

        builder.Property(ae => ae.ErrorMessage)
            .HasMaxLength(2000);

        builder.Property(ae => ae.StackTrace)
            .HasMaxLength(4000);

        builder.HasIndex(ae => ae.TenantId);
        builder.HasIndex(ae => ae.WorkflowInstanceId);
        builder.HasIndex(ae => ae.WorkflowStepId);
        builder.HasIndex(ae => ae.ActivityId);
        builder.HasIndex(ae => ae.Status);
        builder.HasIndex(ae => ae.NextRetryAt);

        // Navigation properties
        builder.HasOne(ae => ae.Tenant)
            .WithMany()
            .HasForeignKey(ae => ae.TenantId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(ae => ae.WorkflowInstance)
            .WithMany(wi => wi.ActivityExecutions)
            .HasForeignKey(ae => ae.WorkflowInstanceId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(ae => ae.WorkflowStep)
            .WithMany(ws => ws.ActivityExecutions)
            .HasForeignKey(ae => ae.WorkflowStepId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(ae => ae.Activity)
            .WithMany(a => a.ActivityExecutions)
            .HasForeignKey(ae => ae.ActivityId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
