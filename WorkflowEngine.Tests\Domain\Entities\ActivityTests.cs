using FluentAssertions;
using WorkflowEngine.Domain.Entities;
using WorkflowEngine.Domain.Enums;

namespace WorkflowEngine.Tests.Domain.Entities;

public class ActivityTests
{
    [Fact]
    public void Activity_Should_Initialize_With_Default_Values()
    {
        // Act
        var activity = new Activity();

        // Assert
        activity.Id.Should().NotBeEmpty();
        activity.TenantId.Should().BeEmpty();
        activity.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        activity.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        activity.Name.Should().BeEmpty();
        activity.Description.Should().BeNull();
        activity.Type.Should().Be(ActivityType.HttpRequest);
        activity.Configuration.Should().BeNull();
        activity.InputSchema.Should().BeNull();
        activity.OutputSchema.Should().BeNull();
        activity.IsActive.Should().BeTrue();
        activity.Version.Should().Be("1.0.0");
        activity.AssemblyName.Should().BeNull();
        activity.ClassName.Should().BeNull();
        activity.WorkflowSteps.Should().NotBeNull().And.BeEmpty();
        activity.ActivityExecutions.Should().NotBeNull().And.BeEmpty();
    }

    [Fact]
    public void Activity_Should_Allow_Setting_Properties()
    {
        // Arrange
        var activity = new Activity();
        var tenantId = Guid.NewGuid();

        // Act
        activity.TenantId = tenantId;
        activity.Name = "Test Activity";
        activity.Description = "Test Description";
        activity.Type = ActivityType.Script;
        activity.Configuration = "{}";
        activity.InputSchema = "input-schema";
        activity.OutputSchema = "output-schema";
        activity.IsActive = false;
        activity.Version = "2.0.0";
        activity.AssemblyName = "TestAssembly";
        activity.ClassName = "TestClass";

        // Assert
        activity.TenantId.Should().Be(tenantId);
        activity.Name.Should().Be("Test Activity");
        activity.Description.Should().Be("Test Description");
        activity.Type.Should().Be(ActivityType.Script);
        activity.Configuration.Should().Be("{}");
        activity.InputSchema.Should().Be("input-schema");
        activity.OutputSchema.Should().Be("output-schema");
        activity.IsActive.Should().BeFalse();
        activity.Version.Should().Be("2.0.0");
        activity.AssemblyName.Should().Be("TestAssembly");
        activity.ClassName.Should().Be("TestClass");
    }

    [Theory]
    [InlineData(ActivityType.HttpRequest)]
    [InlineData(ActivityType.Timer)]
    [InlineData(ActivityType.Script)]
    [InlineData(ActivityType.Decision)]
    [InlineData(ActivityType.Email)]
    [InlineData(ActivityType.Database)]
    [InlineData(ActivityType.FileOperation)]
    [InlineData(ActivityType.Custom)]
    [InlineData(ActivityType.HumanTask)]
    [InlineData(ActivityType.SubWorkflow)]
    public void Activity_Should_Accept_All_Activity_Types(ActivityType activityType)
    {
        // Arrange
        var activity = new Activity();

        // Act
        activity.Type = activityType;

        // Assert
        activity.Type.Should().Be(activityType);
    }

    [Theory]
    [InlineData("1.0.0")]
    [InlineData("2.1.3")]
    [InlineData("10.0.0-beta")]
    [InlineData("1.0.0-alpha.1")]
    public void Activity_Should_Accept_Various_Version_Formats(string version)
    {
        // Arrange
        var activity = new Activity();

        // Act
        activity.Version = version;

        // Assert
        activity.Version.Should().Be(version);
    }

    [Fact]
    public void Activity_Should_Maintain_Separate_Collections()
    {
        // Arrange
        var activity1 = new Activity();
        var activity2 = new Activity();

        // Act
        activity1.WorkflowSteps.Add(new WorkflowStep { Name = "Step1" });
        activity2.ActivityExecutions.Add(new ActivityExecution());

        // Assert
        activity1.WorkflowSteps.Should().HaveCount(1);
        activity1.ActivityExecutions.Should().BeEmpty();
        activity2.WorkflowSteps.Should().BeEmpty();
        activity2.ActivityExecutions.Should().HaveCount(1);
    }

    [Fact]
    public void Activity_Should_Allow_Null_Optional_Properties()
    {
        // Arrange
        var activity = new Activity();

        // Act & Assert
        activity.Description = null;
        activity.Configuration = null;
        activity.InputSchema = null;
        activity.OutputSchema = null;
        activity.AssemblyName = null;
        activity.ClassName = null;

        activity.Description.Should().BeNull();
        activity.Configuration.Should().BeNull();
        activity.InputSchema.Should().BeNull();
        activity.OutputSchema.Should().BeNull();
        activity.AssemblyName.Should().BeNull();
        activity.ClassName.Should().BeNull();
    }
}
