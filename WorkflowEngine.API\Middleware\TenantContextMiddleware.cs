using System.Security.Claims;
using WorkflowEngine.API.Services;
using WorkflowEngine.Infrastructure.Services;

namespace WorkflowEngine.API.Middleware;

public class TenantContextMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<TenantContextMiddleware> _logger;

    public TenantContextMiddleware(RequestDelegate next, ILogger<TenantContextMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context, ITenantContext tenantContext)
    {
        try
        {
            // Extract tenant information from JWT claims
            if (context.User.Identity?.IsAuthenticated == true)
            {
                var tenantIdClaim = context.User.FindFirst(JwtClaims.TenantId);
                var userIdClaim = context.User.FindFirst(JwtClaims.UserId);

                if (tenantIdClaim != null && Guid.TryParse(tenantIdClaim.Value, out var tenantId))
                {
                    tenantContext.TenantId = tenantId;
                    
                    if (userIdClaim != null && Guid.TryParse(userIdClaim.Value, out var userId))
                    {
                        tenantContext.UserId = userId;
                    }

                    _logger.LogDebug("Tenant context set: TenantId={TenantId}, UserId={UserId}", 
                        tenantId, tenantContext.UserId);
                }
                else
                {
                    _logger.LogWarning("No valid tenant ID found in JWT claims for authenticated user");
                }
            }
            else
            {
                // For unauthenticated requests, try to get tenant from header or query parameter
                var tenantHeader = context.Request.Headers["X-Tenant-Id"].FirstOrDefault();
                var tenantQuery = context.Request.Query["tenantId"].FirstOrDefault();
                
                var tenantIdString = tenantHeader ?? tenantQuery;
                
                if (!string.IsNullOrEmpty(tenantIdString) && Guid.TryParse(tenantIdString, out var tenantId))
                {
                    tenantContext.TenantId = tenantId;
                    _logger.LogDebug("Tenant context set from header/query: TenantId={TenantId}", tenantId);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting tenant context");
        }

        await _next(context);
    }
}
