using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WorkflowEngine.Domain.Entities;

namespace WorkflowEngine.Infrastructure.Data.Configurations;

public class DeadLetterQueueConfiguration : IEntityTypeConfiguration<DeadLetterQueue>
{
    public void Configure(EntityTypeBuilder<DeadLetterQueue> builder)
    {
        builder.ToTable("DeadLetterQueue");

        builder.HasKey(dlq => dlq.Id);

        builder.Property(dlq => dlq.Reason)
            .IsRequired()
            .HasConversion<int>();

        builder.Property(dlq => dlq.Status)
            .IsRequired()
            .HasConversion<int>();

        builder.Property(dlq => dlq.ErrorMessage)
            .HasMaxLength(2000);

        builder.Property(dlq => dlq.StackTrace)
            .HasColumnType("text");

        builder.Property(dlq => dlq.InputData)
            .HasColumnType("jsonb");

        builder.Property(dlq => dlq.Configuration)
            .HasColumnType("jsonb");

        builder.Property(dlq => dlq.Resolution)
            .HasMaxLength(1000);

        builder.Property(dlq => dlq.Notes)
            .HasMaxLength(2000);

        builder.Property(dlq => dlq.ProcessedBy)
            .HasMaxLength(100);

        // Indexes
        builder.HasIndex(dlq => new { dlq.TenantId, dlq.Status });
        builder.HasIndex(dlq => new { dlq.TenantId, dlq.WorkflowInstanceId });
        builder.HasIndex(dlq => new { dlq.TenantId, dlq.Reason });
        builder.HasIndex(dlq => dlq.CreatedAt);

        // Relationships
        builder.HasOne(dlq => dlq.Tenant)
            .WithMany()
            .HasForeignKey(dlq => dlq.TenantId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(dlq => dlq.WorkflowInstance)
            .WithMany()
            .HasForeignKey(dlq => dlq.WorkflowInstanceId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(dlq => dlq.WorkflowStep)
            .WithMany()
            .HasForeignKey(dlq => dlq.WorkflowStepId)
            .OnDelete(DeleteBehavior.SetNull);

        builder.HasOne(dlq => dlq.Activity)
            .WithMany()
            .HasForeignKey(dlq => dlq.ActivityId)
            .OnDelete(DeleteBehavior.SetNull);

        builder.HasOne(dlq => dlq.ActivityExecution)
            .WithMany()
            .HasForeignKey(dlq => dlq.ActivityExecutionId)
            .OnDelete(DeleteBehavior.SetNull);
    }
}
