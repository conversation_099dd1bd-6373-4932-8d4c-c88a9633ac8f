using Microsoft.EntityFrameworkCore;
using WorkflowEngine.Application.Interfaces;
using WorkflowEngine.Domain.Entities;
using WorkflowEngine.Domain.Enums;
using WorkflowEngine.Infrastructure.Data;

namespace WorkflowEngine.Infrastructure.Repositories;

public class DeadLetterQueueRepository : BaseTenantRepository<DeadLetterQueue>, IDeadLetterQueueRepository
{
    public DeadLetterQueueRepository(WorkflowDbContext context) : base(context)
    {
    }

    public async Task<IEnumerable<DeadLetterQueue>> GetByStatusAsync(Guid tenantId, DeadLetterStatus status, int maxItems = 100, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(dlq => dlq.TenantId == tenantId && dlq.Status == status)
            .OrderBy(dlq => dlq.CreatedAt)
            .Take(maxItems)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<DeadLetterQueue>> GetByWorkflowInstanceAsync(Guid tenantId, Guid workflowInstanceId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(dlq => dlq.TenantId == tenantId && dlq.WorkflowInstanceId == workflowInstanceId)
            .OrderByDescending(dlq => dlq.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<DeadLetterQueue>> GetByReasonAsync(Guid tenantId, DeadLetterReason reason, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(dlq => dlq.TenantId == tenantId && dlq.Reason == reason)
            .OrderByDescending(dlq => dlq.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<DeadLetterQueueStats> GetStatsAsync(Guid tenantId, CancellationToken cancellationToken = default)
    {
        var items = await _dbSet
            .Where(dlq => dlq.TenantId == tenantId)
            .ToListAsync(cancellationToken);

        var stats = new DeadLetterQueueStats
        {
            TotalCount = items.Count,
            PendingCount = items.Count(i => i.Status == DeadLetterStatus.Pending),
            InvestigationRequiredCount = items.Count(i => i.Status == DeadLetterStatus.InvestigationRequired),
            ResolvedCount = items.Count(i => i.Status == DeadLetterStatus.Resolved),
            IgnoredCount = items.Count(i => i.Status == DeadLetterStatus.Ignored),
            OldestItemDate = items.Any() ? items.Min(i => i.CreatedAt) : null
        };

        // Calculate reason counts
        stats.ReasonCounts = items
            .GroupBy(i => i.Reason)
            .ToDictionary(g => g.Key, g => g.Count());

        return stats;
    }

    public async Task<int> PurgeOldItemsAsync(Guid tenantId, DateTime olderThan, CancellationToken cancellationToken = default)
    {
        var itemsToDelete = await _dbSet
            .Where(dlq => dlq.TenantId == tenantId && 
                         dlq.CreatedAt < olderThan && 
                         (dlq.Status == DeadLetterStatus.Resolved || dlq.Status == DeadLetterStatus.Ignored))
            .ToListAsync(cancellationToken);

        if (itemsToDelete.Any())
        {
            _dbSet.RemoveRange(itemsToDelete);
            await _context.SaveChangesAsync(cancellationToken);
        }

        return itemsToDelete.Count;
    }

    public async Task<IEnumerable<DeadLetterQueue>> GetOldestItemsAsync(Guid tenantId, int count = 10, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(dlq => dlq.TenantId == tenantId && dlq.Status == DeadLetterStatus.Pending)
            .OrderBy(dlq => dlq.CreatedAt)
            .Take(count)
            .ToListAsync(cancellationToken);
    }
}
