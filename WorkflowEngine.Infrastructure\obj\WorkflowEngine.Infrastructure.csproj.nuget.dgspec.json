{"format": 1, "restore": {"D:\\Projects\\Project-ideas\\crm-new\\WorkflowEngine.Infrastructure\\WorkflowEngine.Infrastructure.csproj": {}}, "projects": {"D:\\Projects\\Project-ideas\\crm-new\\WorkflowEngine.Application\\WorkflowEngine.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Projects\\Project-ideas\\crm-new\\WorkflowEngine.Application\\WorkflowEngine.Application.csproj", "projectName": "WorkflowEngine.Application", "projectPath": "D:\\Projects\\Project-ideas\\crm-new\\WorkflowEngine.Application\\WorkflowEngine.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Projects\\Project-ideas\\crm-new\\WorkflowEngine.Application\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\Projects\\Project-ideas\\crm-new\\WorkflowEngine.Domain\\WorkflowEngine.Domain.csproj": {"projectPath": "D:\\Projects\\Project-ideas\\crm-new\\WorkflowEngine.Domain\\WorkflowEngine.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.Extensions.Hosting.Abstractions": {"target": "Package", "version": "[9.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Projects\\Project-ideas\\crm-new\\WorkflowEngine.Domain\\WorkflowEngine.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Projects\\Project-ideas\\crm-new\\WorkflowEngine.Domain\\WorkflowEngine.Domain.csproj", "projectName": "WorkflowEngine.Domain", "projectPath": "D:\\Projects\\Project-ideas\\crm-new\\WorkflowEngine.Domain\\WorkflowEngine.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Projects\\Project-ideas\\crm-new\\WorkflowEngine.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Projects\\Project-ideas\\crm-new\\WorkflowEngine.Infrastructure\\WorkflowEngine.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Projects\\Project-ideas\\crm-new\\WorkflowEngine.Infrastructure\\WorkflowEngine.Infrastructure.csproj", "projectName": "WorkflowEngine.Infrastructure", "projectPath": "D:\\Projects\\Project-ideas\\crm-new\\WorkflowEngine.Infrastructure\\WorkflowEngine.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Projects\\Project-ideas\\crm-new\\WorkflowEngine.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\Projects\\Project-ideas\\crm-new\\WorkflowEngine.Application\\WorkflowEngine.Application.csproj": {"projectPath": "D:\\Projects\\Project-ideas\\crm-new\\WorkflowEngine.Application\\WorkflowEngine.Application.csproj"}, "D:\\Projects\\Project-ideas\\crm-new\\WorkflowEngine.Domain\\WorkflowEngine.Domain.csproj": {"projectPath": "D:\\Projects\\Project-ideas\\crm-new\\WorkflowEngine.Domain\\WorkflowEngine.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": {"target": "Package", "version": "[2.3.0, )"}, "Microsoft.AspNetCore.Mvc.Core": {"target": "Package", "version": "[2.3.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[9.0.6, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[9.0.2, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[8.12.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}}}