using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using WorkflowEngine.Application.Interfaces;
using WorkflowEngine.Domain.Common;
using WorkflowEngine.Infrastructure.Data;

namespace WorkflowEngine.Infrastructure.Repositories;

public class BaseTenantRepository<T> : BaseRepository<T>, ITenantRepository<T> 
    where T : BaseEntity, ITenantEntity
{
    public BaseTenantRepository(WorkflowDbContext context) : base(context)
    {
    }

    public virtual async Task<T?> GetByIdAsync(Guid tenantId, Guid id, CancellationToken cancellationToken = default)
    {
        return await _dbSet.FirstOrDefaultAsync(x => x.TenantId == tenantId && x.Id == id, cancellationToken);
    }

    public virtual async Task<IEnumerable<T>> GetAllAsync(Guid tenantId, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(x => x.TenantId == tenantId).ToListAsync(cancellationToken);
    }

    public virtual async Task<IEnumerable<T>> FindAsync(Guid tenantId, Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(x => x.TenantId == tenantId).Where(predicate).ToListAsync(cancellationToken);
    }

    public virtual async Task<T?> FirstOrDefaultAsync(Guid tenantId, Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(x => x.TenantId == tenantId).FirstOrDefaultAsync(predicate, cancellationToken);
    }

    public virtual async Task<bool> AnyAsync(Guid tenantId, Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(x => x.TenantId == tenantId).AnyAsync(predicate, cancellationToken);
    }

    public virtual async Task<int> CountAsync(Guid tenantId, Expression<Func<T, bool>>? predicate = null, CancellationToken cancellationToken = default)
    {
        var query = _dbSet.Where(x => x.TenantId == tenantId);
        return predicate == null 
            ? await query.CountAsync(cancellationToken)
            : await query.CountAsync(predicate, cancellationToken);
    }
}
