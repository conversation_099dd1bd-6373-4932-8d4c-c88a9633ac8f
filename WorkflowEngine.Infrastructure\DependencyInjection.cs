using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using WorkflowEngine.Application.Interfaces;
using WorkflowEngine.Domain.Enums;
using WorkflowEngine.Infrastructure.Activities;
using WorkflowEngine.Infrastructure.Data;
using WorkflowEngine.Infrastructure.Repositories;
using WorkflowEngine.Infrastructure.Services;

namespace WorkflowEngine.Infrastructure;

public static class DependencyInjection
{
    public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        // Database
        services.AddDbContext<WorkflowDbContext>(options =>
            options.UseNpgsql(configuration.GetConnectionString("DefaultConnection")));

        // Tenant Context
        services.AddScoped<ITenantContext, TenantContext>();
        services.AddScoped<ITenantResolver, TenantResolver>();
        services.AddScoped<ITenantService, TenantService>();

        // Repositories
        services.AddScoped<ITenantRepository, TenantRepository>();
        services.AddScoped<IWorkflowDefinitionRepository, WorkflowDefinitionRepository>();
        services.AddScoped<IWorkflowInstanceRepository, WorkflowInstanceRepository>();
        services.AddScoped<IActivityRepository, ActivityRepository>();
        services.AddScoped<IActivityExecutionRepository, ActivityExecutionRepository>();
        services.AddScoped<IDeadLetterQueueRepository, DeadLetterQueueRepository>();
        services.AddScoped<IOutboxEventRepository, OutboxEventRepository>();

        // Workflow Services
        services.AddScoped<IWorkflowEngine, Services.WorkflowEngine>();
        services.AddScoped<IWorkflowVariableService, WorkflowVariableService>();
        services.AddScoped<IWorkflowExecutionService, WorkflowExecutionService>();
        services.AddScoped<IWorkflowStepTransitionService, WorkflowStepTransitionService>();
        services.AddScoped<IRetryPolicyService, RetryPolicyService>();
        services.AddScoped<IDeadLetterQueueService, DeadLetterQueueService>();
        services.AddScoped<IOutboxService, OutboxService>();

        // Background Services
        services.AddHostedService<WorkflowBackgroundProcessor>();
        services.AddHostedService<OutboxEventProcessor>();

        // Activity Executors
        services.AddScoped<TimerActivityExecutor>();
        services.AddScoped<HttpRequestActivityExecutor>();
        services.AddScoped<DecisionActivityExecutor>();
        services.AddScoped<ScriptActivityExecutor>();

        // HTTP Client for HTTP Request Activity
        services.AddHttpClient<HttpRequestActivityExecutor>();

        // Activity Executor Factory with registered executors
        services.AddSingleton<IActivityExecutorFactory>(provider =>
        {
            var factory = new ActivityExecutorFactory(provider.GetRequiredService<ILogger<ActivityExecutorFactory>>());

            // Register activity executors
            var timerExecutor = provider.GetRequiredService<TimerActivityExecutor>();
            var httpExecutor = provider.GetRequiredService<HttpRequestActivityExecutor>();
            var decisionExecutor = provider.GetRequiredService<DecisionActivityExecutor>();
            var scriptExecutor = provider.GetRequiredService<ScriptActivityExecutor>();

            factory.RegisterExecutor(ActivityType.Timer, timerExecutor);
            factory.RegisterExecutor(ActivityType.HttpRequest, httpExecutor);
            factory.RegisterExecutor(ActivityType.Decision, decisionExecutor);
            factory.RegisterExecutor(ActivityType.Script, scriptExecutor);

            return factory;
        });

        // HTTP Context Accessor for tenant resolution
        services.AddHttpContextAccessor();

        // Outbox Event Handlers
        services.AddScoped<IOutboxEventHandler, Services.OutboxEventHandlers.WorkflowInstanceStartedEventHandler>();
        services.AddScoped<IOutboxEventHandler, Services.OutboxEventHandlers.WorkflowInstanceCompletedEventHandler>();
        services.AddScoped<IOutboxEventHandler, Services.OutboxEventHandlers.WorkflowStepCompletedEventHandler>();

        return services;
    }
}
