using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;

namespace WorkflowEngine.Infrastructure.Data;

public class WorkflowDbContextFactory : IDesignTimeDbContextFactory<WorkflowDbContext>
{
    public WorkflowDbContext CreateDbContext(string[] args)
    {
        var optionsBuilder = new DbContextOptionsBuilder<WorkflowDbContext>();
        
        // Use a default connection string for migrations
        // In production, this will be configured through dependency injection
        optionsBuilder.UseNpgsql("Host=localhost;Database=WorkflowEngine;Username=********;Password=********");

        return new WorkflowDbContext(optionsBuilder.Options);
    }
}
