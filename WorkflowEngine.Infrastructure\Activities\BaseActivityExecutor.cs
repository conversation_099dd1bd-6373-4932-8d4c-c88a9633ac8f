using Microsoft.Extensions.Logging;
using System.Text.Json;
using WorkflowEngine.Application.Interfaces;
using WorkflowEngine.Domain.Enums;

namespace WorkflowEngine.Infrastructure.Activities;

public abstract class BaseActivityExecutor : IActivityExecutor
{
    protected readonly ILogger _logger;

    protected BaseActivityExecutor(ILogger logger)
    {
        _logger = logger;
    }

    public abstract Task<ActivityExecutionResult> ExecuteAsync(ActivityExecutionContext context, CancellationToken cancellationToken = default);
    
    public abstract bool CanExecute(ActivityType activityType);

    public virtual async Task<bool> ValidateConfigurationAsync(string? configuration, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(configuration))
        {
            return true; // Empty configuration is valid for most activities
        }

        try
        {
            // Basic JSON validation
            JsonSerializer.Deserialize<Dictionary<string, object>>(configuration);
            return true;
        }
        catch (JsonException ex)
        {
            _logger.LogWarning(ex, "Invalid JSON configuration: {Configuration}", configuration);
            return false;
        }
    }

    protected virtual Dictionary<string, object>? ParseConfiguration(string? configuration)
    {
        if (string.IsNullOrEmpty(configuration))
        {
            return new Dictionary<string, object>();
        }

        try
        {
            return JsonSerializer.Deserialize<Dictionary<string, object>>(configuration);
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "Failed to parse configuration: {Configuration}", configuration);
            return new Dictionary<string, object>();
        }
    }

    protected virtual T? GetConfigurationValue<T>(Dictionary<string, object>? config, string key, T? defaultValue = default)
    {
        if (config == null || !config.TryGetValue(key, out var value))
        {
            return defaultValue;
        }

        try
        {
            if (value is JsonElement jsonElement)
            {
                return JsonSerializer.Deserialize<T>(jsonElement.GetRawText());
            }

            if (value is T directValue)
            {
                return directValue;
            }

            return (T)Convert.ChangeType(value, typeof(T));
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to convert configuration value {Key} to type {Type}", key, typeof(T).Name);
            return defaultValue;
        }
    }

    protected virtual string SubstituteVariables(string template, Dictionary<string, object>? variables)
    {
        if (string.IsNullOrEmpty(template) || variables == null)
        {
            return template;
        }

        var result = template;
        foreach (var kvp in variables)
        {
            var placeholder = $"${{{kvp.Key}}}";
            var value = kvp.Value?.ToString() ?? string.Empty;
            result = result.Replace(placeholder, value);
        }

        return result;
    }

    protected virtual ActivityExecutionResult CreateSuccessResult(string? outputData = null, Dictionary<string, object>? variables = null, TimeSpan? duration = null)
    {
        return new ActivityExecutionResult
        {
            IsSuccess = true,
            Status = ExecutionStatus.Completed,
            OutputData = outputData,
            Variables = variables,
            Duration = duration
        };
    }

    protected virtual ActivityExecutionResult CreateFailureResult(string errorMessage, Exception? exception = null, bool shouldRetry = false, DateTime? nextRetryAt = null)
    {
        return new ActivityExecutionResult
        {
            IsSuccess = false,
            Status = ExecutionStatus.Failed,
            ErrorMessage = errorMessage,
            StackTrace = exception?.StackTrace,
            ShouldRetry = shouldRetry,
            NextRetryAt = nextRetryAt
        };
    }

    protected virtual ActivityExecutionResult CreatePendingResult(DateTime? resumeAt = null, bool requiresUserInput = false)
    {
        return new ActivityExecutionResult
        {
            IsSuccess = true,
            Status = ExecutionStatus.Pending,
            ResumeAt = resumeAt,
            RequiresUserInput = requiresUserInput
        };
    }
}
