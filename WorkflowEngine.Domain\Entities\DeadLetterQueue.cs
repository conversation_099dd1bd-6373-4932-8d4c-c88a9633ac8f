using WorkflowEngine.Domain.Common;
using WorkflowEngine.Domain.Enums;

namespace WorkflowEngine.Domain.Entities;

public class DeadLetterQueue : BaseEntity, ITenantEntity
{
    public Guid TenantId { get; set; }
    public Guid WorkflowInstanceId { get; set; }
    public Guid? WorkflowStepId { get; set; }
    public Guid? ActivityId { get; set; }
    public Guid? ActivityExecutionId { get; set; }
    public DeadLetterReason Reason { get; set; }
    public string? ErrorMessage { get; set; }
    public string? StackTrace { get; set; }
    public string? InputData { get; set; }
    public string? Configuration { get; set; }
    public int FailedAttempts { get; set; }
    public DateTime FirstFailedAt { get; set; }
    public DateTime LastFailedAt { get; set; }
    public DeadLetterStatus Status { get; set; } = DeadLetterStatus.Pending;
    public DateTime? ProcessedAt { get; set; }
    public string? ProcessedBy { get; set; }
    public string? Resolution { get; set; }
    public string? Notes { get; set; }

    // Navigation properties
    public virtual Tenant Tenant { get; set; } = null!;
    public virtual WorkflowInstance WorkflowInstance { get; set; } = null!;
    public virtual WorkflowStep? WorkflowStep { get; set; }
    public virtual Activity? Activity { get; set; }
    public virtual ActivityExecution? ActivityExecution { get; set; }
}

public enum DeadLetterReason
{
    MaxRetriesExceeded = 0,
    NonRetryableException = 1,
    Timeout = 2,
    ConfigurationError = 3,
    SystemError = 4,
    ManuallyMoved = 5
}

public enum DeadLetterStatus
{
    Pending = 0,
    InvestigationRequired = 1,
    Resolved = 2,
    Ignored = 3,
    Requeued = 4
}
