using Microsoft.Extensions.Logging;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using WorkflowEngine.Application.Interfaces;
using WorkflowEngine.Domain.Enums;

namespace WorkflowEngine.Infrastructure.Activities;

public class HttpRequestActivityExecutor : BaseActivityExecutor
{
    private readonly HttpClient _httpClient;

    public HttpRequestActivityExecutor(HttpClient httpClient, ILogger<HttpRequestActivityExecutor> logger) : base(logger)
    {
        _httpClient = httpClient;
    }

    public override bool CanExecute(ActivityType activityType)
    {
        return activityType == ActivityType.HttpRequest;
    }

    public override async Task<ActivityExecutionResult> ExecuteAsync(ActivityExecutionContext context, CancellationToken cancellationToken = default)
    {
        var startTime = DateTime.UtcNow;
        
        try
        {
            _logger.LogInformation("Executing HTTP request activity for instance {InstanceId}, step {StepId}", 
                context.WorkflowInstance.Id, context.WorkflowStep.Id);

            var config = ParseConfiguration(context.Activity.Configuration);
            
            // Get HTTP configuration
            var url = GetConfigurationValue<string>(config, "url", null);
            var method = GetConfigurationValue<string>(config, "method", "GET").ToUpperInvariant();
            var headers = GetConfigurationValue<Dictionary<string, string>>(config, "headers", new Dictionary<string, string>());
            var body = GetConfigurationValue<string>(config, "body", null);
            var contentType = GetConfigurationValue<string>(config, "contentType", "application/json");
            var timeoutSeconds = GetConfigurationValue<int>(config, "timeoutSeconds", 30);
            var retryOnFailure = GetConfigurationValue<bool>(config, "retryOnFailure", true);
            var expectedStatusCodes = GetConfigurationValue<int[]>(config, "expectedStatusCodes", new[] { 200, 201, 202, 204 });

            if (string.IsNullOrEmpty(url))
            {
                return CreateFailureResult("URL is required for HTTP request activity");
            }

            // Substitute variables in URL, headers, and body
            url = SubstituteVariables(url, context.Variables);
            body = SubstituteVariables(body, context.Variables);

            // Create HTTP request
            using var request = new HttpRequestMessage();
            request.RequestUri = new Uri(url);
            request.Method = new HttpMethod(method);

            // Add headers
            foreach (var header in headers)
            {
                var headerValue = SubstituteVariables(header.Value, context.Variables);
                request.Headers.TryAddWithoutValidation(header.Key, headerValue);
            }

            // Add body for POST, PUT, PATCH requests
            if (!string.IsNullOrEmpty(body) && (method == "POST" || method == "PUT" || method == "PATCH"))
            {
                request.Content = new StringContent(body, Encoding.UTF8, contentType);
            }

            // Set timeout
            using var timeoutCts = new CancellationTokenSource(TimeSpan.FromSeconds(timeoutSeconds));
            using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, timeoutCts.Token);

            _logger.LogDebug("Making HTTP {Method} request to {Url}", method, url);

            // Execute request
            using var response = await _httpClient.SendAsync(request, combinedCts.Token);
            var responseContent = await response.Content.ReadAsStringAsync(combinedCts.Token);
            var duration = DateTime.UtcNow - startTime;

            _logger.LogDebug("HTTP request completed with status {StatusCode} in {Duration}ms", 
                response.StatusCode, duration.TotalMilliseconds);

            // Check if status code is expected
            var statusCodeInt = (int)response.StatusCode;
            var isSuccess = expectedStatusCodes.Contains(statusCodeInt);

            var outputData = JsonSerializer.Serialize(new
            {
                statusCode = statusCodeInt,
                statusText = response.StatusCode.ToString(),
                headers = response.Headers.ToDictionary(h => h.Key, h => string.Join(", ", h.Value)),
                body = responseContent,
                url = url,
                method = method,
                duration = duration.TotalMilliseconds,
                success = isSuccess
            });

            if (isSuccess)
            {
                _logger.LogInformation("HTTP request successful: {Method} {Url} -> {StatusCode}", method, url, response.StatusCode);
                return CreateSuccessResult(outputData, null, duration);
            }
            else
            {
                var errorMessage = $"HTTP request failed with unexpected status code: {response.StatusCode}";
                _logger.LogWarning("HTTP request failed: {Method} {Url} -> {StatusCode}", method, url, response.StatusCode);
                
                return CreateFailureResult(errorMessage, shouldRetry: retryOnFailure, 
                    nextRetryAt: retryOnFailure ? DateTime.UtcNow.AddMinutes(1) : null);
            }
        }
        catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException || cancellationToken.IsCancellationRequested)
        {
            var errorMessage = "HTTP request timed out";
            _logger.LogWarning(ex, "HTTP request timed out for instance {InstanceId}", context.WorkflowInstance.Id);
            return CreateFailureResult(errorMessage, ex, shouldRetry: true, nextRetryAt: DateTime.UtcNow.AddMinutes(2));
        }
        catch (HttpRequestException ex)
        {
            var errorMessage = $"HTTP request failed: {ex.Message}";
            _logger.LogError(ex, "HTTP request failed for instance {InstanceId}", context.WorkflowInstance.Id);
            return CreateFailureResult(errorMessage, ex, shouldRetry: true, nextRetryAt: DateTime.UtcNow.AddMinutes(1));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing HTTP request activity for instance {InstanceId}", context.WorkflowInstance.Id);
            return CreateFailureResult($"HTTP request activity failed: {ex.Message}", ex, shouldRetry: false);
        }
    }

    public override async Task<bool> ValidateConfigurationAsync(string? configuration, CancellationToken cancellationToken = default)
    {
        if (!await base.ValidateConfigurationAsync(configuration, cancellationToken))
        {
            return false;
        }

        var config = ParseConfiguration(configuration);
        if (config == null)
        {
            return false;
        }

        // Validate required URL
        var url = GetConfigurationValue<string>(config, "url", null);
        if (string.IsNullOrEmpty(url))
        {
            _logger.LogWarning("HTTP request activity configuration must specify a URL");
            return false;
        }

        // Validate URL format
        if (!Uri.TryCreate(url, UriKind.Absolute, out var uri) || (uri.Scheme != "http" && uri.Scheme != "https"))
        {
            _logger.LogWarning("Invalid URL format: {Url}", url);
            return false;
        }

        // Validate HTTP method
        var method = GetConfigurationValue<string>(config, "method", "GET").ToUpperInvariant();
        var validMethods = new[] { "GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS" };
        if (!validMethods.Contains(method))
        {
            _logger.LogWarning("Invalid HTTP method: {Method}", method);
            return false;
        }

        // Validate timeout
        var timeoutSeconds = GetConfigurationValue<int>(config, "timeoutSeconds", 30);
        if (timeoutSeconds <= 0 || timeoutSeconds > 300) // Max 5 minutes
        {
            _logger.LogWarning("Invalid timeout value: {TimeoutSeconds}. Must be between 1 and 300 seconds", timeoutSeconds);
            return false;
        }

        return true;
    }
}
