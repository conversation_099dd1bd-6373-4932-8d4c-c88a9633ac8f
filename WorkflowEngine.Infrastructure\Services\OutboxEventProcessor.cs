using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using WorkflowEngine.Application.Interfaces;
using WorkflowEngine.Domain.Entities;

namespace WorkflowEngine.Infrastructure.Services;

public class OutboxEventProcessor : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<OutboxEventProcessor> _logger;
    private readonly TimeSpan _processingInterval = TimeSpan.FromSeconds(30);
    private readonly TimeSpan _retryInterval = TimeSpan.FromMinutes(1);

    public OutboxEventProcessor(
        IServiceProvider serviceProvider,
        ILogger<OutboxEventProcessor> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Outbox Event Processor started");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await ProcessOutboxEventsAsync(stoppingToken);
                await ProcessRetryEventsAsync(stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in outbox event processing cycle");
            }

            try
            {
                await Task.Delay(_processingInterval, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                break;
            }
        }

        _logger.LogInformation("Outbox Event Processor stopped");
    }

    private async Task ProcessOutboxEventsAsync(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var outboxService = scope.ServiceProvider.GetRequiredService<IOutboxService>();
        var eventHandlers = scope.ServiceProvider.GetServices<IOutboxEventHandler>();

        var processedCount = 0;

        try
        {
            var pendingEvents = await outboxService.GetPendingEventsAsync(100, cancellationToken);

            foreach (var outboxEvent in pendingEvents)
            {
                if (cancellationToken.IsCancellationRequested)
                    break;

                try
                {
                    await ProcessSingleEventAsync(outboxEvent, eventHandlers, outboxService, cancellationToken);
                    processedCount++;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing outbox event {EventId}", outboxEvent.Id);
                }
            }

            if (processedCount > 0)
            {
                _logger.LogDebug("Processed {ProcessedCount} outbox events", processedCount);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing outbox events");
        }
    }

    private async Task ProcessRetryEventsAsync(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var outboxService = scope.ServiceProvider.GetRequiredService<IOutboxService>();
        var eventHandlers = scope.ServiceProvider.GetServices<IOutboxEventHandler>();

        var processedCount = 0;

        try
        {
            var retryEvents = await outboxService.GetEventsForRetryAsync(DateTime.UtcNow, 50, cancellationToken);

            foreach (var outboxEvent in retryEvents)
            {
                if (cancellationToken.IsCancellationRequested)
                    break;

                try
                {
                    _logger.LogDebug("Retrying outbox event {EventId} (attempt {RetryCount})", 
                        outboxEvent.Id, outboxEvent.RetryCount + 1);

                    await ProcessSingleEventAsync(outboxEvent, eventHandlers, outboxService, cancellationToken);
                    processedCount++;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error retrying outbox event {EventId}", outboxEvent.Id);
                }
            }

            if (processedCount > 0)
            {
                _logger.LogDebug("Retried {ProcessedCount} outbox events", processedCount);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing retry events");
        }
    }

    private async Task ProcessSingleEventAsync(
        OutboxEvent outboxEvent, 
        IEnumerable<IOutboxEventHandler> eventHandlers, 
        IOutboxService outboxService, 
        CancellationToken cancellationToken)
    {
        try
        {
            // Mark as processing
            await outboxService.ProcessEventAsync(outboxEvent, cancellationToken);

            // Find appropriate handler
            var handler = eventHandlers.FirstOrDefault(h => h.EventType == outboxEvent.EventType);
            if (handler == null)
            {
                _logger.LogWarning("No handler found for outbox event type {EventType}", outboxEvent.EventType);
                await outboxService.MarkEventAsFailedAsync(outboxEvent.Id, 
                    $"No handler available for event type {outboxEvent.EventType}", cancellationToken: cancellationToken);
                return;
            }

            // Check if handler can process this event
            var canHandle = await handler.CanHandleAsync(outboxEvent, cancellationToken);
            if (!canHandle)
            {
                _logger.LogWarning("Handler for {EventType} cannot process event {EventId}", 
                    outboxEvent.EventType, outboxEvent.Id);
                await outboxService.MarkEventAsFailedAsync(outboxEvent.Id, 
                    $"Handler cannot process this event", cancellationToken: cancellationToken);
                return;
            }

            // Process the event
            var result = await handler.HandleAsync(outboxEvent, cancellationToken);

            if (result.IsSuccess)
            {
                await outboxService.MarkEventAsCompletedAsync(outboxEvent.Id, cancellationToken);
                _logger.LogDebug("Successfully processed outbox event {EventId} of type {EventType}", 
                    outboxEvent.Id, outboxEvent.EventType);
            }
            else
            {
                await outboxService.MarkEventAsFailedAsync(outboxEvent.Id, 
                    result.ErrorMessage ?? "Handler returned failure", result.StackTrace, cancellationToken);

                if (result.ShouldRetry && result.NextRetryAt.HasValue)
                {
                    await outboxService.ScheduleRetryAsync(outboxEvent.Id, result.NextRetryAt.Value, cancellationToken);
                }

                _logger.LogWarning("Failed to process outbox event {EventId} of type {EventType}. Error: {Error}", 
                    outboxEvent.Id, outboxEvent.EventType, result.ErrorMessage);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing outbox event {EventId}", outboxEvent.Id);
            
            try
            {
                await outboxService.MarkEventAsFailedAsync(outboxEvent.Id, 
                    $"Processing failed: {ex.Message}", ex.StackTrace, cancellationToken);
            }
            catch (Exception markFailedEx)
            {
                _logger.LogError(markFailedEx, "Error marking outbox event {EventId} as failed", outboxEvent.Id);
            }
        }
    }
}
