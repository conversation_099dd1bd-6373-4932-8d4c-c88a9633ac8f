using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WorkflowEngine.Domain.Entities;
using WorkflowEngine.Domain.Enums;

namespace WorkflowEngine.Infrastructure.Data.Configurations;

public class WorkflowStepConfiguration : IEntityTypeConfiguration<WorkflowStep>
{
    public void Configure(EntityTypeBuilder<WorkflowStep> builder)
    {
        builder.ToTable("WorkflowSteps");

        builder.HasKey(ws => ws.Id);

        builder.Property(ws => ws.Name)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(ws => ws.Description)
            .HasMaxLength(1000);

        builder.Property(ws => ws.Type)
            .HasConversion<int>()
            .HasDefaultValue(StepType.Activity);

        builder.Property(ws => ws.Configuration)
            .HasColumnType("jsonb");

        builder.Property(ws => ws.Conditions)
            .HasColumnType("jsonb");

        builder.HasIndex(ws => ws.TenantId);
        builder.HasIndex(ws => ws.WorkflowDefinitionId);
        builder.HasIndex(ws => new { ws.WorkflowDefinitionId, ws.Order });

        // Navigation properties
        builder.HasOne(ws => ws.Tenant)
            .WithMany()
            .HasForeignKey(ws => ws.TenantId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(ws => ws.WorkflowDefinition)
            .WithMany(wd => wd.Steps)
            .HasForeignKey(ws => ws.WorkflowDefinitionId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(ws => ws.Activity)
            .WithMany(a => a.WorkflowSteps)
            .HasForeignKey(ws => ws.ActivityId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(ws => ws.NextStep)
            .WithMany(ws => ws.PreviousSteps)
            .HasForeignKey(ws => ws.NextStepId)
            .OnDelete(DeleteBehavior.SetNull);

        builder.HasOne(ws => ws.FailureStep)
            .WithMany(ws => ws.FailurePreviousSteps)
            .HasForeignKey(ws => ws.FailureStepId)
            .OnDelete(DeleteBehavior.SetNull);

        builder.HasMany(ws => ws.ActivityExecutions)
            .WithOne(ae => ae.WorkflowStep)
            .HasForeignKey(ae => ae.WorkflowStepId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
