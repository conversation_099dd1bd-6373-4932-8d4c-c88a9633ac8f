using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WorkflowEngine.API.DTOs.WorkflowDefinition;
using WorkflowEngine.Application.Interfaces;
using WorkflowEngine.Domain.Entities;
using WorkflowEngine.Domain.Enums;

namespace WorkflowEngine.API.Controllers;

[ApiController]
[Route("api/workflow-definition")]
[Authorize]
public class WorkflowDefinitionController : ControllerBase
{
    private readonly IWorkflowDefinitionService _workflowDefinitionService;
    private readonly IActivityService _activityService;
    private readonly ILogger<WorkflowDefinitionController> _logger;

    public WorkflowDefinitionController(
        IWorkflowDefinitionService workflowDefinitionService,
        IActivityService activityService,
        ILogger<WorkflowDefinitionController> logger)
    {
        _workflowDefinitionService = workflowDefinitionService;
        _activityService = activityService;
        _logger = logger;
    }

    /// <summary>
    /// Create a new workflow definition
    /// </summary>
    [HttpPost]
    [Authorize(Policy = "RequireWorkflowDefinitionCreate")]
    public async Task<ActionResult<WorkflowDefinitionResponse>> CreateWorkflowDefinition(
        [FromBody] CreateWorkflowDefinitionRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var workflowDefinition = new WorkflowDefinition
            {
                Name = request.Name,
                Description = request.Description,
                Version = request.Version,
                IsActive = request.IsActive,
                Configuration = request.Configuration
            };

            // Create workflow steps
            var steps = new List<WorkflowStep>();
            foreach (var stepRequest in request.Steps)
            {
                var step = new WorkflowStep
                {
                    Name = stepRequest.Name,
                    Description = stepRequest.Description,
                    Type = Enum.Parse<StepType>(stepRequest.Type, true),
                    Order = stepRequest.Order,
                    Configuration = stepRequest.Configuration,
                    ActivityId = stepRequest.ActivityId,
                    ActivityConfiguration = stepRequest.ActivityConfiguration
                };

                // Add decision conditions if this is a decision step
                if (step.Type == StepType.Decision && stepRequest.Conditions != null)
                {
                    step.Conditions = stepRequest.Conditions.Select(c => new DecisionCondition
                    {
                        Field = c.Field,
                        Operator = c.Operator,
                        Value = c.Value?.ToString(),
                        NextStepName = c.NextStepName
                    }).ToList();
                }

                steps.Add(step);
            }

            workflowDefinition.Steps = steps;

            var createdDefinition = await _workflowDefinitionService.CreateAsync(workflowDefinition, cancellationToken);
            var response = await MapToResponseAsync(createdDefinition, cancellationToken);

            _logger.LogInformation("Created workflow definition {WorkflowDefinitionId} with name {Name}",
                createdDefinition.Id, createdDefinition.Name);

            return CreatedAtAction(nameof(GetWorkflowDefinition), new { id = createdDefinition.Id }, response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating workflow definition");
            return StatusCode(500, new { message = "An error occurred while creating the workflow definition" });
        }
    }

    /// <summary>
    /// Get workflow definition by ID
    /// </summary>
    [HttpGet("{id:guid}")]
    [Authorize(Policy = "RequireWorkflowDefinitionRead")]
    public async Task<ActionResult<WorkflowDefinitionResponse>> GetWorkflowDefinition(
        Guid id,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var workflowDefinition = await _workflowDefinitionService.GetByIdAsync(id, cancellationToken);
            if (workflowDefinition == null)
            {
                return NotFound(new { message = "Workflow definition not found" });
            }

            var response = await MapToResponseAsync(workflowDefinition, cancellationToken);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving workflow definition {WorkflowDefinitionId}", id);
            return StatusCode(500, new { message = "An error occurred while retrieving the workflow definition" });
        }
    }

    /// <summary>
    /// Get all workflow definitions
    /// </summary>
    [HttpGet]
    [Authorize(Policy = "RequireWorkflowDefinitionRead")]
    public async Task<ActionResult<IEnumerable<WorkflowDefinitionListResponse>>> GetWorkflowDefinitions(
        [FromQuery] bool? isActive = null,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (page < 1) page = 1;
            if (pageSize < 1 || pageSize > 100) pageSize = 20;

            var workflowDefinitions = await _workflowDefinitionService.GetAllAsync(cancellationToken);

            if (isActive.HasValue)
            {
                workflowDefinitions = workflowDefinitions.Where(wd => wd.IsActive == isActive.Value);
            }

            var pagedDefinitions = workflowDefinitions
                .Skip((page - 1) * pageSize)
                .Take(pageSize);

            var responses = new List<WorkflowDefinitionListResponse>();
            foreach (var definition in pagedDefinitions)
            {
                responses.Add(new WorkflowDefinitionListResponse
                {
                    Id = definition.Id,
                    Name = definition.Name,
                    Description = definition.Description,
                    Version = definition.Version,
                    IsActive = definition.IsActive,
                    CreatedAt = definition.CreatedAt,
                    UpdatedAt = definition.UpdatedAt,
                    CreatedBy = definition.CreatedBy,
                    UpdatedBy = definition.UpdatedBy,
                    StepCount = definition.Steps?.Count ?? 0,
                    InstanceCount = definition.WorkflowInstances?.Count ?? 0,
                    ActiveInstanceCount = definition.WorkflowInstances?.Count(wi => wi.Status == WorkflowInstanceStatus.Running) ?? 0
                });
            }

            return Ok(responses);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving workflow definitions");
            return StatusCode(500, new { message = "An error occurred while retrieving workflow definitions" });
        }
    }

    /// <summary>
    /// Update workflow definition
    /// </summary>
    [HttpPut("{id:guid}")]
    [Authorize(Policy = "RequireWorkflowDefinitionUpdate")]
    public async Task<ActionResult<WorkflowDefinitionResponse>> UpdateWorkflowDefinition(
        Guid id,
        [FromBody] UpdateWorkflowDefinitionRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var existingDefinition = await _workflowDefinitionService.GetByIdAsync(id, cancellationToken);
            if (existingDefinition == null)
            {
                return NotFound(new { message = "Workflow definition not found" });
            }

            existingDefinition.Name = request.Name;
            existingDefinition.Description = request.Description;
            existingDefinition.IsActive = request.IsActive;
            existingDefinition.Configuration = request.Configuration;

            // Update steps if provided
            if (request.Steps != null)
            {
                // This is a simplified update - in a real scenario, you'd want more sophisticated step management
                existingDefinition.Steps?.Clear();
                
                var updatedSteps = new List<WorkflowStep>();
                foreach (var stepRequest in request.Steps)
                {
                    var step = new WorkflowStep
                    {
                        Id = stepRequest.Id ?? Guid.NewGuid(),
                        Name = stepRequest.Name,
                        Description = stepRequest.Description,
                        Type = Enum.Parse<StepType>(stepRequest.Type, true),
                        Order = stepRequest.Order,
                        Configuration = stepRequest.Configuration,
                        ActivityId = stepRequest.ActivityId,
                        ActivityConfiguration = stepRequest.ActivityConfiguration,
                        WorkflowDefinitionId = id
                    };

                    if (step.Type == StepType.Decision && stepRequest.Conditions != null)
                    {
                        step.Conditions = stepRequest.Conditions.Select(c => new DecisionCondition
                        {
                            Id = c.Id ?? Guid.NewGuid(),
                            Field = c.Field,
                            Operator = c.Operator,
                            Value = c.Value?.ToString(),
                            NextStepName = c.NextStepName,
                            WorkflowStepId = step.Id
                        }).ToList();
                    }

                    updatedSteps.Add(step);
                }

                existingDefinition.Steps = updatedSteps;
            }

            var updatedDefinition = await _workflowDefinitionService.UpdateAsync(existingDefinition, cancellationToken);
            var response = await MapToResponseAsync(updatedDefinition, cancellationToken);

            _logger.LogInformation("Updated workflow definition {WorkflowDefinitionId}", id);

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating workflow definition {WorkflowDefinitionId}", id);
            return StatusCode(500, new { message = "An error occurred while updating the workflow definition" });
        }
    }

    /// <summary>
    /// Delete workflow definition
    /// </summary>
    [HttpDelete("{id:guid}")]
    [Authorize(Policy = "RequireWorkflowDefinitionDelete")]
    public async Task<ActionResult> DeleteWorkflowDefinition(
        Guid id,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var workflowDefinition = await _workflowDefinitionService.GetByIdAsync(id, cancellationToken);
            if (workflowDefinition == null)
            {
                return NotFound(new { message = "Workflow definition not found" });
            }

            await _workflowDefinitionService.DeleteAsync(id, cancellationToken);

            _logger.LogInformation("Deleted workflow definition {WorkflowDefinitionId}", id);

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting workflow definition {WorkflowDefinitionId}", id);
            return StatusCode(500, new { message = "An error occurred while deleting the workflow definition" });
        }
    }

    private async Task<WorkflowDefinitionResponse> MapToResponseAsync(WorkflowDefinition definition, CancellationToken cancellationToken)
    {
        var response = new WorkflowDefinitionResponse
        {
            Id = definition.Id,
            Name = definition.Name,
            Description = definition.Description,
            Version = definition.Version,
            IsActive = definition.IsActive,
            Configuration = definition.Configuration,
            CreatedAt = definition.CreatedAt,
            UpdatedAt = definition.UpdatedAt,
            CreatedBy = definition.CreatedBy,
            UpdatedBy = definition.UpdatedBy
        };

        // Map steps
        if (definition.Steps != null)
        {
            foreach (var step in definition.Steps.OrderBy(s => s.Order))
            {
                var stepResponse = new WorkflowStepResponse
                {
                    Id = step.Id,
                    Name = step.Name,
                    Description = step.Description,
                    Type = step.Type.ToString(),
                    Order = step.Order,
                    Configuration = step.Configuration,
                    FailureStepName = step.FailureStep?.Name,
                    ActivityId = step.ActivityId,
                    ActivityConfiguration = step.ActivityConfiguration
                };

                // Get activity details if present
                if (step.ActivityId.HasValue)
                {
                    var activity = await _activityService.GetByIdAsync(step.ActivityId.Value, cancellationToken);
                    if (activity != null)
                    {
                        stepResponse.ActivityName = activity.Name;
                        stepResponse.ActivityType = activity.Type.ToString();
                    }
                }

                // Map next steps
                if (step.NextSteps != null)
                {
                    stepResponse.NextStepNames = step.NextSteps.Select(ns => ns.Name).ToList();
                }

                // Map decision conditions
                if (step.Conditions != null)
                {
                    stepResponse.Conditions = step.Conditions.Select(c => new DecisionConditionResponse
                    {
                        Id = c.Id,
                        Field = c.Field,
                        Operator = c.Operator,
                        Value = c.Value,
                        NextStepName = c.NextStepName
                    }).ToList();
                }

                response.Steps.Add(stepResponse);
            }
        }

        return response;
    }
}
