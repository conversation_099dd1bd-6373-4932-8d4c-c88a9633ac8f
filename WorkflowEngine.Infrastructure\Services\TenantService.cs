using Microsoft.Extensions.Logging;
using WorkflowEngine.Application.Interfaces;
using WorkflowEngine.Domain.Entities;
using WorkflowEngine.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace WorkflowEngine.Infrastructure.Services;

public class TenantService : ITenantService
{
    private readonly ITenantRepository _tenantRepository;
    private readonly ITenantContext _tenantContext;
    private readonly ILogger<TenantService> _logger;

    public TenantService(
        ITenantRepository tenantRepository,
        ITenantContext tenantContext,
        ILogger<TenantService> logger)
    {
        _tenantRepository = tenantRepository;
        _tenantContext = tenantContext;
        _logger = logger;
    }

    public async Task<Tenant?> GetCurrentTenantAsync(CancellationToken cancellationToken = default)
    {
        if (_tenantContext.TenantId == Guid.Empty)
        {
            _logger.LogWarning("No tenant context available");
            return null;
        }

        return await _tenantRepository.GetByIdAsync(_tenantContext.TenantId, cancellationToken);
    }

    public async Task<Tenant?> GetTenantByIdAsync(Guid tenantId, CancellationToken cancellationToken = default)
    {
        return await _tenantRepository.GetByIdAsync(tenantId, cancellationToken);
    }

    public async Task<Tenant?> GetTenantByNameAsync(string name, CancellationToken cancellationToken = default)
    {
        return await _tenantRepository.GetByNameAsync(name, cancellationToken);
    }

    public async Task<Tenant> CreateTenantAsync(string name, string? description = null, CancellationToken cancellationToken = default)
    {
        // Check if tenant already exists
        var existingTenant = await _tenantRepository.GetByNameAsync(name, cancellationToken);
        if (existingTenant != null)
        {
            throw new InvalidOperationException($"Tenant with name '{name}' already exists");
        }

        var tenant = new Tenant
        {
            Name = name,
            Slug = GenerateSlugFromName(name),
            Description = description,
            IsActive = true,
            Settings = new Dictionary<string, object>()
        };

        await _tenantRepository.AddAsync(tenant, cancellationToken);
        await _tenantRepository.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Created new tenant {TenantId} with name {TenantName}", tenant.Id, tenant.Name);

        return tenant;
    }

    public async Task<bool> ValidateTenantAccessAsync(Guid tenantId, CancellationToken cancellationToken = default)
    {
        var tenant = await _tenantRepository.GetByIdAsync(tenantId, cancellationToken);
        return tenant != null && tenant.IsActive;
    }

    public async Task<Dictionary<string, object>?> GetTenantSettingsAsync(Guid tenantId, CancellationToken cancellationToken = default)
    {
        var tenant = await _tenantRepository.GetByIdAsync(tenantId, cancellationToken);
        return tenant?.Settings;
    }

    public async Task UpdateTenantSettingsAsync(Guid tenantId, Dictionary<string, object> settings, CancellationToken cancellationToken = default)
    {
        var tenant = await _tenantRepository.GetByIdAsync(tenantId, cancellationToken);
        if (tenant == null)
        {
            throw new InvalidOperationException($"Tenant with ID '{tenantId}' not found");
        }

        tenant.Settings = settings;
        await _tenantRepository.UpdateAsync(tenant, cancellationToken);
        await _tenantRepository.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Updated settings for tenant {TenantId}", tenantId);
    }

    public async Task<Tenant> CreateTenantWithAdminAsync(string name, string slug, string? description, Dictionary<string, object>? settings, string adminEmail, string adminFirstName, string adminLastName, string adminPassword, CancellationToken cancellationToken = default)
    {
        // Check if tenant slug already exists
        if (await TenantSlugExistsAsync(slug, cancellationToken: cancellationToken))
        {
            throw new InvalidOperationException($"Tenant with slug '{slug}' already exists");
        }

        // Check if tenant name already exists
        var existingTenant = await _tenantRepository.GetByNameAsync(name, cancellationToken);
        if (existingTenant != null)
        {
            throw new InvalidOperationException($"Tenant with name '{name}' already exists");
        }

        // Create tenant
        var tenant = new Tenant
        {
            Name = name,
            Slug = slug,
            Description = description,
            IsActive = true,
            Settings = settings ?? new Dictionary<string, object>()
        };

        await _tenantRepository.AddAsync(tenant, cancellationToken);
        await _tenantRepository.SaveChangesAsync(cancellationToken);

        // TODO: Create admin user for the tenant when auth service is available
        // For now, just create the tenant and log the admin user details
        _logger.LogInformation("Created new tenant {TenantId} with name {TenantName}. Admin user creation pending: {AdminEmail}",
            tenant.Id, tenant.Name, adminEmail);

        return tenant;
    }

    public async Task<Tenant?> GetTenantBySlugAsync(string slug, CancellationToken cancellationToken = default)
    {
        return await _tenantRepository.GetBySlugAsync(slug, cancellationToken);
    }

    public async Task<IEnumerable<Tenant>> GetAllTenantsAsync(CancellationToken cancellationToken = default)
    {
        return await _tenantRepository.GetAllAsync(cancellationToken);
    }

    public async Task<IEnumerable<Tenant>> GetActiveTenantsAsync(CancellationToken cancellationToken = default)
    {
        return await _tenantRepository.GetActiveTenantsAsync(cancellationToken);
    }

    public async Task<Tenant> UpdateTenantAsync(Guid tenantId, string name, string? description = null, bool? isActive = null, Dictionary<string, object>? settings = null, CancellationToken cancellationToken = default)
    {
        var tenant = await _tenantRepository.GetByIdAsync(tenantId, cancellationToken);
        if (tenant == null)
        {
            throw new InvalidOperationException($"Tenant with ID '{tenantId}' not found");
        }

        // Check if name is changing and if new name already exists
        if (tenant.Name != name)
        {
            var existingTenant = await _tenantRepository.GetByNameAsync(name, cancellationToken);
            if (existingTenant != null && existingTenant.Id != tenantId)
            {
                throw new InvalidOperationException($"Tenant with name '{name}' already exists");
            }
        }

        tenant.Name = name;
        tenant.Description = description;
        if (isActive.HasValue)
            tenant.IsActive = isActive.Value;
        if (settings != null)
            tenant.Settings = settings;

        await _tenantRepository.UpdateAsync(tenant, cancellationToken);
        await _tenantRepository.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Updated tenant {TenantId} with name {TenantName}", tenantId, name);

        return tenant;
    }

    public async Task<bool> DeleteTenantAsync(Guid tenantId, CancellationToken cancellationToken = default)
    {
        var tenant = await _tenantRepository.GetByIdAsync(tenantId, cancellationToken);
        if (tenant == null)
        {
            return false;
        }

        await _tenantRepository.DeleteAsync(tenant, cancellationToken);
        await _tenantRepository.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Deleted tenant {TenantId} with name {TenantName}", tenantId, tenant.Name);

        return true;
    }

    public async Task<bool> ActivateTenantAsync(Guid tenantId, CancellationToken cancellationToken = default)
    {
        var tenant = await _tenantRepository.GetByIdAsync(tenantId, cancellationToken);
        if (tenant == null)
        {
            return false;
        }

        tenant.IsActive = true;
        await _tenantRepository.UpdateAsync(tenant, cancellationToken);
        await _tenantRepository.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Activated tenant {TenantId}", tenantId);

        return true;
    }

    public async Task<bool> DeactivateTenantAsync(Guid tenantId, CancellationToken cancellationToken = default)
    {
        var tenant = await _tenantRepository.GetByIdAsync(tenantId, cancellationToken);
        if (tenant == null)
        {
            return false;
        }

        tenant.IsActive = false;
        await _tenantRepository.UpdateAsync(tenant, cancellationToken);
        await _tenantRepository.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Deactivated tenant {TenantId}", tenantId);

        return true;
    }

    public async Task<bool> TenantExistsAsync(Guid tenantId, CancellationToken cancellationToken = default)
    {
        var tenant = await _tenantRepository.GetByIdAsync(tenantId, cancellationToken);
        return tenant != null;
    }

    public async Task<bool> TenantSlugExistsAsync(string slug, Guid? excludeTenantId = null, CancellationToken cancellationToken = default)
    {
        var tenant = await _tenantRepository.GetBySlugAsync(slug, cancellationToken);
        return tenant != null && (excludeTenantId == null || tenant.Id != excludeTenantId);
    }

    public async Task<Dictionary<string, object>> GetTenantStatsAsync(Guid tenantId, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default)
    {
        var stats = new Dictionary<string, object>();

        // Set default date range if not provided
        fromDate ??= DateTime.UtcNow.AddDays(-30);
        toDate ??= DateTime.UtcNow;

        // Get basic counts
        stats["UserCount"] = await GetTenantUserCountAsync(tenantId, cancellationToken);
        stats["WorkflowDefinitionCount"] = await GetTenantWorkflowDefinitionCountAsync(tenantId, cancellationToken);
        stats["ActiveWorkflowInstanceCount"] = await GetTenantActiveWorkflowInstanceCountAsync(tenantId, cancellationToken);

        // For now, set basic statistics - will be enhanced when repository methods are available
        stats["TotalWorkflowInstances"] = 0;
        stats["CompletedWorkflowInstances"] = 0;
        stats["FailedWorkflowInstances"] = 0;
        stats["RunningWorkflowInstances"] = 0;
        stats["WorkflowSuccessRate"] = 0.0;
        stats["AverageWorkflowDurationMinutes"] = 0.0;

        stats["StatsPeriodStart"] = fromDate;
        stats["StatsPeriodEnd"] = toDate;
        stats["GeneratedAt"] = DateTime.UtcNow;

        return stats;
    }

    public Task<int> GetTenantUserCountAsync(Guid tenantId, CancellationToken cancellationToken = default)
    {
        // TODO: Implement when user repository methods are available
        return Task.FromResult(0);
    }

    public Task<int> GetTenantWorkflowDefinitionCountAsync(Guid tenantId, CancellationToken cancellationToken = default)
    {
        // TODO: Implement when workflow definition repository methods are available
        return Task.FromResult(0);
    }

    public Task<int> GetTenantActiveWorkflowInstanceCountAsync(Guid tenantId, CancellationToken cancellationToken = default)
    {
        // TODO: Implement when workflow instance repository methods are available
        return Task.FromResult(0);
    }

    private static string GenerateSlugFromName(string name)
    {
        return name.ToLowerInvariant()
            .Replace(" ", "-")
            .Replace("_", "-")
            .Replace(".", "-")
            .Trim('-');
    }
}
