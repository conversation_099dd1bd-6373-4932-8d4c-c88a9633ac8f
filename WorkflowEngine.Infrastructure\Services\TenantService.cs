using Microsoft.Extensions.Logging;
using WorkflowEngine.Application.Interfaces;
using WorkflowEngine.Domain.Entities;
using WorkflowEngine.Infrastructure.Data;

namespace WorkflowEngine.Infrastructure.Services;

public class TenantService : ITenantService
{
    private readonly ITenantRepository _tenantRepository;
    private readonly ITenantContext _tenantContext;
    private readonly ILogger<TenantService> _logger;

    public TenantService(
        ITenantRepository tenantRepository,
        ITenantContext tenantContext,
        ILogger<TenantService> logger)
    {
        _tenantRepository = tenantRepository;
        _tenantContext = tenantContext;
        _logger = logger;
    }

    public async Task<Tenant?> GetCurrentTenantAsync(CancellationToken cancellationToken = default)
    {
        if (_tenantContext.TenantId == Guid.Empty)
        {
            _logger.LogWarning("No tenant context available");
            return null;
        }

        return await _tenantRepository.GetByIdAsync(_tenantContext.TenantId, cancellationToken);
    }

    public async Task<Tenant?> GetTenantByIdAsync(Guid tenantId, CancellationToken cancellationToken = default)
    {
        return await _tenantRepository.GetByIdAsync(tenantId, cancellationToken);
    }

    public async Task<Tenant?> GetTenantByNameAsync(string name, CancellationToken cancellationToken = default)
    {
        return await _tenantRepository.GetByNameAsync(name, cancellationToken);
    }

    public async Task<Tenant> CreateTenantAsync(string name, string? description = null, CancellationToken cancellationToken = default)
    {
        // Check if tenant already exists
        var existingTenant = await _tenantRepository.GetByNameAsync(name, cancellationToken);
        if (existingTenant != null)
        {
            throw new InvalidOperationException($"Tenant with name '{name}' already exists");
        }

        var tenant = new Tenant
        {
            Name = name,
            Description = description,
            IsActive = true,
            Settings = new Dictionary<string, object>()
        };

        await _tenantRepository.AddAsync(tenant, cancellationToken);
        await _tenantRepository.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Created new tenant {TenantId} with name {TenantName}", tenant.Id, tenant.Name);

        return tenant;
    }

    public async Task<bool> ValidateTenantAccessAsync(Guid tenantId, CancellationToken cancellationToken = default)
    {
        var tenant = await _tenantRepository.GetByIdAsync(tenantId, cancellationToken);
        return tenant != null && tenant.IsActive;
    }

    public async Task<Dictionary<string, object>?> GetTenantSettingsAsync(Guid tenantId, CancellationToken cancellationToken = default)
    {
        var tenant = await _tenantRepository.GetByIdAsync(tenantId, cancellationToken);
        return tenant?.Settings;
    }

    public async Task UpdateTenantSettingsAsync(Guid tenantId, Dictionary<string, object> settings, CancellationToken cancellationToken = default)
    {
        var tenant = await _tenantRepository.GetByIdAsync(tenantId, cancellationToken);
        if (tenant == null)
        {
            throw new InvalidOperationException($"Tenant with ID '{tenantId}' not found");
        }

        tenant.Settings = settings;
        await _tenantRepository.UpdateAsync(tenant, cancellationToken);
        await _tenantRepository.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Updated settings for tenant {TenantId}", tenantId);
    }
}
