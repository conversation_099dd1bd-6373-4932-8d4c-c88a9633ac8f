using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using WorkflowEngine.Application.Interfaces;
using WorkflowEngine.Domain.Entities;
using WorkflowEngine.Domain.Enums;
using WorkflowEngine.Infrastructure.Data;

namespace WorkflowEngine.Infrastructure.Services;

public class WorkflowDefinitionService : IWorkflowDefinitionService
{
    private readonly IWorkflowDefinitionRepository _workflowDefinitionRepository;
    private readonly ITenantContext _tenantContext;
    private readonly ILogger<WorkflowDefinitionService> _logger;

    public WorkflowDefinitionService(
        IWorkflowDefinitionRepository workflowDefinitionRepository,
        ITenantContext tenantContext,
        ILogger<WorkflowDefinitionService> logger)
    {
        _workflowDefinitionRepository = workflowDefinitionRepository;
        _tenantContext = tenantContext;
        _logger = logger;
    }

    public async Task<WorkflowDefinition> CreateAsync(WorkflowDefinition workflowDefinition, CancellationToken cancellationToken = default)
    {
        workflowDefinition.TenantId = _tenantContext.TenantId;
        workflowDefinition.Status = WorkflowStatus.Draft;

        // Check if workflow with same name and version already exists
        var existing = await _workflowDefinitionRepository.GetByNameAndVersionAsync(
            _tenantContext.TenantId, workflowDefinition.Name, workflowDefinition.Version, cancellationToken);

        if (existing != null)
        {
            throw new InvalidOperationException($"Workflow definition with name '{workflowDefinition.Name}' and version '{workflowDefinition.Version}' already exists");
        }

        // Set tenant ID for all steps
        if (workflowDefinition.Steps != null)
        {
            foreach (var step in workflowDefinition.Steps)
            {
                step.TenantId = _tenantContext.TenantId;
                step.WorkflowDefinitionId = workflowDefinition.Id;
            }
        }

        await _workflowDefinitionRepository.AddAsync(workflowDefinition, cancellationToken);
        await _workflowDefinitionRepository.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Created workflow definition {WorkflowDefinitionId} with name {Name} and version {Version}",
            workflowDefinition.Id, workflowDefinition.Name, workflowDefinition.Version);

        return workflowDefinition;
    }

    public async Task<WorkflowDefinition?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _workflowDefinitionRepository.GetByIdAsync(_tenantContext.TenantId, id, cancellationToken);
    }

    public async Task<IEnumerable<WorkflowDefinition>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        return await _workflowDefinitionRepository.GetAllAsync(_tenantContext.TenantId, cancellationToken);
    }

    public async Task<WorkflowDefinition> UpdateAsync(WorkflowDefinition workflowDefinition, CancellationToken cancellationToken = default)
    {
        var existing = await _workflowDefinitionRepository.GetByIdAsync(_tenantContext.TenantId, workflowDefinition.Id, cancellationToken);
        if (existing == null)
        {
            throw new InvalidOperationException($"Workflow definition with ID {workflowDefinition.Id} not found");
        }

        // Update properties
        existing.Name = workflowDefinition.Name;
        existing.Description = workflowDefinition.Description;
        existing.IsActive = workflowDefinition.IsActive;
        existing.Tags = workflowDefinition.Tags;

        // Update steps if provided
        if (workflowDefinition.Steps != null)
        {
            // Clear existing steps
            existing.Steps?.Clear();
            existing.Steps = workflowDefinition.Steps;

            // Ensure proper relationships
            foreach (var step in existing.Steps)
            {
                step.TenantId = _tenantContext.TenantId;
                step.WorkflowDefinitionId = existing.Id;
            }
        }

        await _workflowDefinitionRepository.UpdateAsync(existing, cancellationToken);
        await _workflowDefinitionRepository.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Updated workflow definition {WorkflowDefinitionId}", workflowDefinition.Id);

        return existing;
    }

    public async Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var workflowDefinition = await _workflowDefinitionRepository.GetByIdAsync(_tenantContext.TenantId, id, cancellationToken);
        if (workflowDefinition == null)
        {
            throw new InvalidOperationException($"Workflow definition with ID {id} not found");
        }

        // Check if workflow can be deleted (no active instances)
        if (!await CanDeleteDefinitionAsync(id, cancellationToken))
        {
            throw new InvalidOperationException("Cannot delete workflow definition that has active instances");
        }

        await _workflowDefinitionRepository.DeleteAsync(workflowDefinition, cancellationToken);
        await _workflowDefinitionRepository.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Deleted workflow definition {WorkflowDefinitionId}", id);
    }

    public async Task<WorkflowDefinition> CreateDefinitionAsync(string name, string? description = null, CancellationToken cancellationToken = default)
    {
        var workflowDefinition = new WorkflowDefinition
        {
            TenantId = _tenantContext.TenantId,
            Name = name,
            Description = description,
            Version = 1,
            Status = WorkflowStatus.Draft,
            IsActive = true
        };

        return await CreateAsync(workflowDefinition, cancellationToken);
    }

    public async Task<WorkflowDefinition> UpdateDefinitionAsync(Guid definitionId, string? description = null, string? jsonDefinition = null, CancellationToken cancellationToken = default)
    {
        var existing = await _workflowDefinitionRepository.GetByIdAsync(_tenantContext.TenantId, definitionId, cancellationToken);
        if (existing == null)
        {
            throw new InvalidOperationException($"Workflow definition with ID {definitionId} not found");
        }

        if (!string.IsNullOrEmpty(description))
        {
            existing.Description = description;
        }

        if (!string.IsNullOrEmpty(jsonDefinition))
        {
            existing.JsonDefinition = jsonDefinition;
        }

        return await UpdateAsync(existing, cancellationToken);
    }

    public async Task<WorkflowDefinition> PublishDefinitionAsync(Guid definitionId, CancellationToken cancellationToken = default)
    {
        var definition = await _workflowDefinitionRepository.GetByIdAsync(_tenantContext.TenantId, definitionId, cancellationToken);
        if (definition == null)
        {
            throw new InvalidOperationException($"Workflow definition with ID {definitionId} not found");
        }

        // Validate definition before publishing
        var validationResult = await ValidateDefinitionAsync(definitionId, cancellationToken);
        if (!validationResult.IsValid)
        {
            throw new InvalidOperationException($"Cannot publish invalid workflow definition. Errors: {string.Join(", ", validationResult.Errors)}");
        }

        definition.Status = WorkflowStatus.Published;
        definition.IsActive = true;

        await _workflowDefinitionRepository.UpdateAsync(definition, cancellationToken);
        await _workflowDefinitionRepository.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Published workflow definition {WorkflowDefinitionId}", definitionId);

        return definition;
    }

    public async Task<WorkflowDefinition> CreateNewVersionAsync(Guid definitionId, CancellationToken cancellationToken = default)
    {
        var existingDefinition = await _workflowDefinitionRepository.GetByIdAsync(_tenantContext.TenantId, definitionId, cancellationToken);
        if (existingDefinition == null)
        {
            throw new InvalidOperationException($"Workflow definition with ID {definitionId} not found");
        }

        var newVersion = existingDefinition.Version + 1;

        var newDefinition = new WorkflowDefinition
        {
            TenantId = _tenantContext.TenantId,
            Name = existingDefinition.Name,
            Description = existingDefinition.Description,
            Version = newVersion,
            Status = WorkflowStatus.Draft,
            IsActive = true,
            Tags = existingDefinition.Tags
        };

        // Copy steps
        if (existingDefinition.Steps != null)
        {
            newDefinition.Steps = existingDefinition.Steps.Select(step => new WorkflowStep
            {
                TenantId = _tenantContext.TenantId,
                Name = step.Name,
                Description = step.Description,
                Type = step.Type,
                Order = step.Order,
                Configuration = step.Configuration,
                ActivityId = step.ActivityId,
                WorkflowDefinitionId = newDefinition.Id,
                Conditions = step.Conditions,
                NextStepId = step.NextStepId,
                FailureStepId = step.FailureStepId,
                IsRequired = step.IsRequired,
                TimeoutSeconds = step.TimeoutSeconds
            }).ToList();
        }

        await _workflowDefinitionRepository.AddAsync(newDefinition, cancellationToken);
        await _workflowDefinitionRepository.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Created new version {Version} of workflow definition {WorkflowName} with ID {NewDefinitionId}",
            newVersion, existingDefinition.Name, newDefinition.Id);

        return newDefinition;
    }

    public async Task<WorkflowDefinition?> GetDefinitionAsync(Guid definitionId, CancellationToken cancellationToken = default)
    {
        return await GetByIdAsync(definitionId, cancellationToken);
    }

    public async Task<WorkflowDefinition?> GetDefinitionAsync(string name, int? version = null, CancellationToken cancellationToken = default)
    {
        if (version.HasValue)
        {
            return await _workflowDefinitionRepository.GetByNameAndVersionAsync(_tenantContext.TenantId, name, version.Value, cancellationToken);
        }

        return await _workflowDefinitionRepository.GetLatestVersionAsync(_tenantContext.TenantId, name, cancellationToken);
    }

    public async Task<WorkflowDefinition?> GetLatestDefinitionAsync(string name, CancellationToken cancellationToken = default)
    {
        return await _workflowDefinitionRepository.GetLatestVersionAsync(_tenantContext.TenantId, name, cancellationToken);
    }

    public async Task<IEnumerable<WorkflowDefinition>> GetDefinitionsAsync(WorkflowStatus? status = null, CancellationToken cancellationToken = default)
    {
        var definitions = await _workflowDefinitionRepository.GetAllAsync(_tenantContext.TenantId, cancellationToken);

        if (status.HasValue)
        {
            definitions = definitions.Where(d => d.Status == status.Value);
        }

        return definitions;
    }

    public async Task<IEnumerable<WorkflowDefinition>> GetActiveDefinitionsAsync(CancellationToken cancellationToken = default)
    {
        var definitions = await _workflowDefinitionRepository.GetAllAsync(_tenantContext.TenantId, cancellationToken);
        return definitions.Where(d => d.IsActive);
    }

    // Step Management methods would be implemented here
    public async Task<WorkflowStep> AddStepAsync(Guid definitionId, string stepName, Guid activityId, int order, CancellationToken cancellationToken = default)
    {
        // Implementation would go here
        throw new NotImplementedException();
    }

    public async Task<WorkflowStep> UpdateStepAsync(Guid stepId, string? stepName = null, string? configuration = null, CancellationToken cancellationToken = default)
    {
        // Implementation would go here
        throw new NotImplementedException();
    }

    public async Task RemoveStepAsync(Guid stepId, CancellationToken cancellationToken = default)
    {
        // Implementation would go here
        throw new NotImplementedException();
    }

    public async Task<WorkflowStep> SetNextStepAsync(Guid stepId, Guid? nextStepId, CancellationToken cancellationToken = default)
    {
        // Implementation would go here
        throw new NotImplementedException();
    }

    public async Task<WorkflowStep> SetFailureStepAsync(Guid stepId, Guid? failureStepId, CancellationToken cancellationToken = default)
    {
        // Implementation would go here
        throw new NotImplementedException();
    }

    public async Task<WorkflowValidationResult> ValidateDefinitionAsync(Guid definitionId, CancellationToken cancellationToken = default)
    {
        var definition = await _workflowDefinitionRepository.GetByIdAsync(_tenantContext.TenantId, definitionId, cancellationToken);
        if (definition == null)
        {
            return WorkflowValidationResult.Invalid("Workflow definition not found");
        }

        var result = WorkflowValidationResult.Valid();

        // Basic validation
        if (string.IsNullOrEmpty(definition.Name))
        {
            result.AddError("Workflow definition must have a name");
        }

        if (definition.Steps == null || !definition.Steps.Any())
        {
            result.AddError("Workflow definition must have at least one step");
        }
        else
        {
            // Validate steps
            var startSteps = definition.Steps.Where(s => s.Type == StepType.Start).ToList();
            if (!startSteps.Any())
            {
                result.AddError("Workflow definition must have at least one start step");
            }
            else if (startSteps.Count > 1)
            {
                result.AddWarning("Workflow definition has multiple start steps");
            }

            // Check for duplicate step orders
            var duplicateOrders = definition.Steps.GroupBy(s => s.Order).Where(g => g.Count() > 1).ToList();
            if (duplicateOrders.Any())
            {
                result.AddError($"Duplicate step orders found: {string.Join(", ", duplicateOrders.Select(g => g.Key))}");
            }
        }

        return result;
    }

    public async Task<bool> CanDeleteDefinitionAsync(Guid definitionId, CancellationToken cancellationToken = default)
    {
        // Check if there are any active workflow instances
        // This would require access to workflow instance repository
        // For now, return true - this should be implemented properly
        await Task.CompletedTask;
        return true;
    }

    public async Task<WorkflowDefinition> ImportDefinitionAsync(string jsonDefinition, CancellationToken cancellationToken = default)
    {
        try
        {
            var definition = JsonSerializer.Deserialize<WorkflowDefinition>(jsonDefinition);
            if (definition == null)
            {
                throw new InvalidOperationException("Invalid JSON definition");
            }

            return await CreateAsync(definition, cancellationToken);
        }
        catch (JsonException ex)
        {
            throw new InvalidOperationException($"Invalid JSON format: {ex.Message}", ex);
        }
    }

    public async Task<string> ExportDefinitionAsync(Guid definitionId, CancellationToken cancellationToken = default)
    {
        var definition = await _workflowDefinitionRepository.GetByIdAsync(_tenantContext.TenantId, definitionId, cancellationToken);
        if (definition == null)
        {
            throw new InvalidOperationException($"Workflow definition with ID {definitionId} not found");
        }

        return JsonSerializer.Serialize(definition, new JsonSerializerOptions { WriteIndented = true });
    }
}
