namespace WorkflowEngine.Application.Interfaces;

public interface IWorkflowVariableService
{
    // Variable Management
    Task SetVariableAsync(Guid instanceId, string key, object value, CancellationToken cancellationToken = default);
    Task<T?> GetVariableAsync<T>(Guid instanceId, string key, CancellationToken cancellationToken = default);
    Task<object?> GetVariableAsync(Guid instanceId, string key, CancellationToken cancellationToken = default);
    Task<Dictionary<string, object>> GetAllVariablesAsync(Guid instanceId, CancellationToken cancellationToken = default);
    Task RemoveVariableAsync(Guid instanceId, string key, CancellationToken cancellationToken = default);
    Task ClearVariablesAsync(Guid instanceId, CancellationToken cancellationToken = default);
    
    // Variable Operations
    Task<Dictionary<string, object>> MergeVariablesAsync(Guid instanceId, Dictionary<string, object> variables, CancellationToken cancellationToken = default);
    Task<string> SubstituteVariablesAsync(Guid instanceId, string template, CancellationToken cancellationToken = default);
    Task<object?> EvaluateExpressionAsync(Guid instanceId, string expression, CancellationToken cancellationToken = default);
    
    // Variable Validation
    Task<bool> ValidateVariableAsync(string key, object value, string? schema = null, CancellationToken cancellationToken = default);
    Task<VariableValidationResult> ValidateVariablesAsync(Dictionary<string, object> variables, string? schema = null, CancellationToken cancellationToken = default);
}

public class VariableValidationResult
{
    public bool IsValid { get; set; }
    public Dictionary<string, string> Errors { get; set; } = new();
    
    public void AddError(string key, string error)
    {
        Errors[key] = error;
        IsValid = false;
    }
    
    public static VariableValidationResult Valid()
    {
        return new VariableValidationResult { IsValid = true };
    }
    
    public static VariableValidationResult Invalid(Dictionary<string, string> errors)
    {
        return new VariableValidationResult 
        { 
            IsValid = false, 
            Errors = errors 
        };
    }
}
