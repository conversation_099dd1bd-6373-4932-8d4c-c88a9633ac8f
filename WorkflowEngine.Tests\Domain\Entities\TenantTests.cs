using FluentAssertions;
using WorkflowEngine.Domain.Entities;

namespace WorkflowEngine.Tests.Domain.Entities;

public class TenantTests
{
    [Fact]
    public void Tenant_Should_Initialize_With_Default_Values()
    {
        // Act
        var tenant = new Tenant();

        // Assert
        tenant.Id.Should().NotBeEmpty();
        tenant.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        tenant.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        tenant.Name.Should().BeEmpty();
        tenant.Description.Should().BeNull();
        tenant.IsActive.Should().BeTrue();
        tenant.ConnectionString.Should().BeNull();
        tenant.Settings.Should().BeNull();
        tenant.WorkflowDefinitions.Should().NotBeNull().And.BeEmpty();
        tenant.WorkflowInstances.Should().NotBeNull().And.BeEmpty();
    }

    [Fact]
    public void Tenant_Should_Allow_Setting_Properties()
    {
        // Arrange
        var tenant = new Tenant();
        var settings = new Dictionary<string, object> { { "key1", "value1" } };

        // Act
        tenant.Name = "Test Tenant";
        tenant.Description = "Test Description";
        tenant.IsActive = false;
        tenant.ConnectionString = "test-connection";
        tenant.Settings = settings;

        // Assert
        tenant.Name.Should().Be("Test Tenant");
        tenant.Description.Should().Be("Test Description");
        tenant.IsActive.Should().BeFalse();
        tenant.ConnectionString.Should().Be("test-connection");
        tenant.Settings.Should().BeEquivalentTo(settings);
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData("Valid Name")]
    [InlineData("Name with spaces and 123 numbers")]
    public void Tenant_Should_Accept_Various_Name_Values(string name)
    {
        // Arrange
        var tenant = new Tenant();

        // Act
        tenant.Name = name;

        // Assert
        tenant.Name.Should().Be(name);
    }

    [Fact]
    public void Tenant_Should_Maintain_Separate_Collections()
    {
        // Arrange
        var tenant1 = new Tenant();
        var tenant2 = new Tenant();

        // Act
        tenant1.WorkflowDefinitions.Add(new WorkflowDefinition { Name = "Definition1" });
        tenant2.WorkflowInstances.Add(new WorkflowInstance { Name = "Instance1" });

        // Assert
        tenant1.WorkflowDefinitions.Should().HaveCount(1);
        tenant1.WorkflowInstances.Should().BeEmpty();
        tenant2.WorkflowDefinitions.Should().BeEmpty();
        tenant2.WorkflowInstances.Should().HaveCount(1);
    }
}
