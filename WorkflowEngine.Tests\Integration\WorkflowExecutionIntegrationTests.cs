using FluentAssertions;
using WorkflowEngine.Application.Interfaces;
using WorkflowEngine.Domain.Enums;

namespace WorkflowEngine.Tests.Integration;

public class WorkflowExecutionIntegrationTests : IntegrationTestBase
{
    [Fact]
    public async Task StartInstanceAsync_ShouldCreateWorkflowInstance()
    {
        // Arrange
        var workflowEngine = GetService<IWorkflowEngine>();
        var inputData = """{"testVariable": "testValue"}""";

        // Act
        var instance = await workflowEngine.StartInstanceAsync(TestWorkflowDefinitionId, inputData, "Test Instance");

        // Assert
        instance.Should().NotBeNull();
        instance.Id.Should().NotBeEmpty();
        instance.WorkflowDefinitionId.Should().Be(TestWorkflowDefinitionId);
        instance.Name.Should().Be("Test Instance");
        instance.Status.Should().Be(WorkflowInstanceStatus.NotStarted);
        instance.InputData.Should().Be(inputData);
        instance.TenantId.Should().Be(TestTenantId);
    }

    [Fact]
    public async Task StartInstanceAsync_WithWorkflowName_ShouldCreateWorkflowInstance()
    {
        // Arrange
        var workflowEngine = GetService<IWorkflowEngine>();

        // Act
        var instance = await workflowEngine.StartInstanceAsync("Test Workflow", inputData: """{"test": "value"}""");

        // Assert
        instance.Should().NotBeNull();
        instance.WorkflowDefinitionId.Should().Be(TestWorkflowDefinitionId);
        instance.Status.Should().Be(WorkflowInstanceStatus.NotStarted);
    }

    [Fact]
    public async Task ProgressInstanceAsync_ShouldStartAndExecuteFirstStep()
    {
        // Arrange
        var workflowEngine = GetService<IWorkflowEngine>();
        var instance = await CreateTestWorkflowInstanceAsync();

        // Act
        var result = await workflowEngine.ProgressInstanceAsync(instance.Id);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.Status.Should().Be(WorkflowInstanceStatus.Running);
        result.CurrentStepId.Should().Be(TestStep1Id);
    }

    [Fact]
    public async Task ProgressToNextStepAsync_ShouldMoveToNextStep()
    {
        // Arrange
        var workflowEngine = GetService<IWorkflowEngine>();
        var instance = await CreateTestWorkflowInstanceAsync();

        // Start the workflow
        await workflowEngine.ProgressInstanceAsync(instance.Id);

        // Act - Progress to next step
        var result = await workflowEngine.ProgressToNextStepAsync(instance.Id, """{"stepOutput": "completed"}""");

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.CurrentStepId.Should().Be(TestStep2Id);
    }

    [Fact]
    public async Task CompleteCurrentStepAsync_ShouldMarkStepAsCompleted()
    {
        // Arrange
        var workflowEngine = GetService<IWorkflowEngine>();
        var instance = await CreateTestWorkflowInstanceAsync();

        // Start the workflow
        await workflowEngine.ProgressInstanceAsync(instance.Id);

        // Act
        var result = await workflowEngine.CompleteCurrentStepAsync(instance.Id, """{"manualCompletion": true}""");

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.CurrentStepId.Should().Be(TestStep2Id);

        // Verify activity execution was created
        var activityExecutionRepo = GetService<IActivityExecutionRepository>();
        var executions = await activityExecutionRepo.GetByWorkflowInstanceAsync(TestTenantId, instance.Id);
        executions.Should().NotBeEmpty();
        executions.Should().Contain(e => e.WorkflowStepId == TestStep1Id && e.Status == ExecutionStatus.Completed);
    }

    [Fact]
    public async Task SuspendInstanceAsync_ShouldSuspendWorkflow()
    {
        // Arrange
        var workflowEngine = GetService<IWorkflowEngine>();
        var instance = await CreateTestWorkflowInstanceAsync();
        await workflowEngine.ProgressInstanceAsync(instance.Id);

        // Act
        var suspendedInstance = await workflowEngine.SuspendInstanceAsync(instance.Id, "Test suspension");

        // Assert
        suspendedInstance.Should().NotBeNull();
        suspendedInstance.Status.Should().Be(WorkflowInstanceStatus.Suspended);
    }

    [Fact]
    public async Task ResumeInstanceAsync_ShouldResumeWorkflow()
    {
        // Arrange
        var workflowEngine = GetService<IWorkflowEngine>();
        var instance = await CreateTestWorkflowInstanceAsync();
        await workflowEngine.ProgressInstanceAsync(instance.Id);
        await workflowEngine.SuspendInstanceAsync(instance.Id);

        // Act
        var result = await workflowEngine.ResumeInstanceAsync(instance.Id, "resume", """{"resumeData": "test"}""");

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.Status.Should().Be(WorkflowInstanceStatus.Running);
    }

    [Fact]
    public async Task HandleExternalEventAsync_ShouldStoreEventData()
    {
        // Arrange
        var workflowEngine = GetService<IWorkflowEngine>();
        var variableService = GetService<IWorkflowVariableService>();
        var instance = await CreateTestWorkflowInstanceAsync();
        await workflowEngine.ProgressInstanceAsync(instance.Id);

        // Act
        var result = await workflowEngine.HandleExternalEventAsync(instance.Id, "testEvent", """{"eventData": "value"}""");

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();

        // Verify event data was stored as variables
        var variables = await variableService.GetAllVariablesAsync(instance.Id);
        variables.Should().ContainKey("external_event_testEvent");
        variables.Should().ContainKey("lastExternalEventName");
        variables["lastExternalEventName"].Should().Be("testEvent");
    }

    [Fact]
    public async Task CancelInstanceAsync_ShouldCancelWorkflow()
    {
        // Arrange
        var workflowEngine = GetService<IWorkflowEngine>();
        var instance = await CreateTestWorkflowInstanceAsync();
        await workflowEngine.ProgressInstanceAsync(instance.Id);

        // Act
        var cancelledInstance = await workflowEngine.CancelInstanceAsync(instance.Id, "Test cancellation");

        // Assert
        cancelledInstance.Should().NotBeNull();
        cancelledInstance.Status.Should().Be(WorkflowInstanceStatus.Cancelled);
        cancelledInstance.CompletedAt.Should().NotBeNull();
    }

    [Fact]
    public async Task RetryInstanceAsync_ShouldRetryFailedWorkflow()
    {
        // Arrange
        var workflowEngine = GetService<IWorkflowEngine>();
        var instance = await CreateTestWorkflowInstanceAsync();
        
        // Manually set instance to failed state
        var instanceRepo = GetService<IWorkflowInstanceRepository>();
        var dbInstance = await instanceRepo.GetByIdAsync(TestTenantId, instance.Id);
        dbInstance!.Status = WorkflowInstanceStatus.Failed;
        dbInstance.ErrorMessage = "Test failure";
        await instanceRepo.UpdateAsync(dbInstance);
        await instanceRepo.SaveChangesAsync();

        // Act
        var retriedInstance = await workflowEngine.RetryInstanceAsync(instance.Id);

        // Assert
        retriedInstance.Should().NotBeNull();
        retriedInstance.Status.Should().Be(WorkflowInstanceStatus.Running);
        retriedInstance.RetryCount.Should().Be(1);
        retriedInstance.ErrorMessage.Should().BeNull();
    }

    [Fact]
    public async Task GetInstanceStatusAsync_ShouldReturnCorrectStatus()
    {
        // Arrange
        var workflowEngine = GetService<IWorkflowEngine>();
        var instance = await CreateTestWorkflowInstanceAsync();

        // Act
        var status = await workflowEngine.GetInstanceStatusAsync(instance.Id);

        // Assert
        status.Should().Be(WorkflowInstanceStatus.NotStarted);
    }

    [Fact]
    public async Task SetAndGetInstanceVariableAsync_ShouldWorkCorrectly()
    {
        // Arrange
        var workflowEngine = GetService<IWorkflowEngine>();
        var instance = await CreateTestWorkflowInstanceAsync();

        // Act
        await workflowEngine.SetInstanceVariableAsync(instance.Id, "testKey", "testValue");
        var value = await workflowEngine.GetInstanceVariableAsync<string>(instance.Id, "testKey");

        // Assert
        value.Should().Be("testValue");
    }

    [Fact]
    public async Task GetInstanceVariablesAsync_ShouldReturnAllVariables()
    {
        // Arrange
        var workflowEngine = GetService<IWorkflowEngine>();
        var instance = await CreateTestWorkflowInstanceAsync("""{"initialVar": "initialValue"}""");

        await workflowEngine.SetInstanceVariableAsync(instance.Id, "additionalVar", "additionalValue");

        // Act
        var variables = await workflowEngine.GetInstanceVariablesAsync(instance.Id);

        // Assert
        variables.Should().NotBeEmpty();
        variables.Should().ContainKey("initialVar");
        variables.Should().ContainKey("additionalVar");
        variables["initialVar"].Should().Be("initialValue");
        variables["additionalVar"].Should().Be("additionalValue");
    }

    [Fact]
    public async Task ValidateInstanceAsync_ShouldReturnTrueForValidInstance()
    {
        // Arrange
        var workflowEngine = GetService<IWorkflowEngine>();
        var instance = await CreateTestWorkflowInstanceAsync();

        // Act
        var isValid = await workflowEngine.ValidateInstanceAsync(instance.Id);

        // Assert
        isValid.Should().BeTrue();
    }
}
