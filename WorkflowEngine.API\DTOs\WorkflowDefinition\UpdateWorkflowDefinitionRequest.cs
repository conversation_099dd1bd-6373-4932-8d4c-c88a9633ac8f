using System.ComponentModel.DataAnnotations;

namespace WorkflowEngine.API.DTOs.WorkflowDefinition;

public class UpdateWorkflowDefinitionRequest
{
    [Required]
    [StringLength(200, MinimumLength = 1)]
    public string Name { get; set; } = string.Empty;

    [StringLength(1000)]
    public string Description { get; set; } = string.Empty;

    public bool IsActive { get; set; } = true;

    public Dictionary<string, object>? Configuration { get; set; }

    public List<UpdateWorkflowStepRequest>? Steps { get; set; }
}

public class UpdateWorkflowStepRequest
{
    public Guid? Id { get; set; } // Null for new steps

    [Required]
    [StringLength(200, MinimumLength = 1)]
    public string Name { get; set; } = string.Empty;

    [StringLength(1000)]
    public string Description { get; set; } = string.Empty;

    [Required]
    public string Type { get; set; } = string.Empty;

    [Required]
    [Range(0, int.MaxValue)]
    public int Order { get; set; }

    public Dictionary<string, object>? Configuration { get; set; }

    public List<string>? NextStepNames { get; set; }

    public string? FailureStepName { get; set; }

    public Guid? ActivityId { get; set; }

    public Dictionary<string, object>? ActivityConfiguration { get; set; }

    public List<UpdateDecisionConditionRequest>? Conditions { get; set; }
}

public class UpdateDecisionConditionRequest
{
    public Guid? Id { get; set; } // Null for new conditions

    [Required]
    [StringLength(200, MinimumLength = 1)]
    public string Field { get; set; } = string.Empty;

    [Required]
    [StringLength(50, MinimumLength = 1)]
    public string Operator { get; set; } = string.Empty;

    public object? Value { get; set; }

    [Required]
    [StringLength(200, MinimumLength = 1)]
    public string NextStepName { get; set; } = string.Empty;
}
