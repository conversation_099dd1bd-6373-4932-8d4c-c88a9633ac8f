using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WorkflowEngine.Domain.Entities;
using WorkflowEngine.Domain.Enums;

namespace WorkflowEngine.Infrastructure.Data.Configurations;

public class ActivityConfiguration : IEntityTypeConfiguration<Activity>
{
    public void Configure(EntityTypeBuilder<Activity> builder)
    {
        builder.ToTable("Activities");

        builder.HasKey(a => a.Id);

        builder.Property(a => a.Name)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(a => a.Description)
            .HasMaxLength(1000);

        builder.Property(a => a.Type)
            .HasConversion<int>()
            .HasDefaultValue(ActivityType.Custom);

        builder.Property(a => a.Configuration)
            .HasColumnType("jsonb");

        builder.Property(a => a.InputSchema)
            .HasColumnType("jsonb");

        builder.Property(a => a.OutputSchema)
            .HasColumnType("jsonb");

        builder.Property(a => a.Version)
            .HasMaxLength(20)
            .HasDefaultValue("1.0.0");

        builder.Property(a => a.AssemblyName)
            .HasMaxLength(500);

        builder.Property(a => a.ClassName)
            .HasMaxLength(500);

        builder.HasIndex(a => a.TenantId);
        builder.HasIndex(a => new { a.TenantId, a.Name, a.Version })
            .IsUnique();

        // Navigation properties
        builder.HasOne(a => a.Tenant)
            .WithMany()
            .HasForeignKey(a => a.TenantId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(a => a.WorkflowSteps)
            .WithOne(ws => ws.Activity)
            .HasForeignKey(ws => ws.ActivityId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasMany(a => a.ActivityExecutions)
            .WithOne(ae => ae.Activity)
            .HasForeignKey(ae => ae.ActivityId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
