using WorkflowEngine.Domain.Common;
using WorkflowEngine.Domain.Enums;

namespace WorkflowEngine.Domain.Entities;

public class RetryPolicy : BaseEntity, ITenantEntity
{
    public Guid TenantId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int MaxRetries { get; set; } = 3;
    public TimeSpan InitialDelay { get; set; } = TimeSpan.FromMinutes(1);
    public TimeSpan MaxDelay { get; set; } = TimeSpan.FromHours(1);
    public RetryBackoffStrategy BackoffStrategy { get; set; } = RetryBackoffStrategy.Exponential;
    public double BackoffMultiplier { get; set; } = 2.0;
    public bool RetryOnTimeout { get; set; } = true;
    public bool RetryOnHttpError { get; set; } = true;
    public bool RetryOnSystemError { get; set; } = false;
    public string? RetryableExceptions { get; set; } // JSON array of exception types
    public string? NonRetryableExceptions { get; set; } // JSON array of exception types
    public bool IsActive { get; set; } = true;

    // Navigation properties
    public virtual Tenant Tenant { get; set; } = null!;
    public virtual ICollection<Activity> Activities { get; set; } = new List<Activity>();
    public virtual ICollection<WorkflowDefinition> WorkflowDefinitions { get; set; } = new List<WorkflowDefinition>();
}

public enum RetryBackoffStrategy
{
    Fixed = 0,
    Linear = 1,
    Exponential = 2,
    ExponentialWithJitter = 3
}
