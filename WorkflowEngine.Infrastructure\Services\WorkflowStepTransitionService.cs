using Microsoft.Extensions.Logging;
using System.Text.Json;
using WorkflowEngine.Application.Interfaces;
using WorkflowEngine.Domain.Entities;
using WorkflowEngine.Domain.Enums;
using WorkflowEngine.Infrastructure.Data;

namespace WorkflowEngine.Infrastructure.Services;

public class WorkflowStepTransitionService : IWorkflowStepTransitionService
{
    private readonly IWorkflowDefinitionRepository _workflowDefinitionRepository;
    private readonly IWorkflowVariableService _variableService;
    private readonly ITenantContext _tenantContext;
    private readonly ILogger<WorkflowStepTransitionService> _logger;

    public WorkflowStepTransitionService(
        IWorkflowDefinitionRepository workflowDefinitionRepository,
        IWorkflowVariableService variableService,
        ITenantContext tenantContext,
        ILogger<WorkflowStepTransitionService> logger)
    {
        _workflowDefinitionRepository = workflowDefinitionRepository;
        _variableService = variableService;
        _tenantContext = tenantContext;
        _logger = logger;
    }

    public async Task<WorkflowStepTransitionResult> EvaluateTransitionAsync(WorkflowInstance instance, WorkflowStep currentStep, string? outcome = null, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Evaluating transition from step {StepId} ({StepName}) for instance {InstanceId}", 
                currentStep.Id, currentStep.Name, instance.Id);

            // Check if step conditions are met
            var conditionsMet = await EvaluateStepConditionsAsync(instance, currentStep, cancellationToken);
            if (!conditionsMet)
            {
                return WorkflowStepTransitionResult.NoTransition("Step conditions not met");
            }

            // Get next step based on step type and outcome
            var nextStep = await GetNextStepAsync(instance, currentStep, outcome, cancellationToken);
            if (nextStep == null)
            {
                return WorkflowStepTransitionResult.NoTransition("No next step found - workflow may be complete");
            }

            // Check if we can transition to the next step
            var canTransition = await CanTransitionAsync(instance, currentStep, nextStep, cancellationToken);
            if (!canTransition)
            {
                return WorkflowStepTransitionResult.NoTransition($"Cannot transition to step {nextStep.Name}");
            }

            // Check if next step requires user input
            if (nextStep.Type == StepType.HumanTask)
            {
                return WorkflowStepTransitionResult.WaitForInput($"Step {nextStep.Name} requires user input");
            }

            _logger.LogDebug("Transition approved from step {CurrentStepId} to step {NextStepId}", 
                currentStep.Id, nextStep.Id);

            return WorkflowStepTransitionResult.Success(nextStep);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error evaluating transition for instance {InstanceId}, step {StepId}", 
                instance.Id, currentStep.Id);
            return WorkflowStepTransitionResult.NoTransition($"Transition evaluation failed: {ex.Message}");
        }
    }

    public async Task<WorkflowStep?> GetNextStepAsync(WorkflowInstance instance, WorkflowStep currentStep, string? outcome = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var definition = await _workflowDefinitionRepository.GetByIdAsync(_tenantContext.TenantId, instance.WorkflowDefinitionId, cancellationToken);
            if (definition?.Steps == null)
            {
                return null;
            }

            // Handle different step types
            switch (currentStep.Type)
            {
                case StepType.Decision:
                    return await GetNextStepForDecisionAsync(definition, currentStep, outcome, cancellationToken);
                
                case StepType.Parallel:
                    return await GetNextStepForParallelAsync(definition, currentStep, outcome, cancellationToken);
                
                case StepType.End:
                    return null; // End step has no next step
                
                default:
                    return await GetNextStepDefaultAsync(definition, currentStep, cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting next step for instance {InstanceId}, current step {StepId}", 
                instance.Id, currentStep.Id);
            return null;
        }
    }

    public async Task<bool> CanTransitionAsync(WorkflowInstance instance, WorkflowStep fromStep, WorkflowStep toStep, CancellationToken cancellationToken = default)
    {
        try
        {
            // Check if target step is active
            if (!toStep.IsRequired && !await EvaluateStepConditionsAsync(instance, toStep, cancellationToken))
            {
                return false;
            }

            // Check if there are any blocking conditions
            // This could be extended to check for resource availability, permissions, etc.
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking transition capability from step {FromStepId} to step {ToStepId}", 
                fromStep.Id, toStep.Id);
            return false;
        }
    }

    public async Task<IEnumerable<WorkflowStep>> GetPossibleNextStepsAsync(WorkflowInstance instance, WorkflowStep currentStep, CancellationToken cancellationToken = default)
    {
        try
        {
            var definition = await _workflowDefinitionRepository.GetByIdAsync(_tenantContext.TenantId, instance.WorkflowDefinitionId, cancellationToken);
            if (definition?.Steps == null)
            {
                return Enumerable.Empty<WorkflowStep>();
            }

            var possibleSteps = new List<WorkflowStep>();

            // Add configured next step
            if (currentStep.NextStepId.HasValue)
            {
                var nextStep = definition.Steps.FirstOrDefault(s => s.Id == currentStep.NextStepId.Value);
                if (nextStep != null)
                {
                    possibleSteps.Add(nextStep);
                }
            }

            // Add failure step
            if (currentStep.FailureStepId.HasValue)
            {
                var failureStep = definition.Steps.FirstOrDefault(s => s.Id == currentStep.FailureStepId.Value);
                if (failureStep != null)
                {
                    possibleSteps.Add(failureStep);
                }
            }

            // For decision steps, add all possible outcome steps
            if (currentStep.Type == StepType.Decision)
            {
                // This could be enhanced to parse decision configuration and find all possible outcomes
                var allNextSteps = definition.Steps.Where(s => s.Order > currentStep.Order).Take(5); // Limit for safety
                possibleSteps.AddRange(allNextSteps);
            }

            // If no specific next steps, add the next step by order
            if (!possibleSteps.Any())
            {
                var nextByOrder = definition.Steps
                    .Where(s => s.Order > currentStep.Order)
                    .OrderBy(s => s.Order)
                    .FirstOrDefault();
                
                if (nextByOrder != null)
                {
                    possibleSteps.Add(nextByOrder);
                }
            }

            return possibleSteps.Distinct();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting possible next steps for instance {InstanceId}, step {StepId}", 
                instance.Id, currentStep.Id);
            return Enumerable.Empty<WorkflowStep>();
        }
    }

    public async Task<bool> EvaluateStepConditionsAsync(WorkflowInstance instance, WorkflowStep step, CancellationToken cancellationToken = default)
    {
        try
        {
            // If no conditions are specified, step is always available
            if (string.IsNullOrEmpty(step.Conditions))
            {
                return true;
            }

            // Get workflow variables
            var variables = await _variableService.GetAllVariablesAsync(instance.Id, cancellationToken);

            // Parse conditions (JSON format)
            var conditions = JsonSerializer.Deserialize<StepConditions>(step.Conditions);
            if (conditions == null)
            {
                return true;
            }

            // Evaluate each condition
            foreach (var condition in conditions.Conditions ?? Enumerable.Empty<StepCondition>())
            {
                var result = EvaluateCondition(condition, variables);
                
                if (conditions.LogicalOperator == "AND" && !result)
                {
                    return false;
                }
                else if (conditions.LogicalOperator == "OR" && result)
                {
                    return true;
                }
            }

            // If we get here with OR operator, no condition was true
            return conditions.LogicalOperator != "OR";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error evaluating step conditions for step {StepId}", step.Id);
            return false; // Fail safe - if we can't evaluate conditions, don't allow step
        }
    }

    private async Task<WorkflowStep?> GetNextStepForDecisionAsync(WorkflowDefinition definition, WorkflowStep currentStep, string? outcome, CancellationToken cancellationToken)
    {
        // For decision steps, use the outcome to find the next step
        if (!string.IsNullOrEmpty(outcome))
        {
            // Look for a step that matches the outcome
            var outcomeStep = definition.Steps.FirstOrDefault(s => 
                s.Name.Equals(outcome, StringComparison.OrdinalIgnoreCase) ||
                s.Description?.Contains(outcome, StringComparison.OrdinalIgnoreCase) == true);
            
            if (outcomeStep != null)
            {
                return outcomeStep;
            }
        }

        // Fallback to configured next step
        return await GetNextStepDefaultAsync(definition, currentStep, cancellationToken);
    }

    private async Task<WorkflowStep?> GetNextStepForParallelAsync(WorkflowDefinition definition, WorkflowStep currentStep, string? outcome, CancellationToken cancellationToken)
    {
        // For parallel steps, this would need more complex logic to handle parallel execution
        // For now, treat it like a regular step
        return await GetNextStepDefaultAsync(definition, currentStep, cancellationToken);
    }

    private async Task<WorkflowStep?> GetNextStepDefaultAsync(WorkflowDefinition definition, WorkflowStep currentStep, CancellationToken cancellationToken)
    {
        // Use configured next step
        if (currentStep.NextStepId.HasValue)
        {
            return definition.Steps.FirstOrDefault(s => s.Id == currentStep.NextStepId.Value);
        }

        // Find next step by order
        return definition.Steps
            .Where(s => s.Order > currentStep.Order)
            .OrderBy(s => s.Order)
            .FirstOrDefault();
    }

    private bool EvaluateCondition(StepCondition condition, Dictionary<string, object>? variables)
    {
        if (variables == null || !variables.TryGetValue(condition.VariableName, out var value))
        {
            return false;
        }

        var stringValue = value?.ToString() ?? string.Empty;
        var expectedValue = condition.Value?.ToString() ?? string.Empty;

        return condition.Operator.ToLowerInvariant() switch
        {
            "equals" or "==" => string.Equals(stringValue, expectedValue, StringComparison.OrdinalIgnoreCase),
            "not_equals" or "!=" => !string.Equals(stringValue, expectedValue, StringComparison.OrdinalIgnoreCase),
            "contains" => stringValue.Contains(expectedValue, StringComparison.OrdinalIgnoreCase),
            "greater_than" or ">" => CompareNumeric(stringValue, expectedValue) > 0,
            "less_than" or "<" => CompareNumeric(stringValue, expectedValue) < 0,
            "greater_than_or_equal" or ">=" => CompareNumeric(stringValue, expectedValue) >= 0,
            "less_than_or_equal" or "<=" => CompareNumeric(stringValue, expectedValue) <= 0,
            _ => false
        };
    }

    private int CompareNumeric(string left, string right)
    {
        if (double.TryParse(left, out var leftNum) && double.TryParse(right, out var rightNum))
        {
            return leftNum.CompareTo(rightNum);
        }
        
        return string.Compare(left, right, StringComparison.OrdinalIgnoreCase);
    }
}

public class StepConditions
{
    public string LogicalOperator { get; set; } = "AND"; // AND, OR
    public List<StepCondition> Conditions { get; set; } = new();
}

public class StepCondition
{
    public string VariableName { get; set; } = string.Empty;
    public string Operator { get; set; } = string.Empty; // equals, not_equals, greater_than, etc.
    public object? Value { get; set; }
}
