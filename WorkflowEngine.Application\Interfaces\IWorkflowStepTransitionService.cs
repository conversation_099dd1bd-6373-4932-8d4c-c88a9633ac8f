using WorkflowEngine.Domain.Entities;

namespace WorkflowEngine.Application.Interfaces;

public interface IWorkflowStepTransitionService
{
    Task<WorkflowStepTransitionResult> EvaluateTransitionAsync(WorkflowInstance instance, WorkflowStep currentStep, string? outcome = null, CancellationToken cancellationToken = default);
    Task<WorkflowStep?> GetNextStepAsync(WorkflowInstance instance, WorkflowStep currentStep, string? outcome = null, CancellationToken cancellationToken = default);
    Task<bool> CanTransitionAsync(WorkflowInstance instance, WorkflowStep fromStep, WorkflowStep toStep, CancellationToken cancellationToken = default);
    Task<IEnumerable<WorkflowStep>> GetPossibleNextStepsAsync(WorkflowInstance instance, WorkflowStep currentStep, CancellationToken cancellationToken = default);
    Task<bool> EvaluateStepConditionsAsync(WorkflowInstance instance, WorkflowStep step, CancellationToken cancellationToken = default);
}

public class WorkflowStepTransitionResult
{
    public bool CanTransition { get; set; }
    public WorkflowStep? NextStep { get; set; }
    public string? Reason { get; set; }
    public Dictionary<string, object>? TransitionData { get; set; }
    public bool RequiresUserInput { get; set; }
    public DateTime? DelayUntil { get; set; }

    public static WorkflowStepTransitionResult Success(WorkflowStep nextStep, Dictionary<string, object>? transitionData = null)
    {
        return new WorkflowStepTransitionResult
        {
            CanTransition = true,
            NextStep = nextStep,
            TransitionData = transitionData
        };
    }

    public static WorkflowStepTransitionResult NoTransition(string reason)
    {
        return new WorkflowStepTransitionResult
        {
            CanTransition = false,
            Reason = reason
        };
    }

    public static WorkflowStepTransitionResult WaitForInput(string reason)
    {
        return new WorkflowStepTransitionResult
        {
            CanTransition = false,
            RequiresUserInput = true,
            Reason = reason
        };
    }

    public static WorkflowStepTransitionResult Delay(DateTime delayUntil, string reason)
    {
        return new WorkflowStepTransitionResult
        {
            CanTransition = false,
            DelayUntil = delayUntil,
            Reason = reason
        };
    }
}
