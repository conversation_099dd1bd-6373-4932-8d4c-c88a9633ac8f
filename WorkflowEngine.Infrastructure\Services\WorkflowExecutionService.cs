using Microsoft.Extensions.Logging;
using WorkflowEngine.Application.Interfaces;
using WorkflowEngine.Domain.Entities;
using WorkflowEngine.Domain.Enums;
using WorkflowEngine.Infrastructure.Data;

namespace WorkflowEngine.Infrastructure.Services;

public class WorkflowExecutionService : IWorkflowExecutionService
{
    private readonly IWorkflowDefinitionRepository _workflowDefinitionRepository;
    private readonly IWorkflowInstanceRepository _workflowInstanceRepository;
    private readonly IActivityRepository _activityRepository;
    private readonly IActivityExecutionRepository _activityExecutionRepository;
    private readonly IActivityExecutorFactory _activityExecutorFactory;
    private readonly IWorkflowVariableService _variableService;
    private readonly IWorkflowStepTransitionService _transitionService;
    private readonly IRetryPolicyService _retryPolicyService;
    private readonly IDeadLetterQueueService _deadLetterQueueService;
    private readonly ITenantContext _tenantContext;
    private readonly ILogger<WorkflowExecutionService> _logger;

    public WorkflowExecutionService(
        IWorkflowDefinitionRepository workflowDefinitionRepository,
        IWorkflowInstanceRepository workflowInstanceRepository,
        IActivityRepository activityRepository,
        IActivityExecutionRepository activityExecutionRepository,
        IActivityExecutorFactory activityExecutorFactory,
        IWorkflowVariableService variableService,
        IWorkflowStepTransitionService transitionService,
        IRetryPolicyService retryPolicyService,
        IDeadLetterQueueService deadLetterQueueService,
        ITenantContext tenantContext,
        ILogger<WorkflowExecutionService> logger)
    {
        _workflowDefinitionRepository = workflowDefinitionRepository;
        _workflowInstanceRepository = workflowInstanceRepository;
        _activityRepository = activityRepository;
        _activityExecutionRepository = activityExecutionRepository;
        _activityExecutorFactory = activityExecutorFactory;
        _variableService = variableService;
        _transitionService = transitionService;
        _retryPolicyService = retryPolicyService;
        _deadLetterQueueService = deadLetterQueueService;
        _tenantContext = tenantContext;
        _logger = logger;
    }

    public async Task<WorkflowExecutionResult> ExecuteStepAsync(WorkflowInstance instance, WorkflowStep step, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Executing step {StepId} ({StepName}) for instance {InstanceId}", 
                step.Id, step.Name, instance.Id);

            // Get the activity for this step
            var activity = await _activityRepository.GetByIdAsync(_tenantContext.TenantId, step.ActivityId, cancellationToken);
            if (activity == null)
            {
                return WorkflowExecutionResult.Failure($"Activity {step.ActivityId} not found for step {step.Name}");
            }

            // Check if activity is active
            if (!activity.IsActive)
            {
                return WorkflowExecutionResult.Failure($"Activity {activity.Name} is not active");
            }

            // Get activity executor
            var executor = _activityExecutorFactory.GetExecutor(activity.Type);
            if (executor == null)
            {
                return WorkflowExecutionResult.Failure($"No executor found for activity type {activity.Type}");
            }

            // Get workflow variables
            var variables = await _variableService.GetAllVariablesAsync(instance.Id, cancellationToken);

            // Create activity execution record
            var activityExecution = new ActivityExecution
            {
                TenantId = _tenantContext.TenantId,
                WorkflowInstanceId = instance.Id,
                WorkflowStepId = step.Id,
                ActivityId = activity.Id,
                Status = ExecutionStatus.Running,
                InputData = instance.InputData, // Could be enhanced to use step-specific input
                StartedAt = DateTime.UtcNow,
                MaxRetries = 3 // Could be configurable
            };

            await _activityExecutionRepository.AddAsync(activityExecution, cancellationToken);
            await _activityExecutionRepository.SaveChangesAsync(cancellationToken);

            // Create execution context
            var context = new ActivityExecutionContext
            {
                Activity = activity,
                WorkflowInstance = instance,
                WorkflowStep = step,
                InputData = activityExecution.InputData,
                Variables = variables,
                RetryCount = 0,
                CancellationToken = cancellationToken
            };

            // Execute the activity
            var result = await ExecuteActivityAsync(context, cancellationToken);

            // Update activity execution with result
            activityExecution.Status = result.Status;
            activityExecution.OutputData = result.OutputData;
            activityExecution.ErrorMessage = result.ErrorMessage;
            activityExecution.StackTrace = result.StackTrace;
            activityExecution.CompletedAt = DateTime.UtcNow;
            activityExecution.Duration = activityExecution.CompletedAt - activityExecution.StartedAt;

            // Handle retry logic for failed executions
            if (!result.IsSuccess && result.Status == ExecutionStatus.Failed)
            {
                await HandleExecutionFailureAsync(activityExecution, result, cancellationToken);
            }

            await _activityExecutionRepository.UpdateAsync(activityExecution, cancellationToken);
            await _activityExecutionRepository.SaveChangesAsync(cancellationToken);

            // Update workflow variables if provided
            if (result.Variables != null && result.Variables.Any())
            {
                await _variableService.MergeVariablesAsync(instance.Id, result.Variables, cancellationToken);
            }

            // Convert activity result to workflow result
            var workflowResult = new WorkflowExecutionResult
            {
                IsSuccess = result.IsSuccess,
                ErrorMessage = result.ErrorMessage,
                Status = ConvertExecutionStatusToWorkflowStatus(result.Status),
                CurrentStepId = step.Id,
                OutputData = result.OutputData,
                Variables = result.Variables,
                IsCompleted = result.Status == ExecutionStatus.Completed,
                RequiresUserInput = result.RequiresUserInput,
                NextExecutionTime = result.ResumeAt
            };

            _logger.LogInformation("Step {StepId} execution completed with status {Status}", step.Id, result.Status);
            return workflowResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing step {StepId} for instance {InstanceId}", step.Id, instance.Id);
            return WorkflowExecutionResult.Failure($"Step execution failed: {ex.Message}");
        }
    }

    public async Task<ActivityExecutionResult> ExecuteActivityAsync(ActivityExecutionContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            var executor = _activityExecutorFactory.GetExecutor(context.Activity.Type);
            if (executor == null)
            {
                throw new InvalidOperationException($"No executor found for activity type {context.Activity.Type}");
            }

            _logger.LogDebug("Executing activity {ActivityId} ({ActivityType}) for instance {InstanceId}",
                context.Activity.Id, context.Activity.Type, context.WorkflowInstance.Id);

            var result = await executor.ExecuteAsync(context, cancellationToken);

            _logger.LogDebug("Activity {ActivityId} execution completed with status {Status}",
                context.Activity.Id, result.Status);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing activity {ActivityId} for instance {InstanceId}",
                context.Activity.Id, context.WorkflowInstance.Id);

            return ActivityExecutionResult.Failure($"Activity execution failed: {ex.Message}", ex.StackTrace);
        }
    }

    public async Task<WorkflowStep?> GetNextStepAsync(WorkflowInstance instance, WorkflowStep currentStep, string? outcome = null, CancellationToken cancellationToken = default)
    {
        return await _transitionService.GetNextStepAsync(instance, currentStep, outcome, cancellationToken);
    }

    public async Task<bool> ShouldProgressToNextStepAsync(WorkflowInstance instance, WorkflowStep currentStep, CancellationToken cancellationToken = default)
    {
        try
        {
            // Check if current step is completed
            var latestExecution = await _activityExecutionRepository.GetLatestExecutionAsync(
                _tenantContext.TenantId, instance.Id, currentStep.Id, cancellationToken);

            if (latestExecution == null)
            {
                return false; // No execution found, cannot progress
            }

            // Only progress if the step completed successfully
            return latestExecution.Status == ExecutionStatus.Completed;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if should progress for instance {InstanceId}, step {StepId}", 
                instance.Id, currentStep.Id);
            return false;
        }
    }

    public async Task UpdateInstanceProgressAsync(WorkflowInstance instance, WorkflowExecutionResult result, CancellationToken cancellationToken = default)
    {
        try
        {
            // Update instance status
            instance.Status = result.Status;
            instance.CurrentStepId = result.CurrentStepId;
            instance.NextExecutionTime = result.NextExecutionTime;
            instance.ErrorMessage = result.ErrorMessage;

            if (result.IsCompleted)
            {
                instance.CompletedAt = DateTime.UtcNow;
                instance.OutputData = result.OutputData;
            }

            await _workflowInstanceRepository.UpdateAsync(instance, cancellationToken);
            await _workflowInstanceRepository.SaveChangesAsync(cancellationToken);

            _logger.LogDebug("Updated instance {InstanceId} progress: Status={Status}, CurrentStep={CurrentStepId}", 
                instance.Id, instance.Status, instance.CurrentStepId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating instance progress for {InstanceId}", instance.Id);
            throw;
        }
    }

    private static WorkflowInstanceStatus ConvertExecutionStatusToWorkflowStatus(ExecutionStatus executionStatus)
    {
        return executionStatus switch
        {
            ExecutionStatus.Pending => WorkflowInstanceStatus.Suspended,
            ExecutionStatus.Running => WorkflowInstanceStatus.Running,
            ExecutionStatus.Completed => WorkflowInstanceStatus.Running, // Continue to next step
            ExecutionStatus.Failed => WorkflowInstanceStatus.Failed,
            ExecutionStatus.Cancelled => WorkflowInstanceStatus.Cancelled,
            ExecutionStatus.Skipped => WorkflowInstanceStatus.Running,
            ExecutionStatus.Retrying => WorkflowInstanceStatus.Running,
            ExecutionStatus.TimedOut => WorkflowInstanceStatus.TimedOut,
            _ => WorkflowInstanceStatus.Failed
        };
    }

    private async Task HandleExecutionFailureAsync(ActivityExecution execution, ActivityExecutionResult result, CancellationToken cancellationToken)
    {
        try
        {
            // Determine if we should retry based on retry policy
            var retryDecision = await _retryPolicyService.ShouldRetryAsync(execution, null, cancellationToken);

            if (retryDecision.ShouldRetry)
            {
                // Schedule retry
                execution.NextRetryAt = retryDecision.NextRetryAt;
                execution.Status = ExecutionStatus.Pending; // Reset to pending for retry

                _logger.LogInformation("Scheduled retry for execution {ExecutionId} at {NextRetryAt}. Reason: {Reason}",
                    execution.Id, retryDecision.NextRetryAt, retryDecision.Reason);
            }
            else if (retryDecision.MoveToDeadLetter)
            {
                // Move to dead letter queue
                await _deadLetterQueueService.AddToDeadLetterQueueAsync(
                    execution,
                    DeadLetterReason.MaxRetriesExceeded,
                    retryDecision.Reason,
                    cancellationToken);

                _logger.LogWarning("Moved execution {ExecutionId} to dead letter queue. Reason: {Reason}",
                    execution.Id, retryDecision.Reason);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling execution failure for {ExecutionId}", execution.Id);
        }
    }
}
