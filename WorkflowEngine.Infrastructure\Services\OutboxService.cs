using Microsoft.Extensions.Logging;
using System.Text.Json;
using WorkflowEngine.Application.Interfaces;
using WorkflowEngine.Domain.Common;
using WorkflowEngine.Domain.Entities;
using WorkflowEngine.Infrastructure.Data;

namespace WorkflowEngine.Infrastructure.Services;

public class OutboxService : IOutboxService
{
    private readonly IOutboxEventRepository _outboxEventRepository;
    private readonly ITenantContext _tenantContext;
    private readonly ILogger<OutboxService> _logger;
    private readonly JsonSerializerOptions _jsonOptions;

    public OutboxService(
        IOutboxEventRepository outboxEventRepository,
        ITenantContext tenantContext,
        ILogger<OutboxService> logger)
    {
        _outboxEventRepository = outboxEventRepository;
        _tenantContext = tenantContext;
        _logger = logger;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false
        };
    }

    public async Task<OutboxEvent> PublishEventAsync(string eventType, object eventData, string? correlationId = null, string? causationId = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var serializedData = JsonSerializer.Serialize(eventData, _jsonOptions);
            
            var outboxEvent = new OutboxEvent
            {
                TenantId = _tenantContext.TenantId,
                EventType = eventType,
                EventData = serializedData,
                CorrelationId = correlationId ?? Guid.NewGuid().ToString(),
                CausationId = causationId,
                Status = OutboxEventStatus.Pending,
                Priority = 0
            };

            await _outboxEventRepository.AddAsync(outboxEvent, cancellationToken);
            await _outboxEventRepository.SaveChangesAsync(cancellationToken);

            _logger.LogDebug("Published outbox event {EventId} of type {EventType}", outboxEvent.Id, eventType);

            return outboxEvent;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing outbox event of type {EventType}", eventType);
            throw;
        }
    }

    public async Task<OutboxEvent> PublishWorkflowEventAsync(string eventType, object eventData, Guid workflowInstanceId, Guid? workflowStepId = null, Guid? activityExecutionId = null, string? correlationId = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var serializedData = JsonSerializer.Serialize(eventData, _jsonOptions);
            
            var outboxEvent = new OutboxEvent
            {
                TenantId = _tenantContext.TenantId,
                EventType = eventType,
                EventData = serializedData,
                CorrelationId = correlationId ?? Guid.NewGuid().ToString(),
                WorkflowInstanceId = workflowInstanceId,
                WorkflowStepId = workflowStepId,
                ActivityExecutionId = activityExecutionId,
                Status = OutboxEventStatus.Pending,
                Priority = 1 // Higher priority for workflow events
            };

            await _outboxEventRepository.AddAsync(outboxEvent, cancellationToken);
            await _outboxEventRepository.SaveChangesAsync(cancellationToken);

            _logger.LogDebug("Published workflow outbox event {EventId} of type {EventType} for instance {InstanceId}", 
                outboxEvent.Id, eventType, workflowInstanceId);

            return outboxEvent;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing workflow outbox event of type {EventType} for instance {InstanceId}", 
                eventType, workflowInstanceId);
            throw;
        }
    }

    public async Task PublishEventsAsync(IEnumerable<OutboxEvent> events, CancellationToken cancellationToken = default)
    {
        try
        {
            foreach (var outboxEvent in events)
            {
                outboxEvent.TenantId = _tenantContext.TenantId;
                outboxEvent.Status = OutboxEventStatus.Pending;
                
                if (string.IsNullOrEmpty(outboxEvent.CorrelationId))
                {
                    outboxEvent.CorrelationId = Guid.NewGuid().ToString();
                }
            }

            await _outboxEventRepository.AddRangeAsync(events, cancellationToken);
            await _outboxEventRepository.SaveChangesAsync(cancellationToken);

            _logger.LogDebug("Published {EventCount} outbox events", events.Count());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing batch of outbox events");
            throw;
        }
    }

    public async Task<IEnumerable<OutboxEvent>> GetPendingEventsAsync(int maxEvents = 100, CancellationToken cancellationToken = default)
    {
        return await _outboxEventRepository.GetPendingEventsAsync(_tenantContext.TenantId, maxEvents, cancellationToken);
    }

    public async Task<OutboxEvent> ProcessEventAsync(OutboxEvent outboxEvent, CancellationToken cancellationToken = default)
    {
        try
        {
            // Mark as processing
            outboxEvent.Status = OutboxEventStatus.Processing;
            await _outboxEventRepository.UpdateAsync(outboxEvent, cancellationToken);
            await _outboxEventRepository.SaveChangesAsync(cancellationToken);

            _logger.LogDebug("Processing outbox event {EventId} of type {EventType}", outboxEvent.Id, outboxEvent.EventType);

            return outboxEvent;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing outbox event {EventId}", outboxEvent.Id);
            throw;
        }
    }

    public async Task MarkEventAsCompletedAsync(Guid eventId, CancellationToken cancellationToken = default)
    {
        try
        {
            var outboxEvent = await _outboxEventRepository.GetByIdAsync(_tenantContext.TenantId, eventId, cancellationToken);
            if (outboxEvent != null)
            {
                outboxEvent.Status = OutboxEventStatus.Completed;
                outboxEvent.ProcessedAt = DateTime.UtcNow;
                outboxEvent.ErrorMessage = null;
                outboxEvent.StackTrace = null;

                await _outboxEventRepository.UpdateAsync(outboxEvent, cancellationToken);
                await _outboxEventRepository.SaveChangesAsync(cancellationToken);

                _logger.LogDebug("Marked outbox event {EventId} as completed", eventId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error marking outbox event {EventId} as completed", eventId);
            throw;
        }
    }

    public async Task MarkEventAsFailedAsync(Guid eventId, string errorMessage, string? stackTrace = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var outboxEvent = await _outboxEventRepository.GetByIdAsync(_tenantContext.TenantId, eventId, cancellationToken);
            if (outboxEvent != null)
            {
                outboxEvent.Status = OutboxEventStatus.Failed;
                outboxEvent.RetryCount++;
                outboxEvent.ErrorMessage = errorMessage;
                outboxEvent.StackTrace = stackTrace;

                // Schedule retry if under max retries
                if (outboxEvent.RetryCount < outboxEvent.MaxRetries)
                {
                    var retryDelay = TimeSpan.FromMinutes(Math.Pow(2, outboxEvent.RetryCount)); // Exponential backoff
                    outboxEvent.NextRetryAt = DateTime.UtcNow.Add(retryDelay);
                }
                else
                {
                    outboxEvent.NextRetryAt = null;
                }

                await _outboxEventRepository.UpdateAsync(outboxEvent, cancellationToken);
                await _outboxEventRepository.SaveChangesAsync(cancellationToken);

                _logger.LogWarning("Marked outbox event {EventId} as failed. Retry count: {RetryCount}/{MaxRetries}. Error: {Error}",
                    eventId, outboxEvent.RetryCount, outboxEvent.MaxRetries, errorMessage);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error marking outbox event {EventId} as failed", eventId);
            throw;
        }
    }

    public async Task<IEnumerable<OutboxEvent>> GetEventsForRetryAsync(DateTime retryBefore, int maxEvents = 100, CancellationToken cancellationToken = default)
    {
        return await _outboxEventRepository.GetEventsForRetryAsync(_tenantContext.TenantId, retryBefore, maxEvents, cancellationToken);
    }

    public async Task ScheduleRetryAsync(Guid eventId, DateTime nextRetryAt, CancellationToken cancellationToken = default)
    {
        try
        {
            var outboxEvent = await _outboxEventRepository.GetByIdAsync(_tenantContext.TenantId, eventId, cancellationToken);
            if (outboxEvent != null)
            {
                outboxEvent.NextRetryAt = nextRetryAt;
                outboxEvent.Status = OutboxEventStatus.Failed; // Ensure it's in failed state for retry

                await _outboxEventRepository.UpdateAsync(outboxEvent, cancellationToken);
                await _outboxEventRepository.SaveChangesAsync(cancellationToken);

                _logger.LogDebug("Scheduled retry for outbox event {EventId} at {NextRetryAt}", eventId, nextRetryAt);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error scheduling retry for outbox event {EventId}", eventId);
            throw;
        }
    }

    public async Task<OutboxEvent?> GetEventByIdAsync(Guid eventId, CancellationToken cancellationToken = default)
    {
        return await _outboxEventRepository.GetByIdAsync(_tenantContext.TenantId, eventId, cancellationToken);
    }

    public async Task<IEnumerable<OutboxEvent>> GetEventsByCorrelationIdAsync(string correlationId, CancellationToken cancellationToken = default)
    {
        return await _outboxEventRepository.GetEventsByCorrelationIdAsync(_tenantContext.TenantId, correlationId, cancellationToken);
    }

    public async Task<IEnumerable<OutboxEvent>> GetEventsByWorkflowInstanceAsync(Guid workflowInstanceId, CancellationToken cancellationToken = default)
    {
        return await _outboxEventRepository.GetEventsByWorkflowInstanceAsync(_tenantContext.TenantId, workflowInstanceId, cancellationToken);
    }

    public async Task CancelEventAsync(Guid eventId, string reason, CancellationToken cancellationToken = default)
    {
        try
        {
            var outboxEvent = await _outboxEventRepository.GetByIdAsync(_tenantContext.TenantId, eventId, cancellationToken);
            if (outboxEvent != null)
            {
                outboxEvent.Status = OutboxEventStatus.Cancelled;
                outboxEvent.ErrorMessage = $"Cancelled: {reason}";
                outboxEvent.ProcessedAt = DateTime.UtcNow;

                await _outboxEventRepository.UpdateAsync(outboxEvent, cancellationToken);
                await _outboxEventRepository.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("Cancelled outbox event {EventId}. Reason: {Reason}", eventId, reason);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling outbox event {EventId}", eventId);
            throw;
        }
    }
}
