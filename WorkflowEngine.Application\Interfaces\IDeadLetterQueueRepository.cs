using WorkflowEngine.Domain.Entities;
using WorkflowEngine.Domain.Enums;

namespace WorkflowEngine.Application.Interfaces;

public interface IDeadLetterQueueRepository : ITenantRepository<DeadLetterQueue>
{
    Task<IEnumerable<DeadLetterQueue>> GetByStatusAsync(Guid tenantId, DeadLetterStatus status, int maxItems = 100, CancellationToken cancellationToken = default);
    Task<IEnumerable<DeadLetterQueue>> GetByWorkflowInstanceAsync(Guid tenantId, Guid workflowInstanceId, CancellationToken cancellationToken = default);
    Task<IEnumerable<DeadLetterQueue>> GetByReasonAsync(Guid tenantId, DeadLetterReason reason, CancellationToken cancellationToken = default);
    Task<DeadLetterQueueStats> GetStatsAsync(Guid tenantId, CancellationToken cancellationToken = default);
    Task<int> PurgeOldItemsAsync(Guid tenantId, DateTime olderThan, CancellationToken cancellationToken = default);
    Task<IEnumerable<DeadLetterQueue>> GetOldestItemsAsync(Guid tenantId, int count = 10, CancellationToken cancellationToken = default);
}
