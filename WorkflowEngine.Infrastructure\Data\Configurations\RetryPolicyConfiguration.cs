using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WorkflowEngine.Domain.Entities;

namespace WorkflowEngine.Infrastructure.Data.Configurations;

public class RetryPolicyConfiguration : IEntityTypeConfiguration<RetryPolicy>
{
    public void Configure(EntityTypeBuilder<RetryPolicy> builder)
    {
        builder.ToTable("RetryPolicies");

        builder.HasKey(rp => rp.Id);

        builder.Property(rp => rp.Name)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(rp => rp.Description)
            .HasMaxLength(500);

        builder.Property(rp => rp.MaxRetries)
            .IsRequired();

        builder.Property(rp => rp.InitialDelay)
            .IsRequired();

        builder.Property(rp => rp.MaxDelay)
            .IsRequired();

        builder.Property(rp => rp.BackoffStrategy)
            .IsRequired()
            .HasConversion<int>();

        builder.Property(rp => rp.BackoffMultiplier)
            .IsRequired()
            .HasPrecision(5, 2);

        builder.Property(rp => rp.RetryableExceptions)
            .HasColumnType("jsonb");

        builder.Property(rp => rp.NonRetryableExceptions)
            .HasColumnType("jsonb");

        // Indexes
        builder.HasIndex(rp => new { rp.TenantId, rp.Name })
            .IsUnique();

        builder.HasIndex(rp => rp.TenantId);

        // Relationships
        builder.HasOne(rp => rp.Tenant)
            .WithMany()
            .HasForeignKey(rp => rp.TenantId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
