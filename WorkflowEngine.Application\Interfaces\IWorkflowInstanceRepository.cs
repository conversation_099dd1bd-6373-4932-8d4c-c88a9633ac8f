using WorkflowEngine.Domain.Entities;
using WorkflowEngine.Domain.Enums;

namespace WorkflowEngine.Application.Interfaces;

public interface IWorkflowInstanceRepository : ITenantRepository<WorkflowInstance>
{
    Task<IEnumerable<WorkflowInstance>> GetByStatusAsync(Guid tenantId, WorkflowInstanceStatus status, CancellationToken cancellationToken = default);
    Task<IEnumerable<WorkflowInstance>> GetByWorkflowDefinitionAsync(Guid tenantId, Guid workflowDefinitionId, CancellationToken cancellationToken = default);
    Task<IEnumerable<WorkflowInstance>> GetPendingExecutionAsync(DateTime beforeTime, CancellationToken cancellationToken = default);
    Task<IEnumerable<WorkflowInstance>> GetRunningInstancesAsync(Guid tenantId, CancellationToken cancellationToken = default);
    Task<IEnumerable<WorkflowInstance>> GetInstancesForRetryAsync(DateTime beforeTime, CancellationToken cancellationToken = default);
}
