using Microsoft.Extensions.Logging;
using System.Text.Json;
using WorkflowEngine.Application.Interfaces;
using WorkflowEngine.Domain.Entities;
using WorkflowEngine.Domain.Enums;
using WorkflowEngine.Infrastructure.Data;

namespace WorkflowEngine.Infrastructure.Services;

public class WorkflowEngine : IWorkflowEngine
{
    private readonly IWorkflowDefinitionRepository _workflowDefinitionRepository;
    private readonly IWorkflowInstanceRepository _workflowInstanceRepository;
    private readonly IActivityExecutionRepository _activityExecutionRepository;
    private readonly IWorkflowVariableService _variableService;
    private readonly IWorkflowExecutionService _workflowExecutionService;
    private readonly IOutboxService _outboxService;
    private readonly ITenantContext _tenantContext;
    private readonly ILogger<WorkflowEngine> _logger;

    public WorkflowEngine(
        IWorkflowDefinitionRepository workflowDefinitionRepository,
        IWorkflowInstanceRepository workflowInstanceRepository,
        IActivityExecutionRepository activityExecutionRepository,
        IWorkflowVariableService variableService,
        IWorkflowExecutionService workflowExecutionService,
        IOutboxService outboxService,
        ITenantContext tenantContext,
        ILogger<WorkflowEngine> logger)
    {
        _workflowDefinitionRepository = workflowDefinitionRepository;
        _workflowInstanceRepository = workflowInstanceRepository;
        _activityExecutionRepository = activityExecutionRepository;
        _variableService = variableService;
        _workflowExecutionService = workflowExecutionService;
        _outboxService = outboxService;
        _tenantContext = tenantContext;
        _logger = logger;
    }

    public async Task<WorkflowInstance> StartInstanceAsync(Guid workflowDefinitionId, string? inputData = null, string? instanceName = null, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting workflow instance for definition {DefinitionId}", workflowDefinitionId);

        var definition = await _workflowDefinitionRepository.GetByIdAsync(_tenantContext.TenantId, workflowDefinitionId, cancellationToken);
        if (definition == null)
        {
            throw new InvalidOperationException($"Workflow definition {workflowDefinitionId} not found");
        }

        if (!definition.IsActive || definition.Status != WorkflowStatus.Published)
        {
            throw new InvalidOperationException($"Workflow definition {workflowDefinitionId} is not active or published");
        }

        var instance = new WorkflowInstance
        {
            TenantId = _tenantContext.TenantId,
            WorkflowDefinitionId = workflowDefinitionId,
            Name = instanceName ?? $"{definition.Name} - {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss}",
            Status = WorkflowInstanceStatus.NotStarted,
            InputData = inputData,
            Variables = "{}",
            StartedAt = DateTime.UtcNow
        };

        await _workflowInstanceRepository.AddAsync(instance, cancellationToken);
        await _workflowInstanceRepository.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Created workflow instance {InstanceId} for definition {DefinitionId}", instance.Id, workflowDefinitionId);

        // Publish workflow instance started event
        try
        {
            var eventData = new Services.OutboxEventHandlers.WorkflowInstanceStartedEventData
            {
                InstanceId = instance.Id,
                WorkflowDefinitionId = workflowDefinitionId,
                InstanceName = instance.Name,
                StartedBy = _tenantContext.UserId,
                StartedAt = instance.StartedAt ?? DateTime.UtcNow,
                InputData = inputData
            };

            await _outboxService.PublishWorkflowEventAsync("WorkflowInstanceStarted", eventData, instance.Id, cancellationToken: cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to publish workflow instance started event for instance {InstanceId}", instance.Id);
        }

        // Initialize variables from input data
        if (!string.IsNullOrEmpty(inputData))
        {
            try
            {
                var inputVariables = JsonSerializer.Deserialize<Dictionary<string, object>>(inputData);
                if (inputVariables != null)
                {
                    await _variableService.MergeVariablesAsync(instance.Id, inputVariables, cancellationToken);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to parse input data as variables for instance {InstanceId}", instance.Id);
            }
        }

        return instance;
    }

    public async Task<WorkflowInstance> StartInstanceAsync(string workflowName, int? version = null, string? inputData = null, string? instanceName = null, CancellationToken cancellationToken = default)
    {
        var definition = version.HasValue
            ? await _workflowDefinitionRepository.GetByNameAndVersionAsync(_tenantContext.TenantId, workflowName, version.Value, cancellationToken)
            : await _workflowDefinitionRepository.GetLatestVersionAsync(_tenantContext.TenantId, workflowName, cancellationToken);

        if (definition == null)
        {
            throw new InvalidOperationException($"Workflow definition '{workflowName}' version {version?.ToString() ?? "latest"} not found");
        }

        return await StartInstanceAsync(definition.Id, inputData, instanceName, cancellationToken);
    }

    public async Task<WorkflowExecutionResult> ProgressInstanceAsync(Guid instanceId, CancellationToken cancellationToken = default)
    {
        var instance = await GetInstanceAsync(instanceId, cancellationToken);
        if (instance == null)
        {
            return WorkflowExecutionResult.Failure($"Workflow instance {instanceId} not found");
        }

        if (instance.Status != WorkflowInstanceStatus.Running && instance.Status != WorkflowInstanceStatus.NotStarted)
        {
            return WorkflowExecutionResult.Failure($"Cannot progress instance in status {instance.Status}");
        }

        try
        {
            // If not started, find the first step
            if (instance.Status == WorkflowInstanceStatus.NotStarted)
            {
                var definition = await _workflowDefinitionRepository.GetByIdAsync(_tenantContext.TenantId, instance.WorkflowDefinitionId, cancellationToken);
                if (definition?.Steps?.Any() != true)
                {
                    return WorkflowExecutionResult.Failure("Workflow definition has no steps");
                }

                var firstStep = definition.Steps.OrderBy(s => s.Order).First();
                instance.CurrentStepId = firstStep.Id;
                instance.Status = WorkflowInstanceStatus.Running;
                
                await _workflowInstanceRepository.UpdateAsync(instance, cancellationToken);
                await _workflowInstanceRepository.SaveChangesAsync(cancellationToken);
            }

            // Execute current step
            if (instance.CurrentStepId.HasValue)
            {
                return await ExecuteStepAsync(instance, instance.CurrentStepId.Value, cancellationToken);
            }

            return WorkflowExecutionResult.Failure("No current step to execute");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error progressing workflow instance {InstanceId}", instanceId);
            return WorkflowExecutionResult.Failure($"Error progressing workflow: {ex.Message}");
        }
    }

    public async Task<WorkflowExecutionResult> ProgressToNextStepAsync(Guid instanceId, string? outputData = null, CancellationToken cancellationToken = default)
    {
        var instance = await GetInstanceAsync(instanceId, cancellationToken);
        if (instance == null)
        {
            return WorkflowExecutionResult.Failure($"Workflow instance {instanceId} not found");
        }

        if (instance.Status != WorkflowInstanceStatus.Running)
        {
            return WorkflowExecutionResult.Failure($"Cannot progress instance in status {instance.Status}");
        }

        try
        {
            // Get current step
            if (!instance.CurrentStepId.HasValue)
            {
                return WorkflowExecutionResult.Failure("No current step to progress from");
            }

            var definition = await _workflowDefinitionRepository.GetByIdAsync(_tenantContext.TenantId, instance.WorkflowDefinitionId, cancellationToken);
            if (definition?.Steps == null)
            {
                return WorkflowExecutionResult.Failure("Workflow definition or steps not found");
            }

            var currentStep = definition.Steps.FirstOrDefault(s => s.Id == instance.CurrentStepId.Value);
            if (currentStep == null)
            {
                return WorkflowExecutionResult.Failure($"Current step {instance.CurrentStepId} not found");
            }

            // Get next step
            var nextStep = await _workflowExecutionService.GetNextStepAsync(instance, currentStep, outputData, cancellationToken);
            if (nextStep == null)
            {
                // No more steps, complete the workflow
                instance.Status = WorkflowInstanceStatus.Completed;
                instance.CompletedAt = DateTime.UtcNow;
                instance.CurrentStepId = null;

                await _workflowInstanceRepository.UpdateAsync(instance, cancellationToken);
                await _workflowInstanceRepository.SaveChangesAsync(cancellationToken);

                var result = WorkflowExecutionResult.Success(WorkflowInstanceStatus.Completed);
                result.IsCompleted = true;

                // Publish completion event
                try
                {
                    var eventData = new Services.OutboxEventHandlers.WorkflowInstanceCompletedEventData
                    {
                        InstanceId = instance.Id,
                        WorkflowDefinitionId = instance.WorkflowDefinitionId,
                        InstanceName = instance.Name,
                        Status = instance.Status.ToString(),
                        CompletedAt = instance.CompletedAt ?? DateTime.UtcNow,
                        Duration = (instance.CompletedAt ?? DateTime.UtcNow) - (instance.StartedAt ?? DateTime.UtcNow),
                        OutputData = outputData
                    };

                    await _outboxService.PublishWorkflowEventAsync("WorkflowInstanceCompleted", eventData, instance.Id, cancellationToken: cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to publish workflow instance completed event for instance {InstanceId}", instance.Id);
                }

                return result;
            }

            // Progress to next step
            instance.CurrentStepId = nextStep.Id;
            await _workflowInstanceRepository.UpdateAsync(instance, cancellationToken);
            await _workflowInstanceRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Progressed workflow instance {InstanceId} to step {StepId} ({StepName})",
                instance.Id, nextStep.Id, nextStep.Name);

            // Execute the next step
            return await ExecuteStepAsync(instance, nextStep.Id, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error progressing workflow instance {InstanceId} to next step", instanceId);
            return WorkflowExecutionResult.Failure($"Error progressing to next step: {ex.Message}");
        }
    }

    public async Task<WorkflowExecutionResult> CompleteCurrentStepAsync(Guid instanceId, string? outputData = null, CancellationToken cancellationToken = default)
    {
        var instance = await GetInstanceAsync(instanceId, cancellationToken);
        if (instance == null)
        {
            return WorkflowExecutionResult.Failure($"Workflow instance {instanceId} not found");
        }

        if (instance.Status != WorkflowInstanceStatus.Running)
        {
            return WorkflowExecutionResult.Failure($"Cannot complete step for instance in status {instance.Status}");
        }

        try
        {
            // Get current step
            if (!instance.CurrentStepId.HasValue)
            {
                return WorkflowExecutionResult.Failure("No current step to complete");
            }

            var definition = await _workflowDefinitionRepository.GetByIdAsync(_tenantContext.TenantId, instance.WorkflowDefinitionId, cancellationToken);
            if (definition?.Steps == null)
            {
                return WorkflowExecutionResult.Failure("Workflow definition or steps not found");
            }

            var currentStep = definition.Steps.FirstOrDefault(s => s.Id == instance.CurrentStepId.Value);
            if (currentStep == null)
            {
                return WorkflowExecutionResult.Failure($"Current step {instance.CurrentStepId} not found");
            }

            // Mark current step as completed by creating a successful activity execution
            var activityExecution = new ActivityExecution
            {
                TenantId = _tenantContext.TenantId,
                WorkflowInstanceId = instance.Id,
                WorkflowStepId = currentStep.Id,
                ActivityId = currentStep.ActivityId,
                Status = ExecutionStatus.Completed,
                StartedAt = DateTime.UtcNow,
                CompletedAt = DateTime.UtcNow,
                Duration = TimeSpan.Zero,
                OutputData = outputData,
                RetryCount = 0,
                MaxRetries = 0
            };

            await _activityExecutionRepository.AddAsync(activityExecution, cancellationToken);
            await _activityExecutionRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Manually completed step {StepId} ({StepName}) for workflow instance {InstanceId}",
                currentStep.Id, currentStep.Name, instance.Id);

            // Publish step completed event
            try
            {
                var eventData = new Services.OutboxEventHandlers.WorkflowStepCompletedEventData
                {
                    InstanceId = instance.Id,
                    StepId = currentStep.Id,
                    StepName = currentStep.Name,
                    Status = ExecutionStatus.Completed.ToString(),
                    CompletedAt = DateTime.UtcNow,
                    Duration = TimeSpan.Zero,
                    OutputData = outputData
                };

                await _outboxService.PublishWorkflowEventAsync("WorkflowStepCompleted", eventData, instance.Id, currentStep.Id, cancellationToken: cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to publish workflow step completed event for instance {InstanceId}, step {StepId}", instance.Id, currentStep.Id);
            }

            // Progress to next step
            return await ProgressToNextStepAsync(instanceId, outputData, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error completing current step for workflow instance {InstanceId}", instanceId);
            return WorkflowExecutionResult.Failure($"Error completing current step: {ex.Message}");
        }
    }

    public async Task<WorkflowExecutionResult> ResumeInstanceAsync(Guid instanceId, string eventName, string? eventData = null, CancellationToken cancellationToken = default)
    {
        var instance = await GetInstanceAsync(instanceId, cancellationToken);
        if (instance == null)
        {
            return WorkflowExecutionResult.Failure($"Workflow instance {instanceId} not found");
        }

        if (instance.Status != WorkflowInstanceStatus.Suspended && instance.Status != WorkflowInstanceStatus.WaitingForEvent)
        {
            return WorkflowExecutionResult.Failure($"Cannot resume instance in status {instance.Status}");
        }

        try
        {
            _logger.LogInformation("Resuming workflow instance {InstanceId} with event {EventName}", instanceId, eventName);

            // Update instance status to running
            instance.Status = WorkflowInstanceStatus.Running;
            await _workflowInstanceRepository.UpdateAsync(instance, cancellationToken);
            await _workflowInstanceRepository.SaveChangesAsync(cancellationToken);

            // Store event data as variables if provided
            if (!string.IsNullOrEmpty(eventData))
            {
                try
                {
                    var eventVariables = JsonSerializer.Deserialize<Dictionary<string, object>>(eventData);
                    if (eventVariables != null)
                    {
                        // Add event name and data as variables
                        eventVariables[$"event_{eventName}"] = eventData;
                        eventVariables["lastEventName"] = eventName;
                        eventVariables["lastEventData"] = eventData;

                        await _variableService.MergeVariablesAsync(instance.Id, eventVariables, cancellationToken);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to parse event data as variables for instance {InstanceId}", instance.Id);
                }
            }

            // Continue execution from current step
            return await ProgressInstanceAsync(instanceId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resuming workflow instance {InstanceId} with event {EventName}", instanceId, eventName);
            return WorkflowExecutionResult.Failure($"Error resuming workflow: {ex.Message}");
        }
    }

    public async Task<WorkflowExecutionResult> HandleExternalEventAsync(Guid instanceId, string eventName, string? eventData = null, CancellationToken cancellationToken = default)
    {
        var instance = await GetInstanceAsync(instanceId, cancellationToken);
        if (instance == null)
        {
            return WorkflowExecutionResult.Failure($"Workflow instance {instanceId} not found");
        }

        try
        {
            _logger.LogInformation("Handling external event {EventName} for workflow instance {InstanceId}", eventName, instanceId);

            // Store event data as variables
            if (!string.IsNullOrEmpty(eventData))
            {
                try
                {
                    var eventVariables = JsonSerializer.Deserialize<Dictionary<string, object>>(eventData);
                    if (eventVariables != null)
                    {
                        // Add event name and data as variables
                        eventVariables[$"external_event_{eventName}"] = eventData;
                        eventVariables["lastExternalEventName"] = eventName;
                        eventVariables["lastExternalEventData"] = eventData;

                        await _variableService.MergeVariablesAsync(instance.Id, eventVariables, cancellationToken);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to parse external event data as variables for instance {InstanceId}", instance.Id);
                }
            }

            // If instance is waiting for this specific event, resume it
            if (instance.Status == WorkflowInstanceStatus.WaitingForEvent)
            {
                return await ResumeInstanceAsync(instanceId, eventName, eventData, cancellationToken);
            }

            // Otherwise, just store the event data and continue normal execution if running
            if (instance.Status == WorkflowInstanceStatus.Running)
            {
                return await ProgressInstanceAsync(instanceId, cancellationToken);
            }

            var result = WorkflowExecutionResult.Success(WorkflowInstanceStatus.Running);
            result.OutputData = $"External event {eventName} processed and stored for instance {instanceId}";
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling external event {EventName} for workflow instance {InstanceId}", eventName, instanceId);
            return WorkflowExecutionResult.Failure($"Error handling external event: {ex.Message}");
        }
    }

    public async Task<WorkflowInstance> SuspendInstanceAsync(Guid instanceId, string? reason = null, CancellationToken cancellationToken = default)
    {
        var instance = await GetInstanceAsync(instanceId, cancellationToken);
        if (instance == null)
        {
            throw new InvalidOperationException($"Workflow instance {instanceId} not found");
        }

        instance.Status = WorkflowInstanceStatus.Suspended;
        await _workflowInstanceRepository.UpdateAsync(instance, cancellationToken);
        await _workflowInstanceRepository.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Suspended workflow instance {InstanceId}. Reason: {Reason}", instanceId, reason);
        return instance;
    }

    public async Task<WorkflowInstance> CancelInstanceAsync(Guid instanceId, string? reason = null, CancellationToken cancellationToken = default)
    {
        var instance = await GetInstanceAsync(instanceId, cancellationToken);
        if (instance == null)
        {
            throw new InvalidOperationException($"Workflow instance {instanceId} not found");
        }

        instance.Status = WorkflowInstanceStatus.Cancelled;
        instance.CompletedAt = DateTime.UtcNow;
        await _workflowInstanceRepository.UpdateAsync(instance, cancellationToken);
        await _workflowInstanceRepository.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Cancelled workflow instance {InstanceId}. Reason: {Reason}", instanceId, reason);
        return instance;
    }

    public async Task<WorkflowInstance> RetryInstanceAsync(Guid instanceId, CancellationToken cancellationToken = default)
    {
        var instance = await GetInstanceAsync(instanceId, cancellationToken);
        if (instance == null)
        {
            throw new InvalidOperationException($"Workflow instance {instanceId} not found");
        }

        if (instance.Status != WorkflowInstanceStatus.Failed)
        {
            throw new InvalidOperationException($"Cannot retry instance in status {instance.Status}");
        }

        instance.Status = WorkflowInstanceStatus.Running;
        instance.RetryCount++;
        instance.ErrorMessage = null;
        await _workflowInstanceRepository.UpdateAsync(instance, cancellationToken);
        await _workflowInstanceRepository.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Retrying workflow instance {InstanceId} (attempt {RetryCount})", instanceId, instance.RetryCount);
        return instance;
    }

    public async Task<WorkflowInstance> GetInstanceAsync(Guid instanceId, CancellationToken cancellationToken = default)
    {
        var instance = await _workflowInstanceRepository.GetByIdAsync(_tenantContext.TenantId, instanceId, cancellationToken);
        if (instance == null)
        {
            throw new InvalidOperationException($"Workflow instance {instanceId} not found");
        }
        return instance;
    }

    public async Task<WorkflowInstanceStatus> GetInstanceStatusAsync(Guid instanceId, CancellationToken cancellationToken = default)
    {
        var instance = await GetInstanceAsync(instanceId, cancellationToken);
        return instance.Status;
    }

    public async Task<IEnumerable<ActivityExecution>> GetInstanceHistoryAsync(Guid instanceId, CancellationToken cancellationToken = default)
    {
        return await _activityExecutionRepository.GetByWorkflowInstanceAsync(_tenantContext.TenantId, instanceId, cancellationToken);
    }

    public async Task SetInstanceVariableAsync(Guid instanceId, string key, object value, CancellationToken cancellationToken = default)
    {
        await _variableService.SetVariableAsync(instanceId, key, value, cancellationToken);
    }

    public async Task<T?> GetInstanceVariableAsync<T>(Guid instanceId, string key, CancellationToken cancellationToken = default)
    {
        return await _variableService.GetVariableAsync<T>(instanceId, key, cancellationToken);
    }

    public async Task<Dictionary<string, object>> GetInstanceVariablesAsync(Guid instanceId, CancellationToken cancellationToken = default)
    {
        return await _variableService.GetAllVariablesAsync(instanceId, cancellationToken);
    }

    public async Task PersistInstanceStateAsync(Guid instanceId, CancellationToken cancellationToken = default)
    {
        await _workflowInstanceRepository.SaveChangesAsync(cancellationToken);
    }

    public async Task<bool> ValidateInstanceAsync(Guid instanceId, CancellationToken cancellationToken = default)
    {
        try
        {
            var instance = await GetInstanceAsync(instanceId, cancellationToken);
            var definition = await _workflowDefinitionRepository.GetByIdAsync(_tenantContext.TenantId, instance.WorkflowDefinitionId, cancellationToken);
            return definition != null && definition.IsActive;
        }
        catch
        {
            return false;
        }
    }

    private async Task<WorkflowExecutionResult> ExecuteStepAsync(WorkflowInstance instance, Guid stepId, CancellationToken cancellationToken)
    {
        try
        {
            // Get the workflow definition with steps
            var definition = await _workflowDefinitionRepository.GetByIdAsync(_tenantContext.TenantId, instance.WorkflowDefinitionId, cancellationToken);
            if (definition?.Steps == null)
            {
                return WorkflowExecutionResult.Failure("Workflow definition or steps not found");
            }

            // Find the step to execute
            var step = definition.Steps.FirstOrDefault(s => s.Id == stepId);
            if (step == null)
            {
                return WorkflowExecutionResult.Failure($"Step {stepId} not found in workflow definition");
            }

            // Execute the step
            var result = await _workflowExecutionService.ExecuteStepAsync(instance, step, cancellationToken);

            // Update instance progress
            await _workflowExecutionService.UpdateInstanceProgressAsync(instance, result, cancellationToken);

            // If step completed successfully, check if we should progress to next step
            if (result.IsSuccess && result.IsCompleted)
            {
                var shouldProgress = await _workflowExecutionService.ShouldProgressToNextStepAsync(instance, step, cancellationToken);
                if (shouldProgress)
                {
                    var nextStep = await _workflowExecutionService.GetNextStepAsync(instance, step, null, cancellationToken);
                    if (nextStep != null)
                    {
                        // Update current step and continue execution
                        instance.CurrentStepId = nextStep.Id;
                        result.CurrentStepId = nextStep.Id;
                        result.Status = WorkflowInstanceStatus.Running;

                        await _workflowExecutionService.UpdateInstanceProgressAsync(instance, result, cancellationToken);

                        _logger.LogInformation("Progressed workflow instance {InstanceId} to next step {StepId} ({StepName})",
                            instance.Id, nextStep.Id, nextStep.Name);
                    }
                    else
                    {
                        // No more steps, workflow is complete
                        result.Status = WorkflowInstanceStatus.Completed;
                        result.IsCompleted = true;
                        instance.Status = WorkflowInstanceStatus.Completed;
                        instance.CompletedAt = DateTime.UtcNow;

                        await _workflowExecutionService.UpdateInstanceProgressAsync(instance, result, cancellationToken);

                        _logger.LogInformation("Workflow instance {InstanceId} completed successfully", instance.Id);

                        // Publish workflow instance completed event
                        try
                        {
                            var eventData = new Services.OutboxEventHandlers.WorkflowInstanceCompletedEventData
                            {
                                InstanceId = instance.Id,
                                WorkflowDefinitionId = instance.WorkflowDefinitionId,
                                InstanceName = instance.Name,
                                Status = instance.Status.ToString(),
                                CompletedAt = instance.CompletedAt ?? DateTime.UtcNow,
                                Duration = (instance.CompletedAt ?? DateTime.UtcNow) - (instance.StartedAt ?? DateTime.UtcNow),
                                OutputData = result.OutputData,
                                ErrorMessage = result.ErrorMessage
                            };

                            await _outboxService.PublishWorkflowEventAsync("WorkflowInstanceCompleted", eventData, instance.Id, cancellationToken: cancellationToken);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "Failed to publish workflow instance completed event for instance {InstanceId}", instance.Id);
                        }
                    }
                }
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing step {StepId} for instance {InstanceId}", stepId, instance.Id);
            return WorkflowExecutionResult.Failure($"Step execution failed: {ex.Message}");
        }
    }
}
