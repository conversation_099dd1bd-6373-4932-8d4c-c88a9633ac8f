using Microsoft.Extensions.Logging;
using System.Text.Json;
using WorkflowEngine.Application.Interfaces;
using WorkflowEngine.Domain.Entities;
using WorkflowEngine.Domain.Enums;
using WorkflowEngine.Infrastructure.Data;

namespace WorkflowEngine.Infrastructure.Services;

public class WorkflowEngine : IWorkflowEngine
{
    private readonly IWorkflowDefinitionRepository _workflowDefinitionRepository;
    private readonly IWorkflowInstanceRepository _workflowInstanceRepository;
    private readonly IActivityExecutionRepository _activityExecutionRepository;
    private readonly IWorkflowVariableService _variableService;
    private readonly IWorkflowExecutionService _workflowExecutionService;
    private readonly ITenantContext _tenantContext;
    private readonly ILogger<WorkflowEngine> _logger;

    public WorkflowEngine(
        IWorkflowDefinitionRepository workflowDefinitionRepository,
        IWorkflowInstanceRepository workflowInstanceRepository,
        IActivityExecutionRepository activityExecutionRepository,
        IWorkflowVariableService variableService,
        IWorkflowExecutionService workflowExecutionService,
        ITenantContext tenantContext,
        ILogger<WorkflowEngine> logger)
    {
        _workflowDefinitionRepository = workflowDefinitionRepository;
        _workflowInstanceRepository = workflowInstanceRepository;
        _activityExecutionRepository = activityExecutionRepository;
        _variableService = variableService;
        _workflowExecutionService = workflowExecutionService;
        _tenantContext = tenantContext;
        _logger = logger;
    }

    public async Task<WorkflowInstance> StartInstanceAsync(Guid workflowDefinitionId, string? inputData = null, string? instanceName = null, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting workflow instance for definition {DefinitionId}", workflowDefinitionId);

        var definition = await _workflowDefinitionRepository.GetByIdAsync(_tenantContext.TenantId, workflowDefinitionId, cancellationToken);
        if (definition == null)
        {
            throw new InvalidOperationException($"Workflow definition {workflowDefinitionId} not found");
        }

        if (!definition.IsActive || definition.Status != WorkflowStatus.Published)
        {
            throw new InvalidOperationException($"Workflow definition {workflowDefinitionId} is not active or published");
        }

        var instance = new WorkflowInstance
        {
            TenantId = _tenantContext.TenantId,
            WorkflowDefinitionId = workflowDefinitionId,
            Name = instanceName ?? $"{definition.Name} - {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss}",
            Status = WorkflowInstanceStatus.NotStarted,
            InputData = inputData,
            Variables = "{}",
            StartedAt = DateTime.UtcNow
        };

        await _workflowInstanceRepository.AddAsync(instance, cancellationToken);
        await _workflowInstanceRepository.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Created workflow instance {InstanceId} for definition {DefinitionId}", instance.Id, workflowDefinitionId);

        // Initialize variables from input data
        if (!string.IsNullOrEmpty(inputData))
        {
            try
            {
                var inputVariables = JsonSerializer.Deserialize<Dictionary<string, object>>(inputData);
                if (inputVariables != null)
                {
                    await _variableService.MergeVariablesAsync(instance.Id, inputVariables, cancellationToken);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to parse input data as variables for instance {InstanceId}", instance.Id);
            }
        }

        return instance;
    }

    public async Task<WorkflowInstance> StartInstanceAsync(string workflowName, int? version = null, string? inputData = null, string? instanceName = null, CancellationToken cancellationToken = default)
    {
        var definition = version.HasValue
            ? await _workflowDefinitionRepository.GetByNameAndVersionAsync(_tenantContext.TenantId, workflowName, version.Value, cancellationToken)
            : await _workflowDefinitionRepository.GetLatestVersionAsync(_tenantContext.TenantId, workflowName, cancellationToken);

        if (definition == null)
        {
            throw new InvalidOperationException($"Workflow definition '{workflowName}' version {version?.ToString() ?? "latest"} not found");
        }

        return await StartInstanceAsync(definition.Id, inputData, instanceName, cancellationToken);
    }

    public async Task<WorkflowExecutionResult> ProgressInstanceAsync(Guid instanceId, CancellationToken cancellationToken = default)
    {
        var instance = await GetInstanceAsync(instanceId, cancellationToken);
        if (instance == null)
        {
            return WorkflowExecutionResult.Failure($"Workflow instance {instanceId} not found");
        }

        if (instance.Status != WorkflowInstanceStatus.Running && instance.Status != WorkflowInstanceStatus.NotStarted)
        {
            return WorkflowExecutionResult.Failure($"Cannot progress instance in status {instance.Status}");
        }

        try
        {
            // If not started, find the first step
            if (instance.Status == WorkflowInstanceStatus.NotStarted)
            {
                var definition = await _workflowDefinitionRepository.GetByIdAsync(_tenantContext.TenantId, instance.WorkflowDefinitionId, cancellationToken);
                if (definition?.Steps?.Any() != true)
                {
                    return WorkflowExecutionResult.Failure("Workflow definition has no steps");
                }

                var firstStep = definition.Steps.OrderBy(s => s.Order).First();
                instance.CurrentStepId = firstStep.Id;
                instance.Status = WorkflowInstanceStatus.Running;
                
                await _workflowInstanceRepository.UpdateAsync(instance, cancellationToken);
                await _workflowInstanceRepository.SaveChangesAsync(cancellationToken);
            }

            // Execute current step
            if (instance.CurrentStepId.HasValue)
            {
                return await ExecuteStepAsync(instance, instance.CurrentStepId.Value, cancellationToken);
            }

            return WorkflowExecutionResult.Failure("No current step to execute");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error progressing workflow instance {InstanceId}", instanceId);
            return WorkflowExecutionResult.Failure($"Error progressing workflow: {ex.Message}");
        }
    }

    public async Task<WorkflowExecutionResult> ProgressToNextStepAsync(Guid instanceId, string? outputData = null, CancellationToken cancellationToken = default)
    {
        // Implementation will be added in the next part
        return await ProgressInstanceAsync(instanceId, cancellationToken);
    }

    public async Task<WorkflowExecutionResult> CompleteCurrentStepAsync(Guid instanceId, string? outputData = null, CancellationToken cancellationToken = default)
    {
        // Implementation will be added in the next part
        return await ProgressInstanceAsync(instanceId, cancellationToken);
    }

    public async Task<WorkflowExecutionResult> ResumeInstanceAsync(Guid instanceId, string eventName, string? eventData = null, CancellationToken cancellationToken = default)
    {
        // Implementation will be added in the next part
        return await ProgressInstanceAsync(instanceId, cancellationToken);
    }

    public async Task<WorkflowExecutionResult> HandleExternalEventAsync(Guid instanceId, string eventName, string? eventData = null, CancellationToken cancellationToken = default)
    {
        // Implementation will be added in the next part
        return await ProgressInstanceAsync(instanceId, cancellationToken);
    }

    public async Task<WorkflowInstance> SuspendInstanceAsync(Guid instanceId, string? reason = null, CancellationToken cancellationToken = default)
    {
        var instance = await GetInstanceAsync(instanceId, cancellationToken);
        if (instance == null)
        {
            throw new InvalidOperationException($"Workflow instance {instanceId} not found");
        }

        instance.Status = WorkflowInstanceStatus.Suspended;
        await _workflowInstanceRepository.UpdateAsync(instance, cancellationToken);
        await _workflowInstanceRepository.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Suspended workflow instance {InstanceId}. Reason: {Reason}", instanceId, reason);
        return instance;
    }

    public async Task<WorkflowInstance> CancelInstanceAsync(Guid instanceId, string? reason = null, CancellationToken cancellationToken = default)
    {
        var instance = await GetInstanceAsync(instanceId, cancellationToken);
        if (instance == null)
        {
            throw new InvalidOperationException($"Workflow instance {instanceId} not found");
        }

        instance.Status = WorkflowInstanceStatus.Cancelled;
        instance.CompletedAt = DateTime.UtcNow;
        await _workflowInstanceRepository.UpdateAsync(instance, cancellationToken);
        await _workflowInstanceRepository.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Cancelled workflow instance {InstanceId}. Reason: {Reason}", instanceId, reason);
        return instance;
    }

    public async Task<WorkflowInstance> RetryInstanceAsync(Guid instanceId, CancellationToken cancellationToken = default)
    {
        var instance = await GetInstanceAsync(instanceId, cancellationToken);
        if (instance == null)
        {
            throw new InvalidOperationException($"Workflow instance {instanceId} not found");
        }

        if (instance.Status != WorkflowInstanceStatus.Failed)
        {
            throw new InvalidOperationException($"Cannot retry instance in status {instance.Status}");
        }

        instance.Status = WorkflowInstanceStatus.Running;
        instance.RetryCount++;
        instance.ErrorMessage = null;
        await _workflowInstanceRepository.UpdateAsync(instance, cancellationToken);
        await _workflowInstanceRepository.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Retrying workflow instance {InstanceId} (attempt {RetryCount})", instanceId, instance.RetryCount);
        return instance;
    }

    public async Task<WorkflowInstance> GetInstanceAsync(Guid instanceId, CancellationToken cancellationToken = default)
    {
        var instance = await _workflowInstanceRepository.GetByIdAsync(_tenantContext.TenantId, instanceId, cancellationToken);
        if (instance == null)
        {
            throw new InvalidOperationException($"Workflow instance {instanceId} not found");
        }
        return instance;
    }

    public async Task<WorkflowInstanceStatus> GetInstanceStatusAsync(Guid instanceId, CancellationToken cancellationToken = default)
    {
        var instance = await GetInstanceAsync(instanceId, cancellationToken);
        return instance.Status;
    }

    public async Task<IEnumerable<ActivityExecution>> GetInstanceHistoryAsync(Guid instanceId, CancellationToken cancellationToken = default)
    {
        return await _activityExecutionRepository.GetByWorkflowInstanceAsync(_tenantContext.TenantId, instanceId, cancellationToken);
    }

    public async Task SetInstanceVariableAsync(Guid instanceId, string key, object value, CancellationToken cancellationToken = default)
    {
        await _variableService.SetVariableAsync(instanceId, key, value, cancellationToken);
    }

    public async Task<T?> GetInstanceVariableAsync<T>(Guid instanceId, string key, CancellationToken cancellationToken = default)
    {
        return await _variableService.GetVariableAsync<T>(instanceId, key, cancellationToken);
    }

    public async Task<Dictionary<string, object>> GetInstanceVariablesAsync(Guid instanceId, CancellationToken cancellationToken = default)
    {
        return await _variableService.GetAllVariablesAsync(instanceId, cancellationToken);
    }

    public async Task PersistInstanceStateAsync(Guid instanceId, CancellationToken cancellationToken = default)
    {
        await _workflowInstanceRepository.SaveChangesAsync(cancellationToken);
    }

    public async Task<bool> ValidateInstanceAsync(Guid instanceId, CancellationToken cancellationToken = default)
    {
        try
        {
            var instance = await GetInstanceAsync(instanceId, cancellationToken);
            var definition = await _workflowDefinitionRepository.GetByIdAsync(_tenantContext.TenantId, instance.WorkflowDefinitionId, cancellationToken);
            return definition != null && definition.IsActive;
        }
        catch
        {
            return false;
        }
    }

    private async Task<WorkflowExecutionResult> ExecuteStepAsync(WorkflowInstance instance, Guid stepId, CancellationToken cancellationToken)
    {
        try
        {
            // Get the workflow definition with steps
            var definition = await _workflowDefinitionRepository.GetByIdAsync(_tenantContext.TenantId, instance.WorkflowDefinitionId, cancellationToken);
            if (definition?.Steps == null)
            {
                return WorkflowExecutionResult.Failure("Workflow definition or steps not found");
            }

            // Find the step to execute
            var step = definition.Steps.FirstOrDefault(s => s.Id == stepId);
            if (step == null)
            {
                return WorkflowExecutionResult.Failure($"Step {stepId} not found in workflow definition");
            }

            // Execute the step
            var result = await _workflowExecutionService.ExecuteStepAsync(instance, step, cancellationToken);

            // Update instance progress
            await _workflowExecutionService.UpdateInstanceProgressAsync(instance, result, cancellationToken);

            // If step completed successfully, check if we should progress to next step
            if (result.IsSuccess && result.IsCompleted)
            {
                var shouldProgress = await _workflowExecutionService.ShouldProgressToNextStepAsync(instance, step, cancellationToken);
                if (shouldProgress)
                {
                    var nextStep = await _workflowExecutionService.GetNextStepAsync(instance, step, null, cancellationToken);
                    if (nextStep != null)
                    {
                        // Update current step and continue execution
                        instance.CurrentStepId = nextStep.Id;
                        result.CurrentStepId = nextStep.Id;
                        result.Status = WorkflowInstanceStatus.Running;

                        await _workflowExecutionService.UpdateInstanceProgressAsync(instance, result, cancellationToken);

                        _logger.LogInformation("Progressed workflow instance {InstanceId} to next step {StepId} ({StepName})",
                            instance.Id, nextStep.Id, nextStep.Name);
                    }
                    else
                    {
                        // No more steps, workflow is complete
                        result.Status = WorkflowInstanceStatus.Completed;
                        result.IsCompleted = true;
                        instance.Status = WorkflowInstanceStatus.Completed;
                        instance.CompletedAt = DateTime.UtcNow;

                        await _workflowExecutionService.UpdateInstanceProgressAsync(instance, result, cancellationToken);

                        _logger.LogInformation("Workflow instance {InstanceId} completed successfully", instance.Id);
                    }
                }
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing step {StepId} for instance {InstanceId}", stepId, instance.Id);
            return WorkflowExecutionResult.Failure($"Step execution failed: {ex.Message}");
        }
    }
}
