using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System.Text.Json;
using WorkflowEngine.Domain.Entities;

namespace WorkflowEngine.Infrastructure.Data.Configurations;

public class OutboxEventConfiguration : IEntityTypeConfiguration<OutboxEvent>
{
    public void Configure(EntityTypeBuilder<OutboxEvent> builder)
    {
        builder.ToTable("OutboxEvents");

        builder.HasKey(e => e.Id);

        builder.Property(e => e.EventType)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(e => e.EventData)
            .IsRequired()
            .HasColumnType("jsonb");

        builder.Property(e => e.CorrelationId)
            .HasMaxLength(100);

        builder.Property(e => e.CausationId)
            .HasMaxLength(100);

        builder.Property(e => e.Status)
            .HasConversion<int>()
            .HasDefaultValue(OutboxEventStatus.Pending);

        builder.Property(e => e.RetryCount)
            .HasDefaultValue(0);

        builder.Property(e => e.MaxRetries)
            .HasDefaultValue(3);

        builder.Property(e => e.Priority)
            .HasDefaultValue(0);

        builder.Property(e => e.ErrorMessage)
            .HasMaxLength(2000);

        builder.Property(e => e.StackTrace)
            .HasColumnType("text");

        builder.Property(e => e.Headers)
            .HasConversion(
                v => v == null ? null : JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => v == null ? null : JsonSerializer.Deserialize<Dictionary<string, string>>(v, (JsonSerializerOptions?)null))
            .HasColumnType("jsonb");

        // Indexes for performance
        builder.HasIndex(e => new { e.TenantId, e.Status })
            .HasDatabaseName("IX_OutboxEvents_TenantId_Status");

        builder.HasIndex(e => new { e.TenantId, e.Status, e.Priority, e.CreatedAt })
            .HasDatabaseName("IX_OutboxEvents_TenantId_Status_Priority_CreatedAt");

        builder.HasIndex(e => new { e.TenantId, e.NextRetryAt })
            .HasDatabaseName("IX_OutboxEvents_TenantId_NextRetryAt")
            .HasFilter("NextRetryAt IS NOT NULL");

        builder.HasIndex(e => new { e.TenantId, e.CorrelationId })
            .HasDatabaseName("IX_OutboxEvents_TenantId_CorrelationId")
            .HasFilter("CorrelationId IS NOT NULL");

        builder.HasIndex(e => new { e.TenantId, e.WorkflowInstanceId })
            .HasDatabaseName("IX_OutboxEvents_TenantId_WorkflowInstanceId")
            .HasFilter("WorkflowInstanceId IS NOT NULL");

        builder.HasIndex(e => new { e.TenantId, e.EventType })
            .HasDatabaseName("IX_OutboxEvents_TenantId_EventType");

        // Relationships
        builder.HasOne(e => e.Tenant)
            .WithMany()
            .HasForeignKey(e => e.TenantId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(e => e.WorkflowInstance)
            .WithMany()
            .HasForeignKey(e => e.WorkflowInstanceId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(e => e.WorkflowStep)
            .WithMany()
            .HasForeignKey(e => e.WorkflowStepId)
            .OnDelete(DeleteBehavior.SetNull);

        builder.HasOne(e => e.ActivityExecution)
            .WithMany()
            .HasForeignKey(e => e.ActivityExecutionId)
            .OnDelete(DeleteBehavior.SetNull);
    }
}
