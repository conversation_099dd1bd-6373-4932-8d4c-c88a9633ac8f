using FluentAssertions;
using WorkflowEngine.Domain.Entities;
using WorkflowEngine.Domain.Enums;

namespace WorkflowEngine.Tests.Domain.Entities;

public class WorkflowDefinitionTests
{
    [Fact]
    public void WorkflowDefinition_Should_Initialize_With_Default_Values()
    {
        // Act
        var definition = new WorkflowDefinition();

        // Assert
        definition.Id.Should().NotBeEmpty();
        definition.TenantId.Should().BeEmpty();
        definition.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        definition.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        definition.Name.Should().BeEmpty();
        definition.Description.Should().BeNull();
        definition.Version.Should().Be(1);
        definition.Status.Should().Be(WorkflowStatus.Draft);
        definition.JsonDefinition.Should().BeNull();
        definition.IsActive.Should().BeTrue();
        definition.Tags.Should().BeNull();
        definition.Steps.Should().NotBeNull().And.BeEmpty();
        definition.Instances.Should().NotBeNull().And.BeEmpty();
    }

    [Fact]
    public void WorkflowDefinition_Should_Allow_Setting_Properties()
    {
        // Arrange
        var definition = new WorkflowDefinition();
        var tenantId = Guid.NewGuid();

        // Act
        definition.TenantId = tenantId;
        definition.Name = "Test Workflow";
        definition.Description = "Test Description";
        definition.Version = 2;
        definition.Status = WorkflowStatus.Published;
        definition.JsonDefinition = "{}";
        definition.IsActive = false;
        definition.Tags = "tag1,tag2";

        // Assert
        definition.TenantId.Should().Be(tenantId);
        definition.Name.Should().Be("Test Workflow");
        definition.Description.Should().Be("Test Description");
        definition.Version.Should().Be(2);
        definition.Status.Should().Be(WorkflowStatus.Published);
        definition.JsonDefinition.Should().Be("{}");
        definition.IsActive.Should().BeFalse();
        definition.Tags.Should().Be("tag1,tag2");
    }

    [Theory]
    [InlineData(WorkflowStatus.Draft)]
    [InlineData(WorkflowStatus.Published)]
    [InlineData(WorkflowStatus.Deprecated)]
    [InlineData(WorkflowStatus.Archived)]
    public void WorkflowDefinition_Should_Accept_All_Status_Values(WorkflowStatus status)
    {
        // Arrange
        var definition = new WorkflowDefinition();

        // Act
        definition.Status = status;

        // Assert
        definition.Status.Should().Be(status);
    }

    [Theory]
    [InlineData(1)]
    [InlineData(10)]
    [InlineData(100)]
    public void WorkflowDefinition_Should_Accept_Positive_Version_Numbers(int version)
    {
        // Arrange
        var definition = new WorkflowDefinition();

        // Act
        definition.Version = version;

        // Assert
        definition.Version.Should().Be(version);
    }

    [Fact]
    public void WorkflowDefinition_Should_Maintain_Separate_Collections()
    {
        // Arrange
        var definition1 = new WorkflowDefinition();
        var definition2 = new WorkflowDefinition();

        // Act
        definition1.Steps.Add(new WorkflowStep { Name = "Step1" });
        definition2.Instances.Add(new WorkflowInstance { Name = "Instance1" });

        // Assert
        definition1.Steps.Should().HaveCount(1);
        definition1.Instances.Should().BeEmpty();
        definition2.Steps.Should().BeEmpty();
        definition2.Instances.Should().HaveCount(1);
    }
}
