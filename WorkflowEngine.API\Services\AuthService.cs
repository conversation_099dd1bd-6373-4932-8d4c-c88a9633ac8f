using Microsoft.EntityFrameworkCore;
using System.Security.Cryptography;
using System.Text;
using WorkflowEngine.API.Models.Auth;
using WorkflowEngine.Domain.Entities;
using WorkflowEngine.Infrastructure.Data;

namespace WorkflowEngine.API.Services;

public class AuthService : IAuthService
{
    private readonly WorkflowDbContext _context;
    private readonly IJwtService _jwtService;
    private readonly ILogger<AuthService> _logger;

    public AuthService(WorkflowDbContext context, IJwtService jwtService, ILogger<AuthService> logger)
    {
        _context = context;
        _jwtService = jwtService;
        _logger = logger;
    }

    public async Task<LoginResponse> LoginAsync(LoginRequest request)
    {
        var user = await _context.Users
            .Include(u => u.Tenant)
            .FirstOrDefaultAsync(u => u.Email.ToLower() == request.Email.ToLower());

        if (user == null || !VerifyPassword(request.Password, user.PasswordHash))
        {
            throw new UnauthorizedAccessException("Invalid email or password");
        }

        if (!user.IsActive)
        {
            throw new UnauthorizedAccessException("User account is disabled");
        }

        if (!user.Tenant.IsActive)
        {
            throw new UnauthorizedAccessException("Tenant account is disabled");
        }

        // If tenant ID is specified in request, validate it matches user's tenant
        if (!string.IsNullOrEmpty(request.TenantId) && 
            Guid.TryParse(request.TenantId, out var requestedTenantId) &&
            requestedTenantId != user.TenantId)
        {
            throw new UnauthorizedAccessException("Invalid tenant for user");
        }

        var roles = await GetUserRolesAsync(user.Id);
        var token = _jwtService.GenerateToken(user, user.Tenant, roles);
        var refreshToken = _jwtService.GenerateRefreshToken();

        // Store refresh token
        user.RefreshToken = refreshToken;
        user.RefreshTokenExpiryTime = DateTime.UtcNow.AddDays(7);
        user.LastLoginAt = DateTime.UtcNow;
        await _context.SaveChangesAsync();

        return new LoginResponse
        {
            Token = token,
            RefreshToken = refreshToken,
            ExpiresAt = _jwtService.GetTokenExpiration(token),
            User = new UserInfo
            {
                Id = user.Id,
                Email = user.Email,
                FirstName = user.FirstName,
                LastName = user.LastName,
                Roles = roles
            },
            Tenant = new TenantInfo
            {
                Id = user.Tenant.Id,
                Name = user.Tenant.Name,
                Slug = user.Tenant.Slug,
                IsActive = user.Tenant.IsActive
            }
        };
    }

    public async Task<LoginResponse> RegisterAsync(RegisterRequest request)
    {
        // Check if user already exists
        var existingUser = await _context.Users
            .FirstOrDefaultAsync(u => u.Email.ToLower() == request.Email.ToLower());

        if (existingUser != null)
        {
            throw new InvalidOperationException("User with this email already exists");
        }

        // Check if tenant slug is available
        var existingTenant = await _context.Tenants
            .FirstOrDefaultAsync(t => t.Slug.ToLower() == request.TenantSlug.ToLower());

        if (existingTenant != null)
        {
            throw new InvalidOperationException("Tenant slug is already taken");
        }

        using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            // Create tenant
            var tenant = new Tenant
            {
                Id = Guid.NewGuid(),
                Name = request.TenantName,
                Slug = request.TenantSlug.ToLower(),
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                Settings = new Dictionary<string, object>()
            };

            _context.Tenants.Add(tenant);
            await _context.SaveChangesAsync();

            // Create user
            var user = new User
            {
                Id = Guid.NewGuid(),
                TenantId = tenant.Id,
                Email = request.Email.ToLower(),
                FirstName = request.FirstName,
                LastName = request.LastName,
                PasswordHash = HashPassword(request.Password),
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _context.Users.Add(user);
            await _context.SaveChangesAsync();

            // Assign admin role to the first user of the tenant
            var adminRole = await _context.Roles
                .FirstOrDefaultAsync(r => r.Name == "Admin" && r.TenantId == tenant.Id);

            if (adminRole == null)
            {
                adminRole = new Role
                {
                    Id = Guid.NewGuid(),
                    TenantId = tenant.Id,
                    Name = "Admin",
                    Description = "Administrator role with full access",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };
                _context.Roles.Add(adminRole);
                await _context.SaveChangesAsync();
            }

            var userRole = new UserRole
            {
                UserId = user.Id,
                RoleId = adminRole.Id,
                CreatedAt = DateTime.UtcNow
            };

            _context.UserRoles.Add(userRole);
            await _context.SaveChangesAsync();

            await transaction.CommitAsync();

            // Generate tokens
            var roles = new List<string> { "Admin" };
            var token = _jwtService.GenerateToken(user, tenant, roles);
            var refreshToken = _jwtService.GenerateRefreshToken();

            // Store refresh token
            user.RefreshToken = refreshToken;
            user.RefreshTokenExpiryTime = DateTime.UtcNow.AddDays(7);
            user.LastLoginAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();

            return new LoginResponse
            {
                Token = token,
                RefreshToken = refreshToken,
                ExpiresAt = _jwtService.GetTokenExpiration(token),
                User = new UserInfo
                {
                    Id = user.Id,
                    Email = user.Email,
                    FirstName = user.FirstName,
                    LastName = user.LastName,
                    Roles = roles
                },
                Tenant = new TenantInfo
                {
                    Id = tenant.Id,
                    Name = tenant.Name,
                    Slug = tenant.Slug,
                    IsActive = tenant.IsActive
                }
            };
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }

    public async Task<LoginResponse> RefreshTokenAsync(string refreshToken)
    {
        var user = await _context.Users
            .Include(u => u.Tenant)
            .FirstOrDefaultAsync(u => u.RefreshToken == refreshToken);

        if (user == null || user.RefreshTokenExpiryTime <= DateTime.UtcNow)
        {
            throw new UnauthorizedAccessException("Invalid or expired refresh token");
        }

        var roles = await GetUserRolesAsync(user.Id);
        var newToken = _jwtService.GenerateToken(user, user.Tenant, roles);
        var newRefreshToken = _jwtService.GenerateRefreshToken();

        user.RefreshToken = newRefreshToken;
        user.RefreshTokenExpiryTime = DateTime.UtcNow.AddDays(7);
        await _context.SaveChangesAsync();

        return new LoginResponse
        {
            Token = newToken,
            RefreshToken = newRefreshToken,
            ExpiresAt = _jwtService.GetTokenExpiration(newToken),
            User = new UserInfo
            {
                Id = user.Id,
                Email = user.Email,
                FirstName = user.FirstName,
                LastName = user.LastName,
                Roles = roles
            },
            Tenant = new TenantInfo
            {
                Id = user.Tenant.Id,
                Name = user.Tenant.Name,
                Slug = user.Tenant.Slug,
                IsActive = user.Tenant.IsActive
            }
        };
    }

    public async Task<bool> RevokeTokenAsync(string refreshToken)
    {
        var user = await _context.Users
            .FirstOrDefaultAsync(u => u.RefreshToken == refreshToken);

        if (user == null)
        {
            return false;
        }

        user.RefreshToken = null;
        user.RefreshTokenExpiryTime = null;
        await _context.SaveChangesAsync();

        return true;
    }

    public async Task<bool> ValidateUserAsync(string email, string password)
    {
        var user = await _context.Users
            .FirstOrDefaultAsync(u => u.Email.ToLower() == email.ToLower());

        return user != null && VerifyPassword(password, user.PasswordHash) && user.IsActive;
    }

    private async Task<List<string>> GetUserRolesAsync(Guid userId)
    {
        return await _context.UserRoles
            .Where(ur => ur.UserId == userId)
            .Include(ur => ur.Role)
            .Select(ur => ur.Role.Name)
            .ToListAsync();
    }

    private static string HashPassword(string password)
    {
        using var sha256 = SHA256.Create();
        var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password + "WorkflowEngine_Salt"));
        return Convert.ToBase64String(hashedBytes);
    }

    private static bool VerifyPassword(string password, string hash)
    {
        var hashedPassword = HashPassword(password);
        return hashedPassword == hash;
    }
}
