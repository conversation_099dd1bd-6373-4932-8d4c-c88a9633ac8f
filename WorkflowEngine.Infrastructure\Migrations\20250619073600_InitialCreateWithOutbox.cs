﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WorkflowEngine.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreateWithOutbox : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "RetryPolicyId",
                table: "WorkflowDefinitions",
                type: "uuid",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "RetryPolicyId",
                table: "Activities",
                type: "uuid",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "DeadLetterQueue",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    WorkflowInstanceId = table.Column<Guid>(type: "uuid", nullable: false),
                    WorkflowStepId = table.Column<Guid>(type: "uuid", nullable: true),
                    ActivityId = table.Column<Guid>(type: "uuid", nullable: true),
                    ActivityExecutionId = table.Column<Guid>(type: "uuid", nullable: true),
                    Reason = table.Column<int>(type: "integer", nullable: false),
                    ErrorMessage = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    StackTrace = table.Column<string>(type: "text", nullable: true),
                    InputData = table.Column<string>(type: "jsonb", nullable: true),
                    Configuration = table.Column<string>(type: "jsonb", nullable: true),
                    FailedAttempts = table.Column<int>(type: "integer", nullable: false),
                    FirstFailedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    LastFailedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    ProcessedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ProcessedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    Resolution = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    Notes = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    UpdatedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DeadLetterQueue", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DeadLetterQueue_Activities_ActivityId",
                        column: x => x.ActivityId,
                        principalTable: "Activities",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_DeadLetterQueue_ActivityExecutions_ActivityExecutionId",
                        column: x => x.ActivityExecutionId,
                        principalTable: "ActivityExecutions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_DeadLetterQueue_Tenants_TenantId",
                        column: x => x.TenantId,
                        principalTable: "Tenants",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_DeadLetterQueue_WorkflowInstances_WorkflowInstanceId",
                        column: x => x.WorkflowInstanceId,
                        principalTable: "WorkflowInstances",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_DeadLetterQueue_WorkflowSteps_WorkflowStepId",
                        column: x => x.WorkflowStepId,
                        principalTable: "WorkflowSteps",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "OutboxEvents",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    EventType = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    EventData = table.Column<string>(type: "jsonb", nullable: false),
                    CorrelationId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    CausationId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    WorkflowInstanceId = table.Column<Guid>(type: "uuid", nullable: true),
                    WorkflowStepId = table.Column<Guid>(type: "uuid", nullable: true),
                    ActivityExecutionId = table.Column<Guid>(type: "uuid", nullable: true),
                    Status = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    RetryCount = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    MaxRetries = table.Column<int>(type: "integer", nullable: false, defaultValue: 3),
                    NextRetryAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ProcessedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ErrorMessage = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    StackTrace = table.Column<string>(type: "text", nullable: true),
                    Headers = table.Column<string>(type: "jsonb", nullable: true),
                    Priority = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    UpdatedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OutboxEvents", x => x.Id);
                    table.ForeignKey(
                        name: "FK_OutboxEvents_ActivityExecutions_ActivityExecutionId",
                        column: x => x.ActivityExecutionId,
                        principalTable: "ActivityExecutions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_OutboxEvents_Tenants_TenantId",
                        column: x => x.TenantId,
                        principalTable: "Tenants",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_OutboxEvents_WorkflowInstances_WorkflowInstanceId",
                        column: x => x.WorkflowInstanceId,
                        principalTable: "WorkflowInstances",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_OutboxEvents_WorkflowSteps_WorkflowStepId",
                        column: x => x.WorkflowStepId,
                        principalTable: "WorkflowSteps",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "RetryPolicies",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    MaxRetries = table.Column<int>(type: "integer", nullable: false),
                    InitialDelay = table.Column<TimeSpan>(type: "interval", nullable: false),
                    MaxDelay = table.Column<TimeSpan>(type: "interval", nullable: false),
                    BackoffStrategy = table.Column<int>(type: "integer", nullable: false),
                    BackoffMultiplier = table.Column<double>(type: "double precision", precision: 5, scale: 2, nullable: false),
                    RetryOnTimeout = table.Column<bool>(type: "boolean", nullable: false),
                    RetryOnHttpError = table.Column<bool>(type: "boolean", nullable: false),
                    RetryOnSystemError = table.Column<bool>(type: "boolean", nullable: false),
                    RetryableExceptions = table.Column<string>(type: "jsonb", nullable: true),
                    NonRetryableExceptions = table.Column<string>(type: "jsonb", nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    UpdatedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RetryPolicies", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RetryPolicies_Tenants_TenantId",
                        column: x => x.TenantId,
                        principalTable: "Tenants",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_WorkflowDefinitions_RetryPolicyId",
                table: "WorkflowDefinitions",
                column: "RetryPolicyId");

            migrationBuilder.CreateIndex(
                name: "IX_Activities_RetryPolicyId",
                table: "Activities",
                column: "RetryPolicyId");

            migrationBuilder.CreateIndex(
                name: "IX_DeadLetterQueue_ActivityExecutionId",
                table: "DeadLetterQueue",
                column: "ActivityExecutionId");

            migrationBuilder.CreateIndex(
                name: "IX_DeadLetterQueue_ActivityId",
                table: "DeadLetterQueue",
                column: "ActivityId");

            migrationBuilder.CreateIndex(
                name: "IX_DeadLetterQueue_CreatedAt",
                table: "DeadLetterQueue",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_DeadLetterQueue_TenantId_Reason",
                table: "DeadLetterQueue",
                columns: new[] { "TenantId", "Reason" });

            migrationBuilder.CreateIndex(
                name: "IX_DeadLetterQueue_TenantId_Status",
                table: "DeadLetterQueue",
                columns: new[] { "TenantId", "Status" });

            migrationBuilder.CreateIndex(
                name: "IX_DeadLetterQueue_TenantId_WorkflowInstanceId",
                table: "DeadLetterQueue",
                columns: new[] { "TenantId", "WorkflowInstanceId" });

            migrationBuilder.CreateIndex(
                name: "IX_DeadLetterQueue_WorkflowInstanceId",
                table: "DeadLetterQueue",
                column: "WorkflowInstanceId");

            migrationBuilder.CreateIndex(
                name: "IX_DeadLetterQueue_WorkflowStepId",
                table: "DeadLetterQueue",
                column: "WorkflowStepId");

            migrationBuilder.CreateIndex(
                name: "IX_OutboxEvents_ActivityExecutionId",
                table: "OutboxEvents",
                column: "ActivityExecutionId");

            migrationBuilder.CreateIndex(
                name: "IX_OutboxEvents_TenantId_CorrelationId",
                table: "OutboxEvents",
                columns: new[] { "TenantId", "CorrelationId" },
                filter: "\"CorrelationId\" IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_OutboxEvents_TenantId_EventType",
                table: "OutboxEvents",
                columns: new[] { "TenantId", "EventType" });

            migrationBuilder.CreateIndex(
                name: "IX_OutboxEvents_TenantId_NextRetryAt",
                table: "OutboxEvents",
                columns: new[] { "TenantId", "NextRetryAt" },
                filter: "\"NextRetryAt\" IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_OutboxEvents_TenantId_Status",
                table: "OutboxEvents",
                columns: new[] { "TenantId", "Status" });

            migrationBuilder.CreateIndex(
                name: "IX_OutboxEvents_TenantId_Status_Priority_CreatedAt",
                table: "OutboxEvents",
                columns: new[] { "TenantId", "Status", "Priority", "CreatedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_OutboxEvents_TenantId_WorkflowInstanceId",
                table: "OutboxEvents",
                columns: new[] { "TenantId", "WorkflowInstanceId" },
                filter: "\"WorkflowInstanceId\" IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_OutboxEvents_WorkflowInstanceId",
                table: "OutboxEvents",
                column: "WorkflowInstanceId");

            migrationBuilder.CreateIndex(
                name: "IX_OutboxEvents_WorkflowStepId",
                table: "OutboxEvents",
                column: "WorkflowStepId");

            migrationBuilder.CreateIndex(
                name: "IX_RetryPolicies_TenantId",
                table: "RetryPolicies",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_RetryPolicies_TenantId_Name",
                table: "RetryPolicies",
                columns: new[] { "TenantId", "Name" },
                unique: true);

            migrationBuilder.AddForeignKey(
                name: "FK_Activities_RetryPolicies_RetryPolicyId",
                table: "Activities",
                column: "RetryPolicyId",
                principalTable: "RetryPolicies",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_WorkflowDefinitions_RetryPolicies_RetryPolicyId",
                table: "WorkflowDefinitions",
                column: "RetryPolicyId",
                principalTable: "RetryPolicies",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Activities_RetryPolicies_RetryPolicyId",
                table: "Activities");

            migrationBuilder.DropForeignKey(
                name: "FK_WorkflowDefinitions_RetryPolicies_RetryPolicyId",
                table: "WorkflowDefinitions");

            migrationBuilder.DropTable(
                name: "DeadLetterQueue");

            migrationBuilder.DropTable(
                name: "OutboxEvents");

            migrationBuilder.DropTable(
                name: "RetryPolicies");

            migrationBuilder.DropIndex(
                name: "IX_WorkflowDefinitions_RetryPolicyId",
                table: "WorkflowDefinitions");

            migrationBuilder.DropIndex(
                name: "IX_Activities_RetryPolicyId",
                table: "Activities");

            migrationBuilder.DropColumn(
                name: "RetryPolicyId",
                table: "WorkflowDefinitions");

            migrationBuilder.DropColumn(
                name: "RetryPolicyId",
                table: "Activities");
        }
    }
}
