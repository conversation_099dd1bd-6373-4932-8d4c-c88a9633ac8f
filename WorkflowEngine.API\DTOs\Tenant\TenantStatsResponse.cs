namespace WorkflowEngine.API.DTOs.Tenant;

public class TenantStatsResponse
{
    public Guid TenantId { get; set; }
    public string TenantName { get; set; } = string.Empty;
    public string TenantSlug { get; set; } = string.Empty;
    
    // User Statistics
    public int TotalUsers { get; set; }
    public int ActiveUsers { get; set; }
    public int InactiveUsers { get; set; }
    
    // Workflow Statistics
    public int TotalWorkflowDefinitions { get; set; }
    public int ActiveWorkflowDefinitions { get; set; }
    public int TotalWorkflowInstances { get; set; }
    public int RunningWorkflowInstances { get; set; }
    public int CompletedWorkflowInstances { get; set; }
    public int FailedWorkflowInstances { get; set; }
    public int SuspendedWorkflowInstances { get; set; }
    
    // Activity Statistics
    public int TotalActivities { get; set; }
    public int TotalActivityExecutions { get; set; }
    public int SuccessfulActivityExecutions { get; set; }
    public int FailedActivityExecutions { get; set; }
    
    // Performance Metrics
    public double AverageWorkflowDuration { get; set; }
    public double WorkflowSuccessRate { get; set; }
    public double ActivitySuccessRate { get; set; }
    
    // Storage Statistics
    public long TotalStorageUsed { get; set; }
    public int DeadLetterQueueCount { get; set; }
    public int OutboxEventCount { get; set; }
    
    // Time Range
    public DateTime StatsPeriodStart { get; set; }
    public DateTime StatsPeriodEnd { get; set; }
    public DateTime GeneratedAt { get; set; }
}
