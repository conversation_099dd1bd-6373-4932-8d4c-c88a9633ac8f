﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using WorkflowEngine.Infrastructure.Data;

#nullable disable

namespace WorkflowEngine.Infrastructure.Migrations
{
    [DbContext(typeof(WorkflowDbContext))]
    partial class WorkflowDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.11")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("WorkflowEngine.Domain.Entities.Activity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AssemblyName")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("ClassName")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Configuration")
                        .HasColumnType("jsonb");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("InputSchema")
                        .HasColumnType("jsonb");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("OutputSchema")
                        .HasColumnType("jsonb");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<int>("Type")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(7);

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<string>("Version")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasDefaultValue("1.0.0");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("TenantId", "Name", "Version")
                        .IsUnique();

                    b.ToTable("Activities", (string)null);
                });

            modelBuilder.Entity("WorkflowEngine.Domain.Entities.ActivityExecution", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("ActivityId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CompletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<TimeSpan?>("Duration")
                        .HasColumnType("interval");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("InputData")
                        .HasColumnType("jsonb");

                    b.Property<int>("MaxRetries")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("NextRetryAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("OutputData")
                        .HasColumnType("jsonb");

                    b.Property<int>("RetryCount")
                        .HasColumnType("integer");

                    b.Property<string>("StackTrace")
                        .HasMaxLength(4000)
                        .HasColumnType("character varying(4000)");

                    b.Property<DateTime?>("StartedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Status")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<Guid>("WorkflowInstanceId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("WorkflowStepId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("ActivityId");

                    b.HasIndex("NextRetryAt");

                    b.HasIndex("Status");

                    b.HasIndex("TenantId");

                    b.HasIndex("WorkflowInstanceId");

                    b.HasIndex("WorkflowStepId");

                    b.ToTable("ActivityExecutions", (string)null);
                });

            modelBuilder.Entity("WorkflowEngine.Domain.Entities.Tenant", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ConnectionString")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("Settings")
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("Tenants", (string)null);
                });

            modelBuilder.Entity("WorkflowEngine.Domain.Entities.WorkflowDefinition", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("JsonDefinition")
                        .HasColumnType("jsonb");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<int>("Status")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<string>("Tags")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<int>("Version")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("TenantId", "Name", "Version")
                        .IsUnique();

                    b.ToTable("WorkflowDefinitions", (string)null);
                });

            modelBuilder.Entity("WorkflowEngine.Domain.Entities.WorkflowInstance", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CompletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<Guid?>("CurrentStepId")
                        .HasColumnType("uuid");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("InputData")
                        .HasColumnType("jsonb");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<DateTime?>("NextExecutionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("OutputData")
                        .HasColumnType("jsonb");

                    b.Property<int>("RetryCount")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("StartedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Status")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<string>("Variables")
                        .HasColumnType("jsonb");

                    b.Property<Guid>("WorkflowDefinitionId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("CurrentStepId");

                    b.HasIndex("NextExecutionTime");

                    b.HasIndex("Status");

                    b.HasIndex("TenantId");

                    b.HasIndex("WorkflowDefinitionId");

                    b.ToTable("WorkflowInstances", (string)null);
                });

            modelBuilder.Entity("WorkflowEngine.Domain.Entities.WorkflowStep", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("ActivityId")
                        .HasColumnType("uuid");

                    b.Property<string>("Conditions")
                        .HasColumnType("jsonb");

                    b.Property<string>("Configuration")
                        .HasColumnType("jsonb");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<Guid?>("FailureStepId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsRequired")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<Guid?>("NextStepId")
                        .HasColumnType("uuid");

                    b.Property<int>("Order")
                        .HasColumnType("integer");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<int>("TimeoutSeconds")
                        .HasColumnType("integer");

                    b.Property<int>("Type")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1);

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<Guid>("WorkflowDefinitionId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("ActivityId");

                    b.HasIndex("FailureStepId");

                    b.HasIndex("NextStepId");

                    b.HasIndex("TenantId");

                    b.HasIndex("WorkflowDefinitionId");

                    b.HasIndex("WorkflowDefinitionId", "Order");

                    b.ToTable("WorkflowSteps", (string)null);
                });

            modelBuilder.Entity("WorkflowEngine.Domain.Entities.Activity", b =>
                {
                    b.HasOne("WorkflowEngine.Domain.Entities.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("WorkflowEngine.Domain.Entities.ActivityExecution", b =>
                {
                    b.HasOne("WorkflowEngine.Domain.Entities.Activity", "Activity")
                        .WithMany("ActivityExecutions")
                        .HasForeignKey("ActivityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WorkflowEngine.Domain.Entities.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WorkflowEngine.Domain.Entities.WorkflowInstance", "WorkflowInstance")
                        .WithMany("ActivityExecutions")
                        .HasForeignKey("WorkflowInstanceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WorkflowEngine.Domain.Entities.WorkflowStep", "WorkflowStep")
                        .WithMany("ActivityExecutions")
                        .HasForeignKey("WorkflowStepId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Activity");

                    b.Navigation("Tenant");

                    b.Navigation("WorkflowInstance");

                    b.Navigation("WorkflowStep");
                });

            modelBuilder.Entity("WorkflowEngine.Domain.Entities.WorkflowDefinition", b =>
                {
                    b.HasOne("WorkflowEngine.Domain.Entities.Tenant", "Tenant")
                        .WithMany("WorkflowDefinitions")
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("WorkflowEngine.Domain.Entities.WorkflowInstance", b =>
                {
                    b.HasOne("WorkflowEngine.Domain.Entities.WorkflowStep", "CurrentStep")
                        .WithMany()
                        .HasForeignKey("CurrentStepId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("WorkflowEngine.Domain.Entities.Tenant", "Tenant")
                        .WithMany("WorkflowInstances")
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WorkflowEngine.Domain.Entities.WorkflowDefinition", "WorkflowDefinition")
                        .WithMany("Instances")
                        .HasForeignKey("WorkflowDefinitionId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("CurrentStep");

                    b.Navigation("Tenant");

                    b.Navigation("WorkflowDefinition");
                });

            modelBuilder.Entity("WorkflowEngine.Domain.Entities.WorkflowStep", b =>
                {
                    b.HasOne("WorkflowEngine.Domain.Entities.Activity", "Activity")
                        .WithMany("WorkflowSteps")
                        .HasForeignKey("ActivityId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("WorkflowEngine.Domain.Entities.WorkflowStep", "FailureStep")
                        .WithMany("FailurePreviousSteps")
                        .HasForeignKey("FailureStepId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("WorkflowEngine.Domain.Entities.WorkflowStep", "NextStep")
                        .WithMany("PreviousSteps")
                        .HasForeignKey("NextStepId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("WorkflowEngine.Domain.Entities.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WorkflowEngine.Domain.Entities.WorkflowDefinition", "WorkflowDefinition")
                        .WithMany("Steps")
                        .HasForeignKey("WorkflowDefinitionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Activity");

                    b.Navigation("FailureStep");

                    b.Navigation("NextStep");

                    b.Navigation("Tenant");

                    b.Navigation("WorkflowDefinition");
                });

            modelBuilder.Entity("WorkflowEngine.Domain.Entities.Activity", b =>
                {
                    b.Navigation("ActivityExecutions");

                    b.Navigation("WorkflowSteps");
                });

            modelBuilder.Entity("WorkflowEngine.Domain.Entities.Tenant", b =>
                {
                    b.Navigation("WorkflowDefinitions");

                    b.Navigation("WorkflowInstances");
                });

            modelBuilder.Entity("WorkflowEngine.Domain.Entities.WorkflowDefinition", b =>
                {
                    b.Navigation("Instances");

                    b.Navigation("Steps");
                });

            modelBuilder.Entity("WorkflowEngine.Domain.Entities.WorkflowInstance", b =>
                {
                    b.Navigation("ActivityExecutions");
                });

            modelBuilder.Entity("WorkflowEngine.Domain.Entities.WorkflowStep", b =>
                {
                    b.Navigation("ActivityExecutions");

                    b.Navigation("FailurePreviousSteps");

                    b.Navigation("PreviousSteps");
                });
#pragma warning restore 612, 618
        }
    }
}
