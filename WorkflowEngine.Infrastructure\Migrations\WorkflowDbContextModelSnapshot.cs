﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using WorkflowEngine.Infrastructure.Data;

#nullable disable

namespace WorkflowEngine.Infrastructure.Migrations
{
    [DbContext(typeof(WorkflowDbContext))]
    partial class WorkflowDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.6")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("WorkflowEngine.Domain.Entities.Activity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AssemblyName")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("ClassName")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Configuration")
                        .HasColumnType("jsonb");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("InputSchema")
                        .HasColumnType("jsonb");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("OutputSchema")
                        .HasColumnType("jsonb");

                    b.Property<Guid?>("RetryPolicyId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<int>("Type")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(7);

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<string>("Version")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasDefaultValue("1.0.0");

                    b.HasKey("Id");

                    b.HasIndex("RetryPolicyId");

                    b.HasIndex("TenantId");

                    b.HasIndex("TenantId", "Name", "Version")
                        .IsUnique();

                    b.ToTable("Activities", (string)null);
                });

            modelBuilder.Entity("WorkflowEngine.Domain.Entities.ActivityExecution", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("ActivityId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CompletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<TimeSpan?>("Duration")
                        .HasColumnType("interval");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("InputData")
                        .HasColumnType("jsonb");

                    b.Property<int>("MaxRetries")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("NextRetryAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("OutputData")
                        .HasColumnType("jsonb");

                    b.Property<int>("RetryCount")
                        .HasColumnType("integer");

                    b.Property<string>("StackTrace")
                        .HasMaxLength(4000)
                        .HasColumnType("character varying(4000)");

                    b.Property<DateTime?>("StartedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Status")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<Guid>("WorkflowInstanceId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("WorkflowStepId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("ActivityId");

                    b.HasIndex("NextRetryAt");

                    b.HasIndex("Status");

                    b.HasIndex("TenantId");

                    b.HasIndex("WorkflowInstanceId");

                    b.HasIndex("WorkflowStepId");

                    b.ToTable("ActivityExecutions", (string)null);
                });

            modelBuilder.Entity("WorkflowEngine.Domain.Entities.DeadLetterQueue", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ActivityExecutionId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ActivityId")
                        .HasColumnType("uuid");

                    b.Property<string>("Configuration")
                        .HasColumnType("jsonb");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<int>("FailedAttempts")
                        .HasColumnType("integer");

                    b.Property<DateTime>("FirstFailedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("InputData")
                        .HasColumnType("jsonb");

                    b.Property<DateTime>("LastFailedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<DateTime?>("ProcessedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ProcessedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("Reason")
                        .HasColumnType("integer");

                    b.Property<string>("Resolution")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("StackTrace")
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<Guid>("WorkflowInstanceId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("WorkflowStepId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("ActivityExecutionId");

                    b.HasIndex("ActivityId");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("WorkflowInstanceId");

                    b.HasIndex("WorkflowStepId");

                    b.HasIndex("TenantId", "Reason");

                    b.HasIndex("TenantId", "Status");

                    b.HasIndex("TenantId", "WorkflowInstanceId");

                    b.ToTable("DeadLetterQueue", (string)null);
                });

            modelBuilder.Entity("WorkflowEngine.Domain.Entities.OutboxEvent", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ActivityExecutionId")
                        .HasColumnType("uuid");

                    b.Property<string>("CausationId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("CorrelationId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("EventData")
                        .IsRequired()
                        .HasColumnType("jsonb");

                    b.Property<string>("EventType")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("Headers")
                        .HasColumnType("jsonb");

                    b.Property<int>("MaxRetries")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(3);

                    b.Property<DateTime?>("NextRetryAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Priority")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<DateTime?>("ProcessedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("RetryCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<string>("StackTrace")
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<Guid?>("WorkflowInstanceId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("WorkflowStepId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("ActivityExecutionId");

                    b.HasIndex("WorkflowInstanceId");

                    b.HasIndex("WorkflowStepId");

                    b.HasIndex("TenantId", "CorrelationId")
                        .HasDatabaseName("IX_OutboxEvents_TenantId_CorrelationId")
                        .HasFilter("\"CorrelationId\" IS NOT NULL");

                    b.HasIndex("TenantId", "EventType")
                        .HasDatabaseName("IX_OutboxEvents_TenantId_EventType");

                    b.HasIndex("TenantId", "NextRetryAt")
                        .HasDatabaseName("IX_OutboxEvents_TenantId_NextRetryAt")
                        .HasFilter("\"NextRetryAt\" IS NOT NULL");

                    b.HasIndex("TenantId", "Status")
                        .HasDatabaseName("IX_OutboxEvents_TenantId_Status");

                    b.HasIndex("TenantId", "WorkflowInstanceId")
                        .HasDatabaseName("IX_OutboxEvents_TenantId_WorkflowInstanceId")
                        .HasFilter("\"WorkflowInstanceId\" IS NOT NULL");

                    b.HasIndex("TenantId", "Status", "Priority", "CreatedAt")
                        .HasDatabaseName("IX_OutboxEvents_TenantId_Status_Priority_CreatedAt");

                    b.ToTable("OutboxEvents", (string)null);
                });

            modelBuilder.Entity("WorkflowEngine.Domain.Entities.RetryPolicy", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<double>("BackoffMultiplier")
                        .HasPrecision(5, 2)
                        .HasColumnType("double precision");

                    b.Property<int>("BackoffStrategy")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<TimeSpan>("InitialDelay")
                        .HasColumnType("interval");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<TimeSpan>("MaxDelay")
                        .HasColumnType("interval");

                    b.Property<int>("MaxRetries")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("NonRetryableExceptions")
                        .HasColumnType("jsonb");

                    b.Property<bool>("RetryOnHttpError")
                        .HasColumnType("boolean");

                    b.Property<bool>("RetryOnSystemError")
                        .HasColumnType("boolean");

                    b.Property<bool>("RetryOnTimeout")
                        .HasColumnType("boolean");

                    b.Property<string>("RetryableExceptions")
                        .HasColumnType("jsonb");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("TenantId", "Name")
                        .IsUnique();

                    b.ToTable("RetryPolicies", (string)null);
                });

            modelBuilder.Entity("WorkflowEngine.Domain.Entities.Tenant", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ConnectionString")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("Settings")
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("Tenants", (string)null);
                });

            modelBuilder.Entity("WorkflowEngine.Domain.Entities.WorkflowDefinition", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("JsonDefinition")
                        .HasColumnType("jsonb");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<Guid?>("RetryPolicyId")
                        .HasColumnType("uuid");

                    b.Property<int>("Status")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<string>("Tags")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<int>("Version")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("RetryPolicyId");

                    b.HasIndex("TenantId");

                    b.HasIndex("TenantId", "Name", "Version")
                        .IsUnique();

                    b.ToTable("WorkflowDefinitions", (string)null);
                });

            modelBuilder.Entity("WorkflowEngine.Domain.Entities.WorkflowInstance", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CompletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<Guid?>("CurrentStepId")
                        .HasColumnType("uuid");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("InputData")
                        .HasColumnType("jsonb");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<DateTime?>("NextExecutionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("OutputData")
                        .HasColumnType("jsonb");

                    b.Property<int>("RetryCount")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("StartedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Status")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<string>("Variables")
                        .HasColumnType("jsonb");

                    b.Property<Guid>("WorkflowDefinitionId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("CurrentStepId");

                    b.HasIndex("NextExecutionTime");

                    b.HasIndex("Status");

                    b.HasIndex("TenantId");

                    b.HasIndex("WorkflowDefinitionId");

                    b.ToTable("WorkflowInstances", (string)null);
                });

            modelBuilder.Entity("WorkflowEngine.Domain.Entities.WorkflowStep", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("ActivityId")
                        .HasColumnType("uuid");

                    b.Property<string>("Conditions")
                        .HasColumnType("jsonb");

                    b.Property<string>("Configuration")
                        .HasColumnType("jsonb");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<Guid?>("FailureStepId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsRequired")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<Guid?>("NextStepId")
                        .HasColumnType("uuid");

                    b.Property<int>("Order")
                        .HasColumnType("integer");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<int>("TimeoutSeconds")
                        .HasColumnType("integer");

                    b.Property<int>("Type")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1);

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<Guid>("WorkflowDefinitionId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("ActivityId");

                    b.HasIndex("FailureStepId");

                    b.HasIndex("NextStepId");

                    b.HasIndex("TenantId");

                    b.HasIndex("WorkflowDefinitionId");

                    b.HasIndex("WorkflowDefinitionId", "Order");

                    b.ToTable("WorkflowSteps", (string)null);
                });

            modelBuilder.Entity("WorkflowEngine.Domain.Entities.Activity", b =>
                {
                    b.HasOne("WorkflowEngine.Domain.Entities.RetryPolicy", null)
                        .WithMany("Activities")
                        .HasForeignKey("RetryPolicyId");

                    b.HasOne("WorkflowEngine.Domain.Entities.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("WorkflowEngine.Domain.Entities.ActivityExecution", b =>
                {
                    b.HasOne("WorkflowEngine.Domain.Entities.Activity", "Activity")
                        .WithMany("ActivityExecutions")
                        .HasForeignKey("ActivityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WorkflowEngine.Domain.Entities.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WorkflowEngine.Domain.Entities.WorkflowInstance", "WorkflowInstance")
                        .WithMany("ActivityExecutions")
                        .HasForeignKey("WorkflowInstanceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WorkflowEngine.Domain.Entities.WorkflowStep", "WorkflowStep")
                        .WithMany("ActivityExecutions")
                        .HasForeignKey("WorkflowStepId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Activity");

                    b.Navigation("Tenant");

                    b.Navigation("WorkflowInstance");

                    b.Navigation("WorkflowStep");
                });

            modelBuilder.Entity("WorkflowEngine.Domain.Entities.DeadLetterQueue", b =>
                {
                    b.HasOne("WorkflowEngine.Domain.Entities.ActivityExecution", "ActivityExecution")
                        .WithMany()
                        .HasForeignKey("ActivityExecutionId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("WorkflowEngine.Domain.Entities.Activity", "Activity")
                        .WithMany()
                        .HasForeignKey("ActivityId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("WorkflowEngine.Domain.Entities.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WorkflowEngine.Domain.Entities.WorkflowInstance", "WorkflowInstance")
                        .WithMany()
                        .HasForeignKey("WorkflowInstanceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WorkflowEngine.Domain.Entities.WorkflowStep", "WorkflowStep")
                        .WithMany()
                        .HasForeignKey("WorkflowStepId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Activity");

                    b.Navigation("ActivityExecution");

                    b.Navigation("Tenant");

                    b.Navigation("WorkflowInstance");

                    b.Navigation("WorkflowStep");
                });

            modelBuilder.Entity("WorkflowEngine.Domain.Entities.OutboxEvent", b =>
                {
                    b.HasOne("WorkflowEngine.Domain.Entities.ActivityExecution", "ActivityExecution")
                        .WithMany()
                        .HasForeignKey("ActivityExecutionId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("WorkflowEngine.Domain.Entities.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WorkflowEngine.Domain.Entities.WorkflowInstance", "WorkflowInstance")
                        .WithMany()
                        .HasForeignKey("WorkflowInstanceId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("WorkflowEngine.Domain.Entities.WorkflowStep", "WorkflowStep")
                        .WithMany()
                        .HasForeignKey("WorkflowStepId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("ActivityExecution");

                    b.Navigation("Tenant");

                    b.Navigation("WorkflowInstance");

                    b.Navigation("WorkflowStep");
                });

            modelBuilder.Entity("WorkflowEngine.Domain.Entities.RetryPolicy", b =>
                {
                    b.HasOne("WorkflowEngine.Domain.Entities.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("WorkflowEngine.Domain.Entities.WorkflowDefinition", b =>
                {
                    b.HasOne("WorkflowEngine.Domain.Entities.RetryPolicy", null)
                        .WithMany("WorkflowDefinitions")
                        .HasForeignKey("RetryPolicyId");

                    b.HasOne("WorkflowEngine.Domain.Entities.Tenant", "Tenant")
                        .WithMany("WorkflowDefinitions")
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("WorkflowEngine.Domain.Entities.WorkflowInstance", b =>
                {
                    b.HasOne("WorkflowEngine.Domain.Entities.WorkflowStep", "CurrentStep")
                        .WithMany()
                        .HasForeignKey("CurrentStepId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("WorkflowEngine.Domain.Entities.Tenant", "Tenant")
                        .WithMany("WorkflowInstances")
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WorkflowEngine.Domain.Entities.WorkflowDefinition", "WorkflowDefinition")
                        .WithMany("Instances")
                        .HasForeignKey("WorkflowDefinitionId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("CurrentStep");

                    b.Navigation("Tenant");

                    b.Navigation("WorkflowDefinition");
                });

            modelBuilder.Entity("WorkflowEngine.Domain.Entities.WorkflowStep", b =>
                {
                    b.HasOne("WorkflowEngine.Domain.Entities.Activity", "Activity")
                        .WithMany("WorkflowSteps")
                        .HasForeignKey("ActivityId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("WorkflowEngine.Domain.Entities.WorkflowStep", "FailureStep")
                        .WithMany("FailurePreviousSteps")
                        .HasForeignKey("FailureStepId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("WorkflowEngine.Domain.Entities.WorkflowStep", "NextStep")
                        .WithMany("PreviousSteps")
                        .HasForeignKey("NextStepId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("WorkflowEngine.Domain.Entities.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WorkflowEngine.Domain.Entities.WorkflowDefinition", "WorkflowDefinition")
                        .WithMany("Steps")
                        .HasForeignKey("WorkflowDefinitionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Activity");

                    b.Navigation("FailureStep");

                    b.Navigation("NextStep");

                    b.Navigation("Tenant");

                    b.Navigation("WorkflowDefinition");
                });

            modelBuilder.Entity("WorkflowEngine.Domain.Entities.Activity", b =>
                {
                    b.Navigation("ActivityExecutions");

                    b.Navigation("WorkflowSteps");
                });

            modelBuilder.Entity("WorkflowEngine.Domain.Entities.RetryPolicy", b =>
                {
                    b.Navigation("Activities");

                    b.Navigation("WorkflowDefinitions");
                });

            modelBuilder.Entity("WorkflowEngine.Domain.Entities.Tenant", b =>
                {
                    b.Navigation("WorkflowDefinitions");

                    b.Navigation("WorkflowInstances");
                });

            modelBuilder.Entity("WorkflowEngine.Domain.Entities.WorkflowDefinition", b =>
                {
                    b.Navigation("Instances");

                    b.Navigation("Steps");
                });

            modelBuilder.Entity("WorkflowEngine.Domain.Entities.WorkflowInstance", b =>
                {
                    b.Navigation("ActivityExecutions");
                });

            modelBuilder.Entity("WorkflowEngine.Domain.Entities.WorkflowStep", b =>
                {
                    b.Navigation("ActivityExecutions");

                    b.Navigation("FailurePreviousSteps");

                    b.Navigation("PreviousSteps");
                });
#pragma warning restore 612, 618
        }
    }
}
