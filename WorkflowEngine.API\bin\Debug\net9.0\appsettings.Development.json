{"ConnectionStrings": {"DefaultConnection": "Host=localhost;Database=WorkflowEngineDB;Username=********;Password=********;Port=5432"}, "Jwt": {"SecretKey": "WorkflowEngine_DevelopmentKey_NotForProduction_32Chars", "Issuer": "WorkflowEngine.API", "Audience": "WorkflowEngine.Client", "ExpirationMinutes": 1440}, "Logging": {"LogLevel": {"Default": "Debug", "Microsoft.AspNetCore": "Information", "Microsoft.EntityFrameworkCore.Database.Command": "Information", "WorkflowEngine": "Debug"}}}