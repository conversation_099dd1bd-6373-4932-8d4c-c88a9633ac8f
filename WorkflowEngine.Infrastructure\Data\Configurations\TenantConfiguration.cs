using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WorkflowEngine.Domain.Entities;
using System.Text.Json;

namespace WorkflowEngine.Infrastructure.Data.Configurations;

public class TenantConfiguration : IEntityTypeConfiguration<Tenant>
{
    public void Configure(EntityTypeBuilder<Tenant> builder)
    {
        builder.ToTable("Tenants");

        builder.HasKey(t => t.Id);

        builder.Property(t => t.Name)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(t => t.Slug)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(t => t.Description)
            .HasMaxLength(1000);

        builder.Property(t => t.ConnectionString)
            .HasMaxLength(500);

        builder.Property(t => t.Settings)
            .HasConversion(
                v => v == null ? null : JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => v == null ? null : JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null));

        builder.HasIndex(t => t.Name)
            .IsUnique();

        builder.HasIndex(t => t.Slug)
            .IsUnique();

        // Navigation properties
        builder.HasMany(t => t.WorkflowDefinitions)
            .WithOne(wd => wd.Tenant)
            .HasForeignKey(wd => wd.TenantId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(t => t.WorkflowInstances)
            .WithOne(wi => wi.Tenant)
            .HasForeignKey(wi => wi.TenantId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
