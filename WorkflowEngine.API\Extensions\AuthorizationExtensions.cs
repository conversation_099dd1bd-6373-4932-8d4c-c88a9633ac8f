using Microsoft.AspNetCore.Authorization;
using WorkflowEngine.Domain.Constants;

namespace WorkflowEngine.API.Extensions;

/// <summary>
/// Extension methods for configuring authorization policies using centralized permission constants
/// </summary>
public static class AuthorizationExtensions
{
    /// <summary>
    /// Adds all authorization policies using permission constants
    /// </summary>
    public static IServiceCollection AddWorkflowEngineAuthorization(this IServiceCollection services)
    {
        services.AddAuthorization(options =>
        {
            // System policies
            options.AddPolicy("RequireSystemAdmin", policy => 
                policy.RequireClaim("permission", PermissionConstants.System.Admin));
            options.AddPolicy("RequireSystemView", policy => 
                policy.RequireClaim("permission", PermissionConstants.System.View));
            options.AddPolicy("RequireSystemConfigure", policy => 
                policy.RequireClaim("permission", PermissionConstants.System.Configure));
            options.AddPolicy("RequireSystemMonitor", policy => 
                policy.RequireClaim("permission", PermissionConstants.System.Monitor));

            // Tenant policies
            options.AddPolicy("RequireTenantCreate", policy => 
                policy.RequireClaim("permission", PermissionConstants.Tenant.Create));
            options.AddPolicy("RequireTenantRead", policy => 
                policy.RequireClaim("permission", PermissionConstants.Tenant.Read));
            options.AddPolicy("RequireTenantUpdate", policy => 
                policy.RequireClaim("permission", PermissionConstants.Tenant.Update));
            options.AddPolicy("RequireTenantDelete", policy => 
                policy.RequireClaim("permission", PermissionConstants.Tenant.Delete));
            options.AddPolicy("RequireTenantManage", policy => 
                policy.RequireClaim("permission", PermissionConstants.Tenant.Manage));
            options.AddPolicy("RequireTenantActivate", policy => 
                policy.RequireClaim("permission", PermissionConstants.Tenant.Activate));
            options.AddPolicy("RequireTenantDeactivate", policy => 
                policy.RequireClaim("permission", PermissionConstants.Tenant.Deactivate));
            options.AddPolicy("RequireTenantViewStats", policy => 
                policy.RequireClaim("permission", PermissionConstants.Tenant.ViewStats));

            // User policies
            options.AddPolicy("RequireUserCreate", policy => 
                policy.RequireClaim("permission", PermissionConstants.User.Create));
            options.AddPolicy("RequireUserRead", policy => 
                policy.RequireClaim("permission", PermissionConstants.User.Read));
            options.AddPolicy("RequireUserUpdate", policy => 
                policy.RequireClaim("permission", PermissionConstants.User.Update));
            options.AddPolicy("RequireUserDelete", policy => 
                policy.RequireClaim("permission", PermissionConstants.User.Delete));
            options.AddPolicy("RequireUserManage", policy => 
                policy.RequireClaim("permission", PermissionConstants.User.Manage));
            options.AddPolicy("RequireUserChangePassword", policy => 
                policy.RequireClaim("permission", PermissionConstants.User.ChangePassword));
            options.AddPolicy("RequireUserResetPassword", policy => 
                policy.RequireClaim("permission", PermissionConstants.User.ResetPassword));
            options.AddPolicy("RequireUserViewProfile", policy => 
                policy.RequireClaim("permission", PermissionConstants.User.ViewProfile));

            // Role policies
            options.AddPolicy("RequireRoleCreate", policy => 
                policy.RequireClaim("permission", PermissionConstants.Role.Create));
            options.AddPolicy("RequireRoleRead", policy => 
                policy.RequireClaim("permission", PermissionConstants.Role.Read));
            options.AddPolicy("RequireRoleUpdate", policy => 
                policy.RequireClaim("permission", PermissionConstants.Role.Update));
            options.AddPolicy("RequireRoleDelete", policy => 
                policy.RequireClaim("permission", PermissionConstants.Role.Delete));
            options.AddPolicy("RequireRoleManage", policy => 
                policy.RequireClaim("permission", PermissionConstants.Role.Manage));
            options.AddPolicy("RequireRoleAssignPermissions", policy => 
                policy.RequireClaim("permission", PermissionConstants.Role.AssignPermissions));
            options.AddPolicy("RequireRoleViewPermissions", policy => 
                policy.RequireClaim("permission", PermissionConstants.Role.ViewPermissions));

            // Workflow Definition policies
            options.AddPolicy("RequireWorkflowDefinitionCreate", policy => 
                policy.RequireClaim("permission", PermissionConstants.WorkflowDefinition.Create));
            options.AddPolicy("RequireWorkflowDefinitionRead", policy => 
                policy.RequireClaim("permission", PermissionConstants.WorkflowDefinition.Read));
            options.AddPolicy("RequireWorkflowDefinitionUpdate", policy => 
                policy.RequireClaim("permission", PermissionConstants.WorkflowDefinition.Update));
            options.AddPolicy("RequireWorkflowDefinitionDelete", policy => 
                policy.RequireClaim("permission", PermissionConstants.WorkflowDefinition.Delete));
            options.AddPolicy("RequireWorkflowDefinitionManage", policy => 
                policy.RequireClaim("permission", PermissionConstants.WorkflowDefinition.Manage));
            options.AddPolicy("RequireWorkflowDefinitionPublish", policy => 
                policy.RequireClaim("permission", PermissionConstants.WorkflowDefinition.Publish));
            options.AddPolicy("RequireWorkflowDefinitionValidate", policy => 
                policy.RequireClaim("permission", PermissionConstants.WorkflowDefinition.Validate));
            options.AddPolicy("RequireWorkflowDefinitionExport", policy => 
                policy.RequireClaim("permission", PermissionConstants.WorkflowDefinition.Export));
            options.AddPolicy("RequireWorkflowDefinitionImport", policy => 
                policy.RequireClaim("permission", PermissionConstants.WorkflowDefinition.Import));
            options.AddPolicy("RequireWorkflowDefinitionCreateVersion", policy => 
                policy.RequireClaim("permission", PermissionConstants.WorkflowDefinition.CreateVersion));

            // Workflow Instance policies
            options.AddPolicy("RequireWorkflowInstanceCreate", policy => 
                policy.RequireClaim("permission", PermissionConstants.WorkflowInstance.Create));
            options.AddPolicy("RequireWorkflowInstanceRead", policy => 
                policy.RequireClaim("permission", PermissionConstants.WorkflowInstance.Read));
            options.AddPolicy("RequireWorkflowInstanceUpdate", policy => 
                policy.RequireClaim("permission", PermissionConstants.WorkflowInstance.Update));
            options.AddPolicy("RequireWorkflowInstanceDelete", policy => 
                policy.RequireClaim("permission", PermissionConstants.WorkflowInstance.Delete));
            options.AddPolicy("RequireWorkflowInstanceManage", policy => 
                policy.RequireClaim("permission", PermissionConstants.WorkflowInstance.Manage));
            options.AddPolicy("RequireWorkflowInstanceExecute", policy => 
                policy.RequireClaim("permission", PermissionConstants.WorkflowInstance.Execute));
            options.AddPolicy("RequireWorkflowInstanceStart", policy => 
                policy.RequireClaim("permission", PermissionConstants.WorkflowInstance.Start));
            options.AddPolicy("RequireWorkflowInstanceStop", policy => 
                policy.RequireClaim("permission", PermissionConstants.WorkflowInstance.Stop));
            options.AddPolicy("RequireWorkflowInstancePause", policy => 
                policy.RequireClaim("permission", PermissionConstants.WorkflowInstance.Pause));
            options.AddPolicy("RequireWorkflowInstanceResume", policy => 
                policy.RequireClaim("permission", PermissionConstants.WorkflowInstance.Resume));
            options.AddPolicy("RequireWorkflowInstanceCancel", policy => 
                policy.RequireClaim("permission", PermissionConstants.WorkflowInstance.Cancel));
            options.AddPolicy("RequireWorkflowInstanceRestart", policy => 
                policy.RequireClaim("permission", PermissionConstants.WorkflowInstance.Restart));
            options.AddPolicy("RequireWorkflowInstanceViewHistory", policy => 
                policy.RequireClaim("permission", PermissionConstants.WorkflowInstance.ViewHistory));

            // Activity policies
            options.AddPolicy("RequireActivityCreate", policy => 
                policy.RequireClaim("permission", PermissionConstants.Activity.Create));
            options.AddPolicy("RequireActivityRead", policy => 
                policy.RequireClaim("permission", PermissionConstants.Activity.Read));
            options.AddPolicy("RequireActivityUpdate", policy => 
                policy.RequireClaim("permission", PermissionConstants.Activity.Update));
            options.AddPolicy("RequireActivityDelete", policy => 
                policy.RequireClaim("permission", PermissionConstants.Activity.Delete));
            options.AddPolicy("RequireActivityManage", policy => 
                policy.RequireClaim("permission", PermissionConstants.Activity.Manage));
            options.AddPolicy("RequireActivityExecute", policy => 
                policy.RequireClaim("permission", PermissionConstants.Activity.Execute));
            options.AddPolicy("RequireActivityConfigure", policy => 
                policy.RequireClaim("permission", PermissionConstants.Activity.Configure));
            options.AddPolicy("RequireActivityViewLogs", policy => 
                policy.RequireClaim("permission", PermissionConstants.Activity.ViewLogs));

            // Monitoring policies
            options.AddPolicy("RequireMonitoringRead", policy => 
                policy.RequireClaim("permission", PermissionConstants.Monitoring.Read));
            options.AddPolicy("RequireMonitoringManage", policy => 
                policy.RequireClaim("permission", PermissionConstants.Monitoring.Manage));
            options.AddPolicy("RequireMonitoringViewMetrics", policy => 
                policy.RequireClaim("permission", PermissionConstants.Monitoring.ViewMetrics));
            options.AddPolicy("RequireMonitoringViewDashboard", policy => 
                policy.RequireClaim("permission", PermissionConstants.Monitoring.ViewDashboard));
            options.AddPolicy("RequireMonitoringConfigureAlerts", policy => 
                policy.RequireClaim("permission", PermissionConstants.Monitoring.ConfigureAlerts));
            options.AddPolicy("RequireMonitoringExportReports", policy => 
                policy.RequireClaim("permission", PermissionConstants.Monitoring.ExportReports));

            // Audit policies
            options.AddPolicy("RequireAuditRead", policy => 
                policy.RequireClaim("permission", PermissionConstants.Audit.Read));
            options.AddPolicy("RequireAuditManage", policy => 
                policy.RequireClaim("permission", PermissionConstants.Audit.Manage));
            options.AddPolicy("RequireAuditViewLogs", policy => 
                policy.RequireClaim("permission", PermissionConstants.Audit.ViewLogs));
            options.AddPolicy("RequireAuditExportLogs", policy => 
                policy.RequireClaim("permission", PermissionConstants.Audit.ExportLogs));
            options.AddPolicy("RequireAuditConfigureRetention", policy => 
                policy.RequireClaim("permission", PermissionConstants.Audit.ConfigureRetention));

            // Dead Letter Queue policies
            options.AddPolicy("RequireDeadLetterQueueRead", policy => 
                policy.RequireClaim("permission", PermissionConstants.DeadLetterQueue.Read));
            options.AddPolicy("RequireDeadLetterQueueManage", policy => 
                policy.RequireClaim("permission", PermissionConstants.DeadLetterQueue.Manage));
            options.AddPolicy("RequireDeadLetterQueueRequeue", policy => 
                policy.RequireClaim("permission", PermissionConstants.DeadLetterQueue.Requeue));
            options.AddPolicy("RequireDeadLetterQueueDelete", policy => 
                policy.RequireClaim("permission", PermissionConstants.DeadLetterQueue.Delete));
            options.AddPolicy("RequireDeadLetterQueueViewDetails", policy => 
                policy.RequireClaim("permission", PermissionConstants.DeadLetterQueue.ViewDetails));

            // Retry Policy policies
            options.AddPolicy("RequireRetryPolicyCreate", policy => 
                policy.RequireClaim("permission", PermissionConstants.RetryPolicy.Create));
            options.AddPolicy("RequireRetryPolicyRead", policy => 
                policy.RequireClaim("permission", PermissionConstants.RetryPolicy.Read));
            options.AddPolicy("RequireRetryPolicyUpdate", policy => 
                policy.RequireClaim("permission", PermissionConstants.RetryPolicy.Update));
            options.AddPolicy("RequireRetryPolicyDelete", policy => 
                policy.RequireClaim("permission", PermissionConstants.RetryPolicy.Delete));
            options.AddPolicy("RequireRetryPolicyManage", policy => 
                policy.RequireClaim("permission", PermissionConstants.RetryPolicy.Manage));

            // Role-based policies (for users with specific roles)
            options.AddPolicy("RequireAdmin", policy =>
                policy.RequireAssertion(context =>
                    context.User.IsInRole("Admin") ||
                    context.User.IsInRole("SuperAdmin") ||
                    context.User.HasClaim("system_role", "true")));

            options.AddPolicy("RequireTenantAdmin", policy =>
                policy.RequireAssertion(context =>
                    context.User.IsInRole("TenantAdmin") ||
                    context.User.IsInRole("Admin") ||
                    context.User.IsInRole("SuperAdmin") ||
                    context.User.HasClaim("system_role", "true")));

            options.AddPolicy("RequireWorkflowManager", policy =>
                policy.RequireAssertion(context =>
                    context.User.IsInRole("WorkflowManager") ||
                    context.User.IsInRole("TenantAdmin") ||
                    context.User.IsInRole("Admin") ||
                    context.User.IsInRole("SuperAdmin") ||
                    context.User.HasClaim("system_role", "true")));

            options.AddPolicy("RequireWorkflowUser", policy =>
                policy.RequireAssertion(context =>
                    context.User.IsInRole("WorkflowUser") ||
                    context.User.IsInRole("WorkflowManager") ||
                    context.User.IsInRole("TenantAdmin") ||
                    context.User.IsInRole("Admin") ||
                    context.User.IsInRole("SuperAdmin") ||
                    context.User.HasClaim("system_role", "true")));
        });

        return services;
    }
}
