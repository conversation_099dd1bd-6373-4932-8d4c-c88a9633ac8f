using WorkflowEngine.Domain.Entities;
using WorkflowEngine.Domain.Enums;

namespace WorkflowEngine.Application.Interfaces;

public interface IWorkflowEngine
{
    // Instance Management
    Task<WorkflowInstance> StartInstanceAsync(Guid workflowDefinitionId, string? inputData = null, string? instanceName = null, CancellationToken cancellationToken = default);
    Task<WorkflowInstance> StartInstanceAsync(string workflowName, int? version = null, string? inputData = null, string? instanceName = null, CancellationToken cancellationToken = default);
    
    // Step Progression
    Task<WorkflowExecutionResult> ProgressInstanceAsync(Guid instanceId, CancellationToken cancellationToken = default);
    Task<WorkflowExecutionResult> ProgressToNextStepAsync(Guid instanceId, string? outputData = null, CancellationToken cancellationToken = default);
    Task<WorkflowExecutionResult> CompleteCurrentStepAsync(Guid instanceId, string? outputData = null, CancellationToken cancellationToken = default);
    
    // Event Handling
    Task<WorkflowExecutionResult> ResumeInstanceAsync(Guid instanceId, string eventName, string? eventData = null, CancellationToken cancellationToken = default);
    Task<WorkflowExecutionResult> HandleExternalEventAsync(Guid instanceId, string eventName, string? eventData = null, CancellationToken cancellationToken = default);
    
    // Instance Control
    Task<WorkflowInstance> SuspendInstanceAsync(Guid instanceId, string? reason = null, CancellationToken cancellationToken = default);
    Task<WorkflowInstance> CancelInstanceAsync(Guid instanceId, string? reason = null, CancellationToken cancellationToken = default);
    Task<WorkflowInstance> RetryInstanceAsync(Guid instanceId, CancellationToken cancellationToken = default);
    
    // State Management
    Task<WorkflowInstance> GetInstanceAsync(Guid instanceId, CancellationToken cancellationToken = default);
    Task<WorkflowInstanceStatus> GetInstanceStatusAsync(Guid instanceId, CancellationToken cancellationToken = default);
    Task<IEnumerable<ActivityExecution>> GetInstanceHistoryAsync(Guid instanceId, CancellationToken cancellationToken = default);
    
    // Variable Management
    Task SetInstanceVariableAsync(Guid instanceId, string key, object value, CancellationToken cancellationToken = default);
    Task<T?> GetInstanceVariableAsync<T>(Guid instanceId, string key, CancellationToken cancellationToken = default);
    Task<Dictionary<string, object>> GetInstanceVariablesAsync(Guid instanceId, CancellationToken cancellationToken = default);
    
    // Persistence
    Task PersistInstanceStateAsync(Guid instanceId, CancellationToken cancellationToken = default);
    Task<bool> ValidateInstanceAsync(Guid instanceId, CancellationToken cancellationToken = default);
}

public class WorkflowExecutionResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public WorkflowInstanceStatus Status { get; set; }
    public Guid? CurrentStepId { get; set; }
    public string? OutputData { get; set; }
    public Dictionary<string, object>? Variables { get; set; }
    public bool IsCompleted { get; set; }
    public bool RequiresUserInput { get; set; }
    public DateTime? NextExecutionTime { get; set; }
    
    public static WorkflowExecutionResult Success(WorkflowInstanceStatus status, Guid? currentStepId = null, string? outputData = null)
    {
        return new WorkflowExecutionResult
        {
            IsSuccess = true,
            Status = status,
            CurrentStepId = currentStepId,
            OutputData = outputData,
            IsCompleted = status == WorkflowInstanceStatus.Completed
        };
    }
    
    public static WorkflowExecutionResult Failure(string errorMessage, WorkflowInstanceStatus status = WorkflowInstanceStatus.Failed)
    {
        return new WorkflowExecutionResult
        {
            IsSuccess = false,
            ErrorMessage = errorMessage,
            Status = status
        };
    }
    
    public static WorkflowExecutionResult Suspended(Guid currentStepId, DateTime? nextExecutionTime = null)
    {
        return new WorkflowExecutionResult
        {
            IsSuccess = true,
            Status = WorkflowInstanceStatus.Suspended,
            CurrentStepId = currentStepId,
            NextExecutionTime = nextExecutionTime
        };
    }
    
    public static WorkflowExecutionResult WaitingForInput(Guid currentStepId)
    {
        return new WorkflowExecutionResult
        {
            IsSuccess = true,
            Status = WorkflowInstanceStatus.Suspended,
            CurrentStepId = currentStepId,
            RequiresUserInput = true
        };
    }
}
