using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WorkflowEngine.Domain.Entities;
using WorkflowEngine.Domain.Enums;

namespace WorkflowEngine.Infrastructure.Data.Configurations;

public class WorkflowDefinitionConfiguration : IEntityTypeConfiguration<WorkflowDefinition>
{
    public void Configure(EntityTypeBuilder<WorkflowDefinition> builder)
    {
        builder.ToTable("WorkflowDefinitions");

        builder.HasKey(wd => wd.Id);

        builder.Property(wd => wd.Name)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(wd => wd.Description)
            .HasMaxLength(1000);

        builder.Property(wd => wd.Status)
            .HasConversion<int>()
            .HasDefaultValue(WorkflowStatus.Draft);

        builder.Property(wd => wd.JsonDefinition)
            .HasColumnType("jsonb");

        builder.Property(wd => wd.Tags)
            .HasMaxLength(500);

        builder.HasIndex(wd => new { wd.TenantId, wd.Name, wd.Version })
            .IsUnique();

        builder.HasIndex(wd => wd.TenantId);

        // Navigation properties
        builder.HasOne(wd => wd.Tenant)
            .WithMany(t => t.WorkflowDefinitions)
            .HasForeignKey(wd => wd.TenantId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(wd => wd.Steps)
            .WithOne(ws => ws.WorkflowDefinition)
            .HasForeignKey(ws => ws.WorkflowDefinitionId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(wd => wd.Instances)
            .WithOne(wi => wi.WorkflowDefinition)
            .HasForeignKey(wi => wi.WorkflowDefinitionId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}
