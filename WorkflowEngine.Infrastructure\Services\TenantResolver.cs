using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System.Security.Claims;
using WorkflowEngine.Application.Interfaces;

namespace WorkflowEngine.Infrastructure.Services;

public class TenantResolver : ITenantResolver
{
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly ITenantRepository _tenantRepository;
    private readonly ILogger<TenantResolver> _logger;

    public TenantResolver(
        IHttpContextAccessor httpContextAccessor,
        ITenantRepository tenantRepository,
        ILogger<TenantResolver> logger)
    {
        _httpContextAccessor = httpContextAccessor;
        _tenantRepository = tenantRepository;
        _logger = logger;
    }

    public async Task<Guid?> ResolveTenantIdAsync(string? tenantIdentifier = null)
    {
        try
        {
            // Try to get tenant identifier from parameter first
            if (!string.IsNullOrEmpty(tenantIdentifier))
            {
                return await ResolveTenantByIdentifierAsync(tenantIdentifier);
            }

            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext == null)
            {
                _logger.LogWarning("No HTTP context available for tenant resolution");
                return null;
            }

            // Try to get tenant from header
            if (httpContext.Request.Headers.TryGetValue("X-Tenant-Id", out var tenantHeader))
            {
                var headerValue = tenantHeader.FirstOrDefault();
                if (!string.IsNullOrEmpty(headerValue))
                {
                    return await ResolveTenantByIdentifierAsync(headerValue);
                }
            }

            // Try to get tenant from claims
            var tenantClaim = httpContext.User?.FindFirst("tenant_id")?.Value;
            if (!string.IsNullOrEmpty(tenantClaim))
            {
                return await ResolveTenantByIdentifierAsync(tenantClaim);
            }

            // Try to get tenant from subdomain
            var host = httpContext.Request.Host.Host;
            if (!string.IsNullOrEmpty(host))
            {
                var subdomain = ExtractSubdomain(host);
                if (!string.IsNullOrEmpty(subdomain))
                {
                    return await ResolveTenantByIdentifierAsync(subdomain);
                }
            }

            _logger.LogWarning("Could not resolve tenant from any source");
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resolving tenant ID");
            return null;
        }
    }

    public Task<string?> ResolveUserIdAsync()
    {
        try
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext?.User?.Identity?.IsAuthenticated == true)
            {
                var userId = httpContext.User.FindFirst(ClaimTypes.NameIdentifier)?.Value
                           ?? httpContext.User.FindFirst("sub")?.Value
                           ?? httpContext.User.FindFirst("user_id")?.Value;
                
                return Task.FromResult(userId);
            }

            return Task.FromResult<string?>(null);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resolving user ID");
            return Task.FromResult<string?>(null);
        }
    }

    private async Task<Guid?> ResolveTenantByIdentifierAsync(string identifier)
    {
        // Try to parse as GUID first
        if (Guid.TryParse(identifier, out var tenantId))
        {
            return tenantId;
        }

        // Try to find by name
        var tenant = await _tenantRepository.GetByNameAsync(identifier);
        return tenant?.Id;
    }

    private static string? ExtractSubdomain(string host)
    {
        if (string.IsNullOrEmpty(host))
            return null;

        var parts = host.Split('.');
        if (parts.Length > 2)
        {
            return parts[0];
        }

        return null;
    }
}
