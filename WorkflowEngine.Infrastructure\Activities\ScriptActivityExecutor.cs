using Microsoft.Extensions.Logging;
using System.Text.Json;
using WorkflowEngine.Application.Interfaces;
using WorkflowEngine.Domain.Enums;

namespace WorkflowEngine.Infrastructure.Activities;

public class ScriptActivityExecutor : BaseActivityExecutor
{
    public ScriptActivityExecutor(ILogger<ScriptActivityExecutor> logger) : base(logger)
    {
    }

    public override bool CanExecute(ActivityType activityType)
    {
        return activityType == ActivityType.Script;
    }

    public override async Task<ActivityExecutionResult> ExecuteAsync(ActivityExecutionContext context, CancellationToken cancellationToken = default)
    {
        var startTime = DateTime.UtcNow;
        
        try
        {
            _logger.LogInformation("Executing script activity for instance {InstanceId}, step {StepId}", 
                context.WorkflowInstance.Id, context.WorkflowStep.Id);

            var config = ParseConfiguration(context.Activity.Configuration);
            
            // Get script configuration
            var script = GetConfigurationValue<string>(config, "script", null);
            var scriptType = GetConfigurationValue<string>(config, "scriptType", "javascript"); // javascript, csharp, python
            var timeoutSeconds = GetConfigurationValue<int>(config, "timeoutSeconds", 30);
            var allowedNamespaces = GetConfigurationValue<string[]>(config, "allowedNamespaces", new[] { "System", "System.Math" });
            var returnVariable = GetConfigurationValue<string>(config, "returnVariable", "result");

            if (string.IsNullOrEmpty(script))
            {
                return CreateFailureResult("Script is required for script activity");
            }

            // Substitute variables in script
            script = SubstituteVariables(script, context.Variables);

            object? result = null;
            var outputVariables = new Dictionary<string, object>();

            switch (scriptType.ToLowerInvariant())
            {
                case "javascript":
                case "js":
                    result = await ExecuteJavaScript(script, context.Variables, timeoutSeconds);
                    break;
                
                case "csharp":
                case "c#":
                    result = await ExecuteCSharpScript(script, context.Variables, allowedNamespaces, timeoutSeconds);
                    break;
                
                case "expression":
                    result = await ExecuteExpression(script, context.Variables);
                    break;
                
                default:
                    return CreateFailureResult($"Unsupported script type: {scriptType}");
            }

            var duration = DateTime.UtcNow - startTime;

            // Store result in variables
            if (result != null)
            {
                outputVariables[returnVariable] = result;
            }

            var outputData = JsonSerializer.Serialize(new
            {
                result = result,
                scriptType = scriptType,
                executionTime = duration.TotalMilliseconds,
                returnVariable = returnVariable
            });

            _logger.LogInformation("Script executed successfully in {Duration}ms", duration.TotalMilliseconds);
            return CreateSuccessResult(outputData, outputVariables, duration);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing script activity for instance {InstanceId}", context.WorkflowInstance.Id);
            return CreateFailureResult($"Script execution failed: {ex.Message}", ex, shouldRetry: false);
        }
    }

    private async Task<object?> ExecuteJavaScript(string script, Dictionary<string, object>? variables, int timeoutSeconds)
    {
        // Placeholder for JavaScript execution using Jint or similar
        // For now, implement basic expression evaluation
        
        try
        {
            // Simple math expression evaluation
            if (IsSimpleMathExpression(script))
            {
                return EvaluateMathExpression(script);
            }

            // String operations
            if (script.Contains("concat") || script.Contains("+"))
            {
                return EvaluateStringExpression(script, variables);
            }

            _logger.LogWarning("JavaScript execution not fully implemented, returning script as string");
            return script;
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"JavaScript execution failed: {ex.Message}", ex);
        }
    }

    private async Task<object?> ExecuteCSharpScript(string script, Dictionary<string, object>? variables, string[] allowedNamespaces, int timeoutSeconds)
    {
        // Placeholder for C# script execution using Roslyn
        // For now, implement basic operations
        
        try
        {
            // Simple variable assignment
            if (script.Contains("=") && !script.Contains("=="))
            {
                var parts = script.Split('=', 2);
                if (parts.Length == 2)
                {
                    var varName = parts[0].Trim();
                    var value = parts[1].Trim().Trim(';', '"', '\'');
                    
                    if (double.TryParse(value, out var numValue))
                    {
                        return numValue;
                    }
                    return value;
                }
            }

            // Math operations
            if (IsSimpleMathExpression(script))
            {
                return EvaluateMathExpression(script);
            }

            _logger.LogWarning("C# script execution not fully implemented, returning script as string");
            return script;
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"C# script execution failed: {ex.Message}", ex);
        }
    }

    private async Task<object?> ExecuteExpression(string expression, Dictionary<string, object>? variables)
    {
        try
        {
            // Substitute variables first
            var substituted = SubstituteVariables(expression, variables);
            
            // Try to evaluate as math expression
            if (IsSimpleMathExpression(substituted))
            {
                return EvaluateMathExpression(substituted);
            }

            // Try to parse as boolean
            if (bool.TryParse(substituted, out var boolResult))
            {
                return boolResult;
            }

            // Try to parse as number
            if (double.TryParse(substituted, out var numResult))
            {
                return numResult;
            }

            // Return as string
            return substituted;
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Expression evaluation failed: {ex.Message}", ex);
        }
    }

    private bool IsSimpleMathExpression(string expression)
    {
        // Check if expression contains only numbers, operators, and parentheses
        return System.Text.RegularExpressions.Regex.IsMatch(expression, @"^[\d\+\-\*\/\(\)\.\s]+$");
    }

    private double EvaluateMathExpression(string expression)
    {
        // Simple math expression evaluator
        // In a full implementation, use a proper expression parser like NCalc
        
        try
        {
            // Remove spaces
            expression = expression.Replace(" ", "");
            
            // Basic evaluation for simple expressions
            if (expression.Contains("+"))
            {
                var parts = expression.Split('+');
                return parts.Sum(p => double.Parse(p));
            }
            
            if (expression.Contains("-") && !expression.StartsWith("-"))
            {
                var parts = expression.Split('-');
                var result = double.Parse(parts[0]);
                for (int i = 1; i < parts.Length; i++)
                {
                    result -= double.Parse(parts[i]);
                }
                return result;
            }
            
            if (expression.Contains("*"))
            {
                var parts = expression.Split('*');
                return parts.Aggregate(1.0, (acc, p) => acc * double.Parse(p));
            }
            
            if (expression.Contains("/"))
            {
                var parts = expression.Split('/');
                var result = double.Parse(parts[0]);
                for (int i = 1; i < parts.Length; i++)
                {
                    result /= double.Parse(parts[i]);
                }
                return result;
            }
            
            return double.Parse(expression);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Math expression evaluation failed: {ex.Message}", ex);
        }
    }

    private string EvaluateStringExpression(string expression, Dictionary<string, object>? variables)
    {
        // Simple string concatenation
        if (expression.Contains("+"))
        {
            var parts = expression.Split('+');
            return string.Join("", parts.Select(p => p.Trim().Trim('"', '\'')));
        }
        
        return expression;
    }

    public override async Task<bool> ValidateConfigurationAsync(string? configuration, CancellationToken cancellationToken = default)
    {
        if (!await base.ValidateConfigurationAsync(configuration, cancellationToken))
        {
            return false;
        }

        var config = ParseConfiguration(configuration);
        if (config == null)
        {
            return false;
        }

        // Validate script
        var script = GetConfigurationValue<string>(config, "script", null);
        if (string.IsNullOrEmpty(script))
        {
            _logger.LogWarning("Script activity must have a script");
            return false;
        }

        // Validate script type
        var scriptType = GetConfigurationValue<string>(config, "scriptType", "javascript");
        var validTypes = new[] { "javascript", "js", "csharp", "c#", "expression" };
        if (!validTypes.Contains(scriptType.ToLowerInvariant()))
        {
            _logger.LogWarning("Invalid script type: {ScriptType}", scriptType);
            return false;
        }

        // Validate timeout
        var timeoutSeconds = GetConfigurationValue<int>(config, "timeoutSeconds", 30);
        if (timeoutSeconds <= 0 || timeoutSeconds > 300)
        {
            _logger.LogWarning("Invalid timeout value: {TimeoutSeconds}. Must be between 1 and 300 seconds", timeoutSeconds);
            return false;
        }

        return true;
    }
}
