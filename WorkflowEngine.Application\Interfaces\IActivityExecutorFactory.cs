using WorkflowEngine.Domain.Enums;

namespace WorkflowEngine.Application.Interfaces;

public interface IActivityExecutorFactory
{
    IActivityExecutor? GetExecutor(ActivityType activityType);
    IActivityExecutor? GetExecutor(string activityTypeName);
    IEnumerable<IActivityExecutor> GetAllExecutors();
    void RegisterExecutor(ActivityType activityType, IActivityExecutor executor);
    void RegisterExecutor(string activityTypeName, IActivityExecutor executor);
    bool IsSupported(ActivityType activityType);
    bool IsSupported(string activityTypeName);
}
