using Microsoft.Extensions.Logging;
using WorkflowEngine.Application.Interfaces;
using WorkflowEngine.Domain.Entities;
using WorkflowEngine.Domain.Enums;
using WorkflowEngine.Infrastructure.Data;

namespace WorkflowEngine.Infrastructure.Services;

public class DeadLetterQueueService : IDeadLetterQueueService
{
    private readonly IDeadLetterQueueRepository _deadLetterRepository;
    private readonly ITenantContext _tenantContext;
    private readonly ILogger<DeadLetterQueueService> _logger;

    public DeadLetterQueueService(
        IDeadLetterQueueRepository deadLetterRepository,
        ITenantContext tenantContext,
        ILogger<DeadLetterQueueService> logger)
    {
        _deadLetterRepository = deadLetterRepository;
        _tenantContext = tenantContext;
        _logger = logger;
    }

    public async Task<DeadLetterQueue> AddToDeadLetterQueueAsync(ActivityExecution execution, DeadLetterReason reason, string? errorMessage = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var deadLetterItem = new DeadLetterQueue
            {
                TenantId = _tenantContext.TenantId,
                WorkflowInstanceId = execution.WorkflowInstanceId,
                WorkflowStepId = execution.WorkflowStepId,
                ActivityId = execution.ActivityId,
                ActivityExecutionId = execution.Id,
                Reason = reason,
                ErrorMessage = errorMessage ?? execution.ErrorMessage,
                StackTrace = execution.StackTrace,
                InputData = execution.InputData,
                FailedAttempts = execution.RetryCount + 1,
                FirstFailedAt = execution.StartedAt ?? DateTime.UtcNow,
                LastFailedAt = DateTime.UtcNow,
                Status = DeadLetterStatus.Pending
            };

            await _deadLetterRepository.AddAsync(deadLetterItem, cancellationToken);
            await _deadLetterRepository.SaveChangesAsync(cancellationToken);

            _logger.LogWarning("Added activity execution {ExecutionId} to dead letter queue. Reason: {Reason}, Error: {Error}",
                execution.Id, reason, errorMessage);

            return deadLetterItem;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding activity execution {ExecutionId} to dead letter queue", execution.Id);
            throw;
        }
    }

    public async Task<DeadLetterQueue> AddToDeadLetterQueueAsync(WorkflowInstance instance, DeadLetterReason reason, string? errorMessage = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var deadLetterItem = new DeadLetterQueue
            {
                TenantId = _tenantContext.TenantId,
                WorkflowInstanceId = instance.Id,
                Reason = reason,
                ErrorMessage = errorMessage ?? instance.ErrorMessage,
                InputData = instance.InputData,
                FailedAttempts = instance.RetryCount + 1,
                FirstFailedAt = instance.StartedAt ?? DateTime.UtcNow,
                LastFailedAt = DateTime.UtcNow,
                Status = DeadLetterStatus.Pending
            };

            await _deadLetterRepository.AddAsync(deadLetterItem, cancellationToken);
            await _deadLetterRepository.SaveChangesAsync(cancellationToken);

            _logger.LogWarning("Added workflow instance {InstanceId} to dead letter queue. Reason: {Reason}, Error: {Error}",
                instance.Id, reason, errorMessage);

            return deadLetterItem;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding workflow instance {InstanceId} to dead letter queue", instance.Id);
            throw;
        }
    }

    public async Task<IEnumerable<DeadLetterQueue>> GetPendingItemsAsync(int maxItems = 100, CancellationToken cancellationToken = default)
    {
        return await _deadLetterRepository.GetByStatusAsync(_tenantContext.TenantId, DeadLetterStatus.Pending, maxItems, cancellationToken);
    }

    public async Task<IEnumerable<DeadLetterQueue>> GetItemsByStatusAsync(DeadLetterStatus status, int maxItems = 100, CancellationToken cancellationToken = default)
    {
        return await _deadLetterRepository.GetByStatusAsync(_tenantContext.TenantId, status, maxItems, cancellationToken);
    }

    public async Task<DeadLetterQueue?> GetItemAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _deadLetterRepository.GetByIdAsync(_tenantContext.TenantId, id, cancellationToken);
    }

    public async Task<bool> RequeueItemAsync(Guid id, string? notes = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var item = await _deadLetterRepository.GetByIdAsync(_tenantContext.TenantId, id, cancellationToken);
            if (item == null)
            {
                return false;
            }

            item.Status = DeadLetterStatus.Requeued;
            item.ProcessedAt = DateTime.UtcNow;
            item.ProcessedBy = _tenantContext.UserId?.ToString();
            item.Notes = notes;

            await _deadLetterRepository.UpdateAsync(item, cancellationToken);
            await _deadLetterRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Requeued dead letter item {ItemId} for workflow instance {InstanceId}",
                id, item.WorkflowInstanceId);

            // TODO: Trigger reprocessing of the workflow/activity
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error requeuing dead letter item {ItemId}", id);
            return false;
        }
    }

    public async Task<bool> ResolveItemAsync(Guid id, string resolution, string? notes = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var item = await _deadLetterRepository.GetByIdAsync(_tenantContext.TenantId, id, cancellationToken);
            if (item == null)
            {
                return false;
            }

            item.Status = DeadLetterStatus.Resolved;
            item.ProcessedAt = DateTime.UtcNow;
            item.ProcessedBy = _tenantContext.UserId?.ToString();
            item.Resolution = resolution;
            item.Notes = notes;

            await _deadLetterRepository.UpdateAsync(item, cancellationToken);
            await _deadLetterRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Resolved dead letter item {ItemId} for workflow instance {InstanceId}. Resolution: {Resolution}",
                id, item.WorkflowInstanceId, resolution);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resolving dead letter item {ItemId}", id);
            return false;
        }
    }

    public async Task<bool> IgnoreItemAsync(Guid id, string? notes = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var item = await _deadLetterRepository.GetByIdAsync(_tenantContext.TenantId, id, cancellationToken);
            if (item == null)
            {
                return false;
            }

            item.Status = DeadLetterStatus.Ignored;
            item.ProcessedAt = DateTime.UtcNow;
            item.ProcessedBy = _tenantContext.UserId?.ToString();
            item.Notes = notes;

            await _deadLetterRepository.UpdateAsync(item, cancellationToken);
            await _deadLetterRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Ignored dead letter item {ItemId} for workflow instance {InstanceId}",
                id, item.WorkflowInstanceId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error ignoring dead letter item {ItemId}", id);
            return false;
        }
    }

    public async Task<DeadLetterQueueStats> GetStatsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return await _deadLetterRepository.GetStatsAsync(_tenantContext.TenantId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting dead letter queue stats");
            return new DeadLetterQueueStats();
        }
    }

    public async Task<bool> PurgeOldItemsAsync(TimeSpan olderThan, CancellationToken cancellationToken = default)
    {
        try
        {
            var cutoffDate = DateTime.UtcNow.Subtract(olderThan);
            var purgedCount = await _deadLetterRepository.PurgeOldItemsAsync(_tenantContext.TenantId, cutoffDate, cancellationToken);

            _logger.LogInformation("Purged {Count} old dead letter items older than {CutoffDate}", purgedCount, cutoffDate);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error purging old dead letter items");
            return false;
        }
    }
}
