using WorkflowEngine.Domain.Entities;
using WorkflowEngine.Domain.Enums;

namespace WorkflowEngine.Application.Interfaces;

public interface IActivityRepository : ITenantRepository<Activity>
{
    Task<Activity?> GetByNameAndVersionAsync(Guid tenantId, string name, string version, CancellationToken cancellationToken = default);
    Task<IEnumerable<Activity>> GetByTypeAsync(Guid tenantId, ActivityType type, CancellationToken cancellationToken = default);
    Task<IEnumerable<Activity>> GetActiveActivitiesAsync(Guid tenantId, CancellationToken cancellationToken = default);
}
