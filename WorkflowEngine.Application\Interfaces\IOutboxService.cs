using WorkflowEngine.Domain.Entities;

namespace WorkflowEngine.Application.Interfaces;

public interface IOutboxService
{
    // Event Publishing
    Task<OutboxEvent> PublishEventAsync(string eventType, object eventData, string? correlationId = null, string? causationId = null, CancellationToken cancellationToken = default);
    Task<OutboxEvent> PublishWorkflowEventAsync(string eventType, object eventData, Guid workflowInstanceId, Guid? workflowStepId = null, Guid? activityExecutionId = null, string? correlationId = null, CancellationToken cancellationToken = default);
    Task PublishEventsAsync(IEnumerable<OutboxEvent> events, CancellationToken cancellationToken = default);

    // Event Processing
    Task<IEnumerable<OutboxEvent>> GetPendingEventsAsync(int maxEvents = 100, CancellationToken cancellationToken = default);
    Task<OutboxEvent> ProcessEventAsync(OutboxEvent outboxEvent, CancellationToken cancellationToken = default);
    Task MarkEventAsCompletedAsync(Guid eventId, CancellationToken cancellationToken = default);
    Task MarkEventAsFailedAsync(Guid eventId, string errorMessage, string? stackTrace = null, CancellationToken cancellationToken = default);

    // Event Retry
    Task<IEnumerable<OutboxEvent>> GetEventsForRetryAsync(DateTime retryBefore, int maxEvents = 100, CancellationToken cancellationToken = default);
    Task ScheduleRetryAsync(Guid eventId, DateTime nextRetryAt, CancellationToken cancellationToken = default);

    // Event Management
    Task<OutboxEvent?> GetEventByIdAsync(Guid eventId, CancellationToken cancellationToken = default);
    Task<IEnumerable<OutboxEvent>> GetEventsByCorrelationIdAsync(string correlationId, CancellationToken cancellationToken = default);
    Task<IEnumerable<OutboxEvent>> GetEventsByWorkflowInstanceAsync(Guid workflowInstanceId, CancellationToken cancellationToken = default);
    Task CancelEventAsync(Guid eventId, string reason, CancellationToken cancellationToken = default);
}

public interface IOutboxEventHandler
{
    string EventType { get; }
    Task<bool> CanHandleAsync(OutboxEvent outboxEvent, CancellationToken cancellationToken = default);
    Task<OutboxEventHandlerResult> HandleAsync(OutboxEvent outboxEvent, CancellationToken cancellationToken = default);
}

public class OutboxEventHandlerResult
{
    public bool IsSuccess { get; set; }
    public bool ShouldRetry { get; set; }
    public string? ErrorMessage { get; set; }
    public string? StackTrace { get; set; }
    public DateTime? NextRetryAt { get; set; }
    public Dictionary<string, object>? ResultData { get; set; }

    public static OutboxEventHandlerResult Success(Dictionary<string, object>? resultData = null)
    {
        return new OutboxEventHandlerResult
        {
            IsSuccess = true,
            ShouldRetry = false,
            ResultData = resultData
        };
    }

    public static OutboxEventHandlerResult Failure(string errorMessage, bool shouldRetry = true, DateTime? nextRetryAt = null, string? stackTrace = null)
    {
        return new OutboxEventHandlerResult
        {
            IsSuccess = false,
            ShouldRetry = shouldRetry,
            ErrorMessage = errorMessage,
            StackTrace = stackTrace,
            NextRetryAt = nextRetryAt
        };
    }
}
