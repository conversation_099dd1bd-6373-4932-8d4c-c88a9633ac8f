using WorkflowEngine.Infrastructure.Data;

namespace WorkflowEngine.Infrastructure.Services;

public class TenantContext : ITenantContext
{
    public Guid TenantId { get; private set; }
    public string? UserId { get; private set; }

    public void SetTenant(Guid tenantId, string? userId = null)
    {
        TenantId = tenantId;
        UserId = userId;
    }

    public void Clear()
    {
        TenantId = Guid.Empty;
        UserId = null;
    }
}
