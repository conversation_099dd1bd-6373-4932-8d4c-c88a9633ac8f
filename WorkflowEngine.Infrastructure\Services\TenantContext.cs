using WorkflowEngine.Infrastructure.Data;

namespace WorkflowEngine.Infrastructure.Services;

public class TenantContext : ITenantContext
{
    public Guid TenantId { get; set; }
    public Guid? UserId { get; set; }

    public void SetTenant(Guid tenantId, Guid? userId = null)
    {
        TenantId = tenantId;
        UserId = userId;
    }

    public void Clear()
    {
        TenantId = Guid.Empty;
        UserId = null;
    }
}
