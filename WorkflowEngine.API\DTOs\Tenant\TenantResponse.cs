namespace WorkflowEngine.API.DTOs.Tenant;

public class TenantResponse
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Slug { get; set; } = string.Empty;
    public string? Description { get; set; }
    public bool IsActive { get; set; }
    public Dictionary<string, object>? Settings { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public string? CreatedBy { get; set; }
    public string? UpdatedBy { get; set; }
    public int UserCount { get; set; }
    public int WorkflowDefinitionCount { get; set; }
    public int ActiveWorkflowInstanceCount { get; set; }
}
