using WorkflowEngine.Domain.Entities;

namespace WorkflowEngine.Application.Interfaces;

public interface ITenantService
{
    // Existing methods
    Task<Tenant?> GetCurrentTenantAsync(CancellationToken cancellationToken = default);
    Task<Tenant?> GetTenantByIdAsync(Guid tenantId, CancellationToken cancellationToken = default);
    Task<Tenant?> GetTenantByNameAsync(string name, CancellationToken cancellationToken = default);
    Task<Tenant> CreateTenantAsync(string name, string? description = null, CancellationToken cancellationToken = default);
    Task<bool> ValidateTenantAccessAsync(Guid tenantId, CancellationToken cancellationToken = default);
    Task<Dictionary<string, object>?> GetTenantSettingsAsync(Guid tenantId, CancellationToken cancellationToken = default);
    Task UpdateTenantSettingsAsync(Guid tenantId, Dictionary<string, object> settings, CancellationToken cancellationToken = default);

    // Extended methods for tenant management
    Task<Tenant> CreateTenantWithAdminAsync(string name, string slug, string? description, Dictionary<string, object>? settings, string adminEmail, string adminFirstName, string adminLastName, string adminPassword, CancellationToken cancellationToken = default);
    Task<Tenant?> GetTenantBySlugAsync(string slug, CancellationToken cancellationToken = default);
    Task<IEnumerable<Tenant>> GetAllTenantsAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<Tenant>> GetActiveTenantsAsync(CancellationToken cancellationToken = default);
    Task<Tenant> UpdateTenantAsync(Guid tenantId, string name, string? description = null, bool? isActive = null, Dictionary<string, object>? settings = null, CancellationToken cancellationToken = default);
    Task<bool> DeleteTenantAsync(Guid tenantId, CancellationToken cancellationToken = default);
    Task<bool> ActivateTenantAsync(Guid tenantId, CancellationToken cancellationToken = default);
    Task<bool> DeactivateTenantAsync(Guid tenantId, CancellationToken cancellationToken = default);
    Task<bool> TenantExistsAsync(Guid tenantId, CancellationToken cancellationToken = default);
    Task<bool> TenantSlugExistsAsync(string slug, Guid? excludeTenantId = null, CancellationToken cancellationToken = default);
    Task<Dictionary<string, object>> GetTenantStatsAsync(Guid tenantId, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default);
    Task<int> GetTenantUserCountAsync(Guid tenantId, CancellationToken cancellationToken = default);
    Task<int> GetTenantWorkflowDefinitionCountAsync(Guid tenantId, CancellationToken cancellationToken = default);
    Task<int> GetTenantActiveWorkflowInstanceCountAsync(Guid tenantId, CancellationToken cancellationToken = default);
}
