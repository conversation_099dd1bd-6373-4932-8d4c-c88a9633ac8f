using WorkflowEngine.Domain.Entities;

namespace WorkflowEngine.Application.Interfaces;

public interface ITenantService
{
    Task<Tenant?> GetCurrentTenantAsync(CancellationToken cancellationToken = default);
    Task<Tenant?> GetTenantByIdAsync(Guid tenantId, CancellationToken cancellationToken = default);
    Task<Tenant?> GetTenantByNameAsync(string name, CancellationToken cancellationToken = default);
    Task<Tenant> CreateTenantAsync(string name, string? description = null, CancellationToken cancellationToken = default);
    Task<bool> ValidateTenantAccessAsync(Guid tenantId, CancellationToken cancellationToken = default);
    Task<Dictionary<string, object>?> GetTenantSettingsAsync(Guid tenantId, CancellationToken cancellationToken = default);
    Task UpdateTenantSettingsAsync(Guid tenantId, Dictionary<string, object> settings, CancellationToken cancellationToken = default);
}
