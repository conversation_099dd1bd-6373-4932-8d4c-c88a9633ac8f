using WorkflowEngine.Domain.Common;
using WorkflowEngine.Domain.Enums;

namespace WorkflowEngine.Domain.Entities;

public class Activity : BaseEntity, ITenantEntity
{
    public Guid TenantId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public ActivityType Type { get; set; }
    public string? Configuration { get; set; }
    public string? InputSchema { get; set; }
    public string? OutputSchema { get; set; }
    public bool IsActive { get; set; } = true;
    public string? Version { get; set; } = "1.0.0";
    public string? AssemblyName { get; set; }
    public string? ClassName { get; set; }

    // Navigation properties
    public virtual Tenant Tenant { get; set; } = null!;
    public virtual ICollection<WorkflowStep> WorkflowSteps { get; set; } = new List<WorkflowStep>();
    public virtual ICollection<ActivityExecution> ActivityExecutions { get; set; } = new List<ActivityExecution>();
}
