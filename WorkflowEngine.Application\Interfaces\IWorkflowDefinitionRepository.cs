using WorkflowEngine.Domain.Entities;
using WorkflowEngine.Domain.Enums;

namespace WorkflowEngine.Application.Interfaces;

public interface IWorkflowDefinitionRepository : ITenantRepository<WorkflowDefinition>
{
    Task<WorkflowDefinition?> GetByNameAndVersionAsync(Guid tenantId, string name, int version, CancellationToken cancellationToken = default);
    Task<WorkflowDefinition?> GetLatestVersionAsync(Guid tenantId, string name, CancellationToken cancellationToken = default);
    Task<IEnumerable<WorkflowDefinition>> GetByStatusAsync(Guid tenantId, WorkflowStatus status, CancellationToken cancellationToken = default);
    Task<IEnumerable<WorkflowDefinition>> GetActiveDefinitionsAsync(Guid tenantId, CancellationToken cancellationToken = default);
    Task<int> GetNextVersionAsync(Guid tenantId, string name, CancellationToken cancellationToken = default);
}
