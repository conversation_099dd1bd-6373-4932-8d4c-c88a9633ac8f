using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Text.RegularExpressions;
using WorkflowEngine.Application.Interfaces;
using WorkflowEngine.Infrastructure.Data;

namespace WorkflowEngine.Infrastructure.Services;

public class WorkflowVariableService : IWorkflowVariableService
{
    private readonly IWorkflowInstanceRepository _workflowInstanceRepository;
    private readonly ITenantContext _tenantContext;
    private readonly ILogger<WorkflowVariableService> _logger;

    public WorkflowVariableService(
        IWorkflowInstanceRepository workflowInstanceRepository,
        ITenantContext tenantContext,
        ILogger<WorkflowVariableService> logger)
    {
        _workflowInstanceRepository = workflowInstanceRepository;
        _tenantContext = tenantContext;
        _logger = logger;
    }

    public async Task SetVariableAsync(Guid instanceId, string key, object value, CancellationToken cancellationToken = default)
    {
        var instance = await _workflowInstanceRepository.GetByIdAsync(_tenantContext.TenantId, instanceId, cancellationToken);
        if (instance == null)
        {
            throw new InvalidOperationException($"Workflow instance {instanceId} not found");
        }

        var variables = await GetAllVariablesAsync(instanceId, cancellationToken);
        variables[key] = value;

        instance.Variables = JsonSerializer.Serialize(variables);
        await _workflowInstanceRepository.UpdateAsync(instance, cancellationToken);
        await _workflowInstanceRepository.SaveChangesAsync(cancellationToken);

        _logger.LogDebug("Set variable {Key} for instance {InstanceId}", key, instanceId);
    }

    public async Task<T?> GetVariableAsync<T>(Guid instanceId, string key, CancellationToken cancellationToken = default)
    {
        var value = await GetVariableAsync(instanceId, key, cancellationToken);
        if (value == null)
        {
            return default(T);
        }

        try
        {
            if (value is JsonElement jsonElement)
            {
                return JsonSerializer.Deserialize<T>(jsonElement.GetRawText());
            }

            if (value is T directValue)
            {
                return directValue;
            }

            // Try to convert the value
            return (T)Convert.ChangeType(value, typeof(T));
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to convert variable {Key} to type {Type} for instance {InstanceId}", key, typeof(T).Name, instanceId);
            return default(T);
        }
    }

    public async Task<object?> GetVariableAsync(Guid instanceId, string key, CancellationToken cancellationToken = default)
    {
        var variables = await GetAllVariablesAsync(instanceId, cancellationToken);
        return variables.TryGetValue(key, out var value) ? value : null;
    }

    public async Task<Dictionary<string, object>> GetAllVariablesAsync(Guid instanceId, CancellationToken cancellationToken = default)
    {
        var instance = await _workflowInstanceRepository.GetByIdAsync(_tenantContext.TenantId, instanceId, cancellationToken);
        if (instance == null)
        {
            throw new InvalidOperationException($"Workflow instance {instanceId} not found");
        }

        if (string.IsNullOrEmpty(instance.Variables))
        {
            return new Dictionary<string, object>();
        }

        try
        {
            var variables = JsonSerializer.Deserialize<Dictionary<string, object>>(instance.Variables);
            return variables ?? new Dictionary<string, object>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to deserialize variables for instance {InstanceId}", instanceId);
            return new Dictionary<string, object>();
        }
    }

    public async Task RemoveVariableAsync(Guid instanceId, string key, CancellationToken cancellationToken = default)
    {
        var variables = await GetAllVariablesAsync(instanceId, cancellationToken);
        if (variables.Remove(key))
        {
            var instance = await _workflowInstanceRepository.GetByIdAsync(_tenantContext.TenantId, instanceId, cancellationToken);
            if (instance != null)
            {
                instance.Variables = JsonSerializer.Serialize(variables);
                await _workflowInstanceRepository.UpdateAsync(instance, cancellationToken);
                await _workflowInstanceRepository.SaveChangesAsync(cancellationToken);

                _logger.LogDebug("Removed variable {Key} from instance {InstanceId}", key, instanceId);
            }
        }
    }

    public async Task ClearVariablesAsync(Guid instanceId, CancellationToken cancellationToken = default)
    {
        var instance = await _workflowInstanceRepository.GetByIdAsync(_tenantContext.TenantId, instanceId, cancellationToken);
        if (instance == null)
        {
            throw new InvalidOperationException($"Workflow instance {instanceId} not found");
        }

        instance.Variables = "{}";
        await _workflowInstanceRepository.UpdateAsync(instance, cancellationToken);
        await _workflowInstanceRepository.SaveChangesAsync(cancellationToken);

        _logger.LogDebug("Cleared all variables for instance {InstanceId}", instanceId);
    }

    public async Task<Dictionary<string, object>> MergeVariablesAsync(Guid instanceId, Dictionary<string, object> variables, CancellationToken cancellationToken = default)
    {
        var existingVariables = await GetAllVariablesAsync(instanceId, cancellationToken);
        
        foreach (var kvp in variables)
        {
            existingVariables[kvp.Key] = kvp.Value;
        }

        var instance = await _workflowInstanceRepository.GetByIdAsync(_tenantContext.TenantId, instanceId, cancellationToken);
        if (instance != null)
        {
            instance.Variables = JsonSerializer.Serialize(existingVariables);
            await _workflowInstanceRepository.UpdateAsync(instance, cancellationToken);
            await _workflowInstanceRepository.SaveChangesAsync(cancellationToken);
        }

        _logger.LogDebug("Merged {Count} variables for instance {InstanceId}", variables.Count, instanceId);
        return existingVariables;
    }

    public async Task<string> SubstituteVariablesAsync(Guid instanceId, string template, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(template))
        {
            return template;
        }

        var variables = await GetAllVariablesAsync(instanceId, cancellationToken);
        
        // Replace variables in the format ${variableName} or {{variableName}}
        var result = Regex.Replace(template, @"\$\{([^}]+)\}|\{\{([^}]+)\}\}", match =>
        {
            var variableName = match.Groups[1].Success ? match.Groups[1].Value : match.Groups[2].Value;
            
            if (variables.TryGetValue(variableName, out var value))
            {
                return value?.ToString() ?? string.Empty;
            }
            
            _logger.LogWarning("Variable {VariableName} not found during substitution for instance {InstanceId}", variableName, instanceId);
            return match.Value; // Return original if variable not found
        });

        return result;
    }

    public async Task<object?> EvaluateExpressionAsync(Guid instanceId, string expression, CancellationToken cancellationToken = default)
    {
        // Basic expression evaluation - can be enhanced with a proper expression engine
        if (string.IsNullOrEmpty(expression))
        {
            return null;
        }

        // First substitute variables
        var substituted = await SubstituteVariablesAsync(instanceId, expression, cancellationToken);
        
        // For now, just return the substituted string
        // In a full implementation, you would use an expression evaluator like NCalc or similar
        return substituted;
    }

    public async Task<bool> ValidateVariableAsync(string key, object value, string? schema = null, CancellationToken cancellationToken = default)
    {
        // Basic validation - can be enhanced with JSON schema validation
        if (string.IsNullOrEmpty(key))
        {
            return false;
        }

        // Add more validation logic as needed
        return true;
    }

    public async Task<VariableValidationResult> ValidateVariablesAsync(Dictionary<string, object> variables, string? schema = null, CancellationToken cancellationToken = default)
    {
        var result = VariableValidationResult.Valid();

        foreach (var kvp in variables)
        {
            var isValid = await ValidateVariableAsync(kvp.Key, kvp.Value, schema, cancellationToken);
            if (!isValid)
            {
                result.AddError(kvp.Key, "Invalid variable value");
            }
        }

        return result;
    }
}
