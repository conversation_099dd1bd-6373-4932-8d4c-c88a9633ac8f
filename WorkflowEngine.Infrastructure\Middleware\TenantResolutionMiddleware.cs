using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using WorkflowEngine.Application.Interfaces;
using WorkflowEngine.Infrastructure.Data;

namespace WorkflowEngine.Infrastructure.Middleware;

public class TenantResolutionMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<TenantResolutionMiddleware> _logger;

    public TenantResolutionMiddleware(RequestDelegate next, ILogger<TenantResolutionMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context, ITenantResolver tenantResolver, ITenantContext tenantContext)
    {
        try
        {
            // Resolve tenant and user information
            var tenantId = await tenantResolver.ResolveTenantIdAsync();
            var userId = await tenantResolver.ResolveUserIdAsync();

            // Set tenant context if tenant was resolved
            if (tenantId.HasValue)
            {
                if (tenantContext is Infrastructure.Services.TenantContext mutableContext)
                {
                    mutableContext.SetTenant(tenantId.Value, userId);
                }

                _logger.LogDebug("Resolved tenant {TenantId} for request {RequestPath}", 
                    tenantId.Value, context.Request.Path);
            }
            else
            {
                _logger.LogDebug("No tenant resolved for request {RequestPath}", context.Request.Path);
            }

            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in tenant resolution middleware");
            
            // Continue processing even if tenant resolution fails
            await _next(context);
        }
        finally
        {
            // Clear tenant context after request
            if (tenantContext is Infrastructure.Services.TenantContext mutableContext)
            {
                mutableContext.Clear();
            }
        }
    }
}
