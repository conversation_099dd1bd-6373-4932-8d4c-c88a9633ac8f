using WorkflowEngine.Domain.Entities;

namespace WorkflowEngine.Application.Interfaces;

public interface IRetryPolicyService
{
    Task<RetryPolicy?> GetRetryPolicyAsync(Guid activityId, CancellationToken cancellationToken = default);
    Task<RetryPolicy?> GetRetryPolicyAsync(string policyName, CancellationToken cancellationToken = default);
    Task<RetryDecision> ShouldRetryAsync(ActivityExecution execution, Exception? exception = null, CancellationToken cancellationToken = default);
    Task<DateTime> CalculateNextRetryTimeAsync(ActivityExecution execution, RetryPolicy policy, CancellationToken cancellationToken = default);
    Task<bool> IsRetryableExceptionAsync(Exception exception, RetryPolicy policy, CancellationToken cancellationToken = default);
    Task<RetryPolicy> CreateRetryPolicyAsync(string name, int maxRetries, TimeSpan initialDelay, CancellationToken cancellationToken = default);
    Task UpdateRetryPolicyAsync(RetryPolicy policy, CancellationToken cancellationToken = default);
}

public class RetryDecision
{
    public bool ShouldRetry { get; set; }
    public string Reason { get; set; } = string.Empty;
    public DateTime? NextRetryAt { get; set; }
    public TimeSpan? Delay { get; set; }
    public bool MoveToDeadLetter { get; set; }

    public static RetryDecision Retry(DateTime nextRetryAt, TimeSpan delay, string reason = "")
    {
        return new RetryDecision
        {
            ShouldRetry = true,
            NextRetryAt = nextRetryAt,
            Delay = delay,
            Reason = reason
        };
    }

    public static RetryDecision NoRetry(string reason, bool moveToDeadLetter = false)
    {
        return new RetryDecision
        {
            ShouldRetry = false,
            Reason = reason,
            MoveToDeadLetter = moveToDeadLetter
        };
    }

    public static RetryDecision DeadLetter(string reason)
    {
        return new RetryDecision
        {
            ShouldRetry = false,
            MoveToDeadLetter = true,
            Reason = reason
        };
    }
}
