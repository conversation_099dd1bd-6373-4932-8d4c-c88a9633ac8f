using WorkflowEngine.Domain.Common;

namespace WorkflowEngine.Domain.Entities;

public class Role : BaseEntity
{
    public Guid? TenantId { get; set; } // Nullable for system-wide roles
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public bool IsSystemRole { get; set; } = false;
    public bool IsActive { get; set; } = true;

    // Navigation properties
    public Tenant? Tenant { get; set; }
    public ICollection<UserRole> UserRoles { get; set; } = new List<UserRole>();
    public ICollection<RolePermission> RolePermissions { get; set; } = new List<RolePermission>();
}
