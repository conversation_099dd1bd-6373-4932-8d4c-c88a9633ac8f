using Microsoft.Extensions.Logging;
using WorkflowEngine.Application.Interfaces;
using WorkflowEngine.Domain.Enums;

namespace WorkflowEngine.Infrastructure.Activities;

public class TimerActivityExecutor : BaseActivityExecutor
{
    public TimerActivityExecutor(ILogger<TimerActivityExecutor> logger) : base(logger)
    {
    }

    public override bool CanExecute(ActivityType activityType)
    {
        return activityType == ActivityType.Timer;
    }

    public override async Task<ActivityExecutionResult> ExecuteAsync(ActivityExecutionContext context, CancellationToken cancellationToken = default)
    {
        var startTime = DateTime.UtcNow;
        
        try
        {
            _logger.LogInformation("Executing timer activity for instance {InstanceId}, step {StepId}", 
                context.WorkflowInstance.Id, context.WorkflowStep.Id);

            var config = ParseConfiguration(context.Activity.Configuration);
            
            // Get delay configuration
            var delaySeconds = GetConfigurationValue<int>(config, "delaySeconds", 0);
            var delayMinutes = GetConfigurationValue<int>(config, "delayMinutes", 0);
            var delayHours = GetConfigurationValue<int>(config, "delayHours", 0);
            var delayDays = GetConfigurationValue<int>(config, "delayDays", 0);
            
            // Support for specific datetime
            var delayUntil = GetConfigurationValue<DateTime?>(config, "delayUntil", null);
            
            // Support for cron expression (basic implementation)
            var cronExpression = GetConfigurationValue<string>(config, "cronExpression", null);

            DateTime resumeAt;

            if (delayUntil.HasValue)
            {
                // Delay until specific datetime
                resumeAt = delayUntil.Value;
                _logger.LogInformation("Timer will resume at specific time: {ResumeAt}", resumeAt);
            }
            else if (!string.IsNullOrEmpty(cronExpression))
            {
                // Basic cron support - for now, just parse simple expressions
                resumeAt = ParseCronExpression(cronExpression);
                _logger.LogInformation("Timer will resume based on cron expression '{CronExpression}' at: {ResumeAt}", cronExpression, resumeAt);
            }
            else
            {
                // Calculate delay from current time
                var totalDelay = TimeSpan.FromDays(delayDays) + 
                               TimeSpan.FromHours(delayHours) + 
                               TimeSpan.FromMinutes(delayMinutes) + 
                               TimeSpan.FromSeconds(delaySeconds);

                if (totalDelay == TimeSpan.Zero)
                {
                    // Default to 1 minute if no delay specified
                    totalDelay = TimeSpan.FromMinutes(1);
                    _logger.LogWarning("No delay specified for timer activity, defaulting to 1 minute");
                }

                resumeAt = DateTime.UtcNow.Add(totalDelay);
                _logger.LogInformation("Timer will resume after {Delay} at: {ResumeAt}", totalDelay, resumeAt);
            }

            // Check if the delay time has already passed
            if (resumeAt <= DateTime.UtcNow)
            {
                _logger.LogInformation("Timer delay has already passed, completing immediately");
                
                var duration = DateTime.UtcNow - startTime;
                var outputData = System.Text.Json.JsonSerializer.Serialize(new
                {
                    completedAt = DateTime.UtcNow,
                    delayCompleted = true,
                    actualDelay = duration.TotalSeconds
                });

                return CreateSuccessResult(outputData, null, duration);
            }

            // Return pending result with resume time
            return CreatePendingResult(resumeAt);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing timer activity for instance {InstanceId}", context.WorkflowInstance.Id);
            return CreateFailureResult($"Timer activity failed: {ex.Message}", ex, shouldRetry: true);
        }
    }

    public override async Task<bool> ValidateConfigurationAsync(string? configuration, CancellationToken cancellationToken = default)
    {
        if (!await base.ValidateConfigurationAsync(configuration, cancellationToken))
        {
            return false;
        }

        var config = ParseConfiguration(configuration);
        if (config == null)
        {
            return false;
        }

        // Validate that at least one delay parameter is specified
        var hasDelaySeconds = config.ContainsKey("delaySeconds");
        var hasDelayMinutes = config.ContainsKey("delayMinutes");
        var hasDelayHours = config.ContainsKey("delayHours");
        var hasDelayDays = config.ContainsKey("delayDays");
        var hasDelayUntil = config.ContainsKey("delayUntil");
        var hasCronExpression = config.ContainsKey("cronExpression");

        if (!hasDelaySeconds && !hasDelayMinutes && !hasDelayHours && !hasDelayDays && !hasDelayUntil && !hasCronExpression)
        {
            _logger.LogWarning("Timer activity configuration must specify at least one delay parameter");
            return false;
        }

        // Validate delayUntil format if specified
        if (hasDelayUntil)
        {
            var delayUntilValue = GetConfigurationValue<string>(config, "delayUntil", null);
            if (!DateTime.TryParse(delayUntilValue, out _))
            {
                _logger.LogWarning("Invalid delayUntil format: {DelayUntil}", delayUntilValue);
                return false;
            }
        }

        return true;
    }

    private DateTime ParseCronExpression(string cronExpression)
    {
        // Basic cron parsing - in a full implementation, use a proper cron library like Cronos
        // For now, support simple patterns like "0 9 * * *" (daily at 9 AM)
        
        try
        {
            var parts = cronExpression.Split(' ');
            if (parts.Length >= 5)
            {
                var minute = int.Parse(parts[0]);
                var hour = int.Parse(parts[1]);
                
                var now = DateTime.UtcNow;
                var nextRun = new DateTime(now.Year, now.Month, now.Day, hour, minute, 0, DateTimeKind.Utc);
                
                // If the time has passed today, schedule for tomorrow
                if (nextRun <= now)
                {
                    nextRun = nextRun.AddDays(1);
                }
                
                return nextRun;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to parse cron expression: {CronExpression}", cronExpression);
        }
        
        // Fallback to 1 hour from now
        return DateTime.UtcNow.AddHours(1);
    }
}
