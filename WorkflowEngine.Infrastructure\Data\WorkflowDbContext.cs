using Microsoft.EntityFrameworkCore;
using WorkflowEngine.Domain.Common;
using WorkflowEngine.Domain.Entities;
using WorkflowEngine.Infrastructure.Data.Configurations;

namespace WorkflowEngine.Infrastructure.Data;

public class WorkflowDbContext : DbContext
{
    private readonly ITenantContext? _tenantContext;

    public WorkflowDbContext(DbContextOptions<WorkflowDbContext> options, ITenantContext? tenantContext = null)
        : base(options)
    {
        _tenantContext = tenantContext;
    }

    public DbSet<Tenant> Tenants { get; set; }
    public DbSet<User> Users { get; set; }
    public DbSet<Role> Roles { get; set; }
    public DbSet<Permission> Permissions { get; set; }
    public DbSet<UserRole> UserRoles { get; set; }
    public DbSet<RolePermission> RolePermissions { get; set; }
    public DbSet<RoleTemplate> RoleTemplates { get; set; }
    public DbSet<WorkflowDefinition> WorkflowDefinitions { get; set; }
    public DbSet<WorkflowInstance> WorkflowInstances { get; set; }
    public DbSet<WorkflowStep> WorkflowSteps { get; set; }
    public DbSet<Activity> Activities { get; set; }
    public DbSet<ActivityExecution> ActivityExecutions { get; set; }
    public DbSet<RetryPolicy> RetryPolicies { get; set; }
    public DbSet<DeadLetterQueue> DeadLetterQueue { get; set; }
    public DbSet<OutboxEvent> OutboxEvents { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Apply entity configurations
        modelBuilder.ApplyConfiguration(new TenantConfiguration());
        modelBuilder.ApplyConfiguration(new UserConfiguration());
        modelBuilder.ApplyConfiguration(new RoleConfiguration());
        modelBuilder.ApplyConfiguration(new PermissionConfiguration());
        modelBuilder.ApplyConfiguration(new UserRoleConfiguration());
        modelBuilder.ApplyConfiguration(new RolePermissionConfiguration());
        modelBuilder.ApplyConfiguration(new RoleTemplateConfiguration());
        modelBuilder.ApplyConfiguration(new WorkflowDefinitionConfiguration());
        modelBuilder.ApplyConfiguration(new WorkflowInstanceConfiguration());
        modelBuilder.ApplyConfiguration(new WorkflowStepConfiguration());
        modelBuilder.ApplyConfiguration(new ActivityConfiguration());
        modelBuilder.ApplyConfiguration(new ActivityExecutionConfiguration());
        modelBuilder.ApplyConfiguration(new RetryPolicyConfiguration());
        modelBuilder.ApplyConfiguration(new DeadLetterQueueConfiguration());
        modelBuilder.ApplyConfiguration(new OutboxEventConfiguration());

        // Apply global query filters for multi-tenancy
        ApplyGlobalFilters(modelBuilder);
    }

    private void ApplyGlobalFilters(ModelBuilder modelBuilder)
    {
        // Apply tenant filter to all entities that implement ITenantEntity
        foreach (var entityType in modelBuilder.Model.GetEntityTypes())
        {
            if (typeof(ITenantEntity).IsAssignableFrom(entityType.ClrType))
            {
                var method = typeof(WorkflowDbContext)
                    .GetMethod(nameof(GetTenantFilter), System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static)!
                    .MakeGenericMethod(entityType.ClrType);
                
                var filter = method.Invoke(null, new object[] { this });
                modelBuilder.Entity(entityType.ClrType).HasQueryFilter((System.Linq.Expressions.LambdaExpression)filter!);
            }
        }
    }

    private static System.Linq.Expressions.LambdaExpression GetTenantFilter<TEntity>(WorkflowDbContext context)
        where TEntity : class, ITenantEntity
    {
        System.Linq.Expressions.Expression<Func<TEntity, bool>> filter = x => 
            context._tenantContext == null || x.TenantId == context._tenantContext.TenantId;
        return filter;
    }

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        // Set audit fields and tenant context
        foreach (var entry in ChangeTracker.Entries<BaseEntity>())
        {
            switch (entry.State)
            {
                case EntityState.Added:
                    entry.Entity.CreatedAt = DateTime.UtcNow;
                    entry.Entity.UpdatedAt = DateTime.UtcNow;
                    if (_tenantContext?.UserId != null)
                    {
                        entry.Entity.CreatedBy = _tenantContext.UserId.ToString();
                        entry.Entity.UpdatedBy = _tenantContext.UserId.ToString();
                    }
                    break;
                case EntityState.Modified:
                    entry.Entity.UpdatedAt = DateTime.UtcNow;
                    if (_tenantContext?.UserId != null)
                    {
                        entry.Entity.UpdatedBy = _tenantContext.UserId.ToString();
                    }
                    break;
            }
        }

        // Set tenant context for new tenant entities
        foreach (var entry in ChangeTracker.Entries<ITenantEntity>())
        {
            if (entry.State == EntityState.Added && _tenantContext != null)
            {
                entry.Entity.TenantId = _tenantContext.TenantId;
            }
        }

        return await base.SaveChangesAsync(cancellationToken);
    }
}
