namespace WorkflowEngine.Domain.Constants;

/// <summary>
/// Centralized permission constants for the workflow engine system.
/// This provides a single source of truth for all permissions used throughout the application.
/// </summary>
public static class PermissionConstants
{
    #region System Permissions
    public static class System
    {
        public const string Resource = "system";
        
        public const string Admin = "system.admin";
        public const string View = "system.view";
        public const string Configure = "system.configure";
        public const string Monitor = "system.monitor";
    }
    #endregion

    #region Tenant Permissions
    public static class Tenant
    {
        public const string Resource = "tenant";
        
        public const string Create = "tenant.create";
        public const string Read = "tenant.read";
        public const string Update = "tenant.update";
        public const string Delete = "tenant.delete";
        public const string Manage = "tenant.manage";
        public const string Activate = "tenant.activate";
        public const string Deactivate = "tenant.deactivate";
        public const string ViewStats = "tenant.view_stats";
    }
    #endregion

    #region User Permissions
    public static class User
    {
        public const string Resource = "user";
        
        public const string Create = "user.create";
        public const string Read = "user.read";
        public const string Update = "user.update";
        public const string Delete = "user.delete";
        public const string Manage = "user.manage";
        public const string ChangePassword = "user.change_password";
        public const string ResetPassword = "user.reset_password";
        public const string ViewProfile = "user.view_profile";
    }
    #endregion

    #region Role Permissions
    public static class Role
    {
        public const string Resource = "role";
        
        public const string Create = "role.create";
        public const string Read = "role.read";
        public const string Update = "role.update";
        public const string Delete = "role.delete";
        public const string Manage = "role.manage";
        public const string AssignPermissions = "role.assign_permissions";
        public const string ViewPermissions = "role.view_permissions";
    }
    #endregion

    #region Workflow Definition Permissions
    public static class WorkflowDefinition
    {
        public const string Resource = "workflow_definition";
        
        public const string Create = "workflow.definition.create";
        public const string Read = "workflow.definition.read";
        public const string Update = "workflow.definition.update";
        public const string Delete = "workflow.definition.delete";
        public const string Manage = "workflow.definition.manage";
        public const string Publish = "workflow.definition.publish";
        public const string Validate = "workflow.definition.validate";
        public const string Export = "workflow.definition.export";
        public const string Import = "workflow.definition.import";
        public const string CreateVersion = "workflow.definition.create_version";
    }
    #endregion

    #region Workflow Instance Permissions
    public static class WorkflowInstance
    {
        public const string Resource = "workflow_instance";
        
        public const string Create = "workflow.instance.create";
        public const string Read = "workflow.instance.read";
        public const string Update = "workflow.instance.update";
        public const string Delete = "workflow.instance.delete";
        public const string Manage = "workflow.instance.manage";
        public const string Execute = "workflow.instance.execute";
        public const string Start = "workflow.instance.start";
        public const string Stop = "workflow.instance.stop";
        public const string Pause = "workflow.instance.pause";
        public const string Resume = "workflow.instance.resume";
        public const string Cancel = "workflow.instance.cancel";
        public const string Restart = "workflow.instance.restart";
        public const string ViewHistory = "workflow.instance.view_history";
    }
    #endregion

    #region Activity Permissions
    public static class Activity
    {
        public const string Resource = "activity";
        
        public const string Create = "activity.create";
        public const string Read = "activity.read";
        public const string Update = "activity.update";
        public const string Delete = "activity.delete";
        public const string Manage = "activity.manage";
        public const string Execute = "activity.execute";
        public const string Configure = "activity.configure";
        public const string ViewLogs = "activity.view_logs";
    }
    #endregion

    #region Monitoring Permissions
    public static class Monitoring
    {
        public const string Resource = "monitoring";
        
        public const string Read = "monitoring.read";
        public const string Manage = "monitoring.manage";
        public const string ViewMetrics = "monitoring.view_metrics";
        public const string ViewDashboard = "monitoring.view_dashboard";
        public const string ConfigureAlerts = "monitoring.configure_alerts";
        public const string ExportReports = "monitoring.export_reports";
    }
    #endregion

    #region Audit Permissions
    public static class Audit
    {
        public const string Resource = "audit";
        
        public const string Read = "audit.read";
        public const string Manage = "audit.manage";
        public const string ViewLogs = "audit.view_logs";
        public const string ExportLogs = "audit.export_logs";
        public const string ConfigureRetention = "audit.configure_retention";
    }
    #endregion

    #region Dead Letter Queue Permissions
    public static class DeadLetterQueue
    {
        public const string Resource = "dead_letter_queue";
        
        public const string Read = "dlq.read";
        public const string Manage = "dlq.manage";
        public const string Requeue = "dlq.requeue";
        public const string Delete = "dlq.delete";
        public const string ViewDetails = "dlq.view_details";
    }
    #endregion

    #region Retry Policy Permissions
    public static class RetryPolicy
    {
        public const string Resource = "retry_policy";
        
        public const string Create = "retry_policy.create";
        public const string Read = "retry_policy.read";
        public const string Update = "retry_policy.update";
        public const string Delete = "retry_policy.delete";
        public const string Manage = "retry_policy.manage";
    }
    #endregion

    /// <summary>
    /// Helper class to get all permissions with their metadata
    /// </summary>
    public static class All
    {
        public static readonly PermissionDefinition[] Permissions = new[]
        {
            // System permissions
            new PermissionDefinition(System.Admin, System.Resource, "admin", "Full system administration access", true),
            new PermissionDefinition(System.View, System.Resource, "view", "View system information", true),
            new PermissionDefinition(System.Configure, System.Resource, "configure", "Configure system settings", true),
            new PermissionDefinition(System.Monitor, System.Resource, "monitor", "Monitor system health and performance", true),

            // Tenant permissions
            new PermissionDefinition(Tenant.Create, Tenant.Resource, "create", "Create new tenants"),
            new PermissionDefinition(Tenant.Read, Tenant.Resource, "read", "View tenant information"),
            new PermissionDefinition(Tenant.Update, Tenant.Resource, "update", "Update tenant information"),
            new PermissionDefinition(Tenant.Delete, Tenant.Resource, "delete", "Delete tenants"),
            new PermissionDefinition(Tenant.Manage, Tenant.Resource, "manage", "Full tenant management"),
            new PermissionDefinition(Tenant.Activate, Tenant.Resource, "activate", "Activate tenants"),
            new PermissionDefinition(Tenant.Deactivate, Tenant.Resource, "deactivate", "Deactivate tenants"),
            new PermissionDefinition(Tenant.ViewStats, Tenant.Resource, "view_stats", "View tenant statistics"),

            // User permissions
            new PermissionDefinition(User.Create, User.Resource, "create", "Create new users"),
            new PermissionDefinition(User.Read, User.Resource, "read", "View user information"),
            new PermissionDefinition(User.Update, User.Resource, "update", "Update user information"),
            new PermissionDefinition(User.Delete, User.Resource, "delete", "Delete users"),
            new PermissionDefinition(User.Manage, User.Resource, "manage", "Full user management"),
            new PermissionDefinition(User.ChangePassword, User.Resource, "change_password", "Change user passwords"),
            new PermissionDefinition(User.ResetPassword, User.Resource, "reset_password", "Reset user passwords"),
            new PermissionDefinition(User.ViewProfile, User.Resource, "view_profile", "View user profiles"),

            // Role permissions
            new PermissionDefinition(Role.Create, Role.Resource, "create", "Create new roles"),
            new PermissionDefinition(Role.Read, Role.Resource, "read", "View role information"),
            new PermissionDefinition(Role.Update, Role.Resource, "update", "Update role information"),
            new PermissionDefinition(Role.Delete, Role.Resource, "delete", "Delete roles"),
            new PermissionDefinition(Role.Manage, Role.Resource, "manage", "Full role management"),
            new PermissionDefinition(Role.AssignPermissions, Role.Resource, "assign_permissions", "Assign permissions to roles"),
            new PermissionDefinition(Role.ViewPermissions, Role.Resource, "view_permissions", "View role permissions"),

            // Workflow Definition permissions
            new PermissionDefinition(WorkflowDefinition.Create, WorkflowDefinition.Resource, "create", "Create workflow definitions"),
            new PermissionDefinition(WorkflowDefinition.Read, WorkflowDefinition.Resource, "read", "View workflow definitions"),
            new PermissionDefinition(WorkflowDefinition.Update, WorkflowDefinition.Resource, "update", "Update workflow definitions"),
            new PermissionDefinition(WorkflowDefinition.Delete, WorkflowDefinition.Resource, "delete", "Delete workflow definitions"),
            new PermissionDefinition(WorkflowDefinition.Manage, WorkflowDefinition.Resource, "manage", "Full workflow definition management"),
            new PermissionDefinition(WorkflowDefinition.Publish, WorkflowDefinition.Resource, "publish", "Publish workflow definitions"),
            new PermissionDefinition(WorkflowDefinition.Validate, WorkflowDefinition.Resource, "validate", "Validate workflow definitions"),
            new PermissionDefinition(WorkflowDefinition.Export, WorkflowDefinition.Resource, "export", "Export workflow definitions"),
            new PermissionDefinition(WorkflowDefinition.Import, WorkflowDefinition.Resource, "import", "Import workflow definitions"),
            new PermissionDefinition(WorkflowDefinition.CreateVersion, WorkflowDefinition.Resource, "create_version", "Create new versions of workflow definitions"),

            // Workflow Instance permissions
            new PermissionDefinition(WorkflowInstance.Create, WorkflowInstance.Resource, "create", "Create workflow instances"),
            new PermissionDefinition(WorkflowInstance.Read, WorkflowInstance.Resource, "read", "View workflow instances"),
            new PermissionDefinition(WorkflowInstance.Update, WorkflowInstance.Resource, "update", "Update workflow instances"),
            new PermissionDefinition(WorkflowInstance.Delete, WorkflowInstance.Resource, "delete", "Delete workflow instances"),
            new PermissionDefinition(WorkflowInstance.Manage, WorkflowInstance.Resource, "manage", "Full workflow instance management"),
            new PermissionDefinition(WorkflowInstance.Execute, WorkflowInstance.Resource, "execute", "Execute workflow instances"),
            new PermissionDefinition(WorkflowInstance.Start, WorkflowInstance.Resource, "start", "Start workflow instances"),
            new PermissionDefinition(WorkflowInstance.Stop, WorkflowInstance.Resource, "stop", "Stop workflow instances"),
            new PermissionDefinition(WorkflowInstance.Pause, WorkflowInstance.Resource, "pause", "Pause workflow instances"),
            new PermissionDefinition(WorkflowInstance.Resume, WorkflowInstance.Resource, "resume", "Resume workflow instances"),
            new PermissionDefinition(WorkflowInstance.Cancel, WorkflowInstance.Resource, "cancel", "Cancel workflow instances"),
            new PermissionDefinition(WorkflowInstance.Restart, WorkflowInstance.Resource, "restart", "Restart workflow instances"),
            new PermissionDefinition(WorkflowInstance.ViewHistory, WorkflowInstance.Resource, "view_history", "View workflow instance history"),

            // Activity permissions
            new PermissionDefinition(Activity.Create, Activity.Resource, "create", "Create activities"),
            new PermissionDefinition(Activity.Read, Activity.Resource, "read", "View activities"),
            new PermissionDefinition(Activity.Update, Activity.Resource, "update", "Update activities"),
            new PermissionDefinition(Activity.Delete, Activity.Resource, "delete", "Delete activities"),
            new PermissionDefinition(Activity.Manage, Activity.Resource, "manage", "Full activity management"),
            new PermissionDefinition(Activity.Execute, Activity.Resource, "execute", "Execute activities"),
            new PermissionDefinition(Activity.Configure, Activity.Resource, "configure", "Configure activities"),
            new PermissionDefinition(Activity.ViewLogs, Activity.Resource, "view_logs", "View activity execution logs"),

            // Monitoring permissions
            new PermissionDefinition(Monitoring.Read, Monitoring.Resource, "read", "View monitoring data"),
            new PermissionDefinition(Monitoring.Manage, Monitoring.Resource, "manage", "Manage monitoring settings"),
            new PermissionDefinition(Monitoring.ViewMetrics, Monitoring.Resource, "view_metrics", "View system metrics"),
            new PermissionDefinition(Monitoring.ViewDashboard, Monitoring.Resource, "view_dashboard", "View monitoring dashboard"),
            new PermissionDefinition(Monitoring.ConfigureAlerts, Monitoring.Resource, "configure_alerts", "Configure monitoring alerts"),
            new PermissionDefinition(Monitoring.ExportReports, Monitoring.Resource, "export_reports", "Export monitoring reports"),

            // Audit permissions
            new PermissionDefinition(Audit.Read, Audit.Resource, "read", "View audit logs"),
            new PermissionDefinition(Audit.Manage, Audit.Resource, "manage", "Manage audit settings"),
            new PermissionDefinition(Audit.ViewLogs, Audit.Resource, "view_logs", "View detailed audit logs"),
            new PermissionDefinition(Audit.ExportLogs, Audit.Resource, "export_logs", "Export audit logs"),
            new PermissionDefinition(Audit.ConfigureRetention, Audit.Resource, "configure_retention", "Configure audit log retention"),

            // Dead Letter Queue permissions
            new PermissionDefinition(DeadLetterQueue.Read, DeadLetterQueue.Resource, "read", "View dead letter queue items"),
            new PermissionDefinition(DeadLetterQueue.Manage, DeadLetterQueue.Resource, "manage", "Manage dead letter queue"),
            new PermissionDefinition(DeadLetterQueue.Requeue, DeadLetterQueue.Resource, "requeue", "Requeue dead letter items"),
            new PermissionDefinition(DeadLetterQueue.Delete, DeadLetterQueue.Resource, "delete", "Delete dead letter items"),
            new PermissionDefinition(DeadLetterQueue.ViewDetails, DeadLetterQueue.Resource, "view_details", "View dead letter item details"),

            // Retry Policy permissions
            new PermissionDefinition(RetryPolicy.Create, RetryPolicy.Resource, "create", "Create retry policies"),
            new PermissionDefinition(RetryPolicy.Read, RetryPolicy.Resource, "read", "View retry policies"),
            new PermissionDefinition(RetryPolicy.Update, RetryPolicy.Resource, "update", "Update retry policies"),
            new PermissionDefinition(RetryPolicy.Delete, RetryPolicy.Resource, "delete", "Delete retry policies"),
            new PermissionDefinition(RetryPolicy.Manage, RetryPolicy.Resource, "manage", "Full retry policy management")
        };
    }

    /// <summary>
    /// Permission definition with metadata
    /// </summary>
    public record PermissionDefinition(
        string Name,
        string Resource,
        string Action,
        string Description,
        bool IsSystemPermission = false);
}
