using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using WorkflowEngine.Domain.Entities;
using WorkflowEngine.Infrastructure.Data;

namespace WorkflowEngine.Infrastructure.Services;

public class DataSeedingService
{
    private readonly WorkflowDbContext _context;
    private readonly ILogger<DataSeedingService> _logger;

    public DataSeedingService(WorkflowDbContext context, ILogger<DataSeedingService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task SeedDefaultDataAsync()
    {
        try
        {
            await SeedPermissionsAsync();
            await SeedSystemRolesAsync();
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("Default data seeding completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred during data seeding");
            throw;
        }
    }

    private async Task SeedPermissionsAsync()
    {
        var permissions = new[]
        {
            // System-wide permissions
            new Permission { Name = "system.admin", Description = "Full system administration access" },
            new Permission { Name = "system.view", Description = "View system information" },
            
            // Tenant permissions
            new Permission { Name = "tenant.create", Description = "Create new tenants" },
            new Permission { Name = "tenant.read", Description = "View tenant information" },
            new Permission { Name = "tenant.update", Description = "Update tenant information" },
            new Permission { Name = "tenant.delete", Description = "Delete tenants" },
            new Permission { Name = "tenant.manage", Description = "Full tenant management" },
            
            // User permissions
            new Permission { Name = "user.create", Description = "Create new users" },
            new Permission { Name = "user.read", Description = "View user information" },
            new Permission { Name = "user.update", Description = "Update user information" },
            new Permission { Name = "user.delete", Description = "Delete users" },
            new Permission { Name = "user.manage", Description = "Full user management" },
            
            // Role permissions
            new Permission { Name = "role.create", Description = "Create new roles" },
            new Permission { Name = "role.read", Description = "View role information" },
            new Permission { Name = "role.update", Description = "Update role information" },
            new Permission { Name = "role.delete", Description = "Delete roles" },
            new Permission { Name = "role.manage", Description = "Full role management" },
            
            // Workflow Definition permissions
            new Permission { Name = "workflow.definition.create", Description = "Create workflow definitions" },
            new Permission { Name = "workflow.definition.read", Description = "View workflow definitions" },
            new Permission { Name = "workflow.definition.update", Description = "Update workflow definitions" },
            new Permission { Name = "workflow.definition.delete", Description = "Delete workflow definitions" },
            new Permission { Name = "workflow.definition.manage", Description = "Full workflow definition management" },
            
            // Workflow Instance permissions
            new Permission { Name = "workflow.instance.create", Description = "Create workflow instances" },
            new Permission { Name = "workflow.instance.read", Description = "View workflow instances" },
            new Permission { Name = "workflow.instance.update", Description = "Update workflow instances" },
            new Permission { Name = "workflow.instance.delete", Description = "Delete workflow instances" },
            new Permission { Name = "workflow.instance.execute", Description = "Execute workflow instances" },
            new Permission { Name = "workflow.instance.manage", Description = "Full workflow instance management" },
            
            // Activity permissions
            new Permission { Name = "activity.create", Description = "Create activities" },
            new Permission { Name = "activity.read", Description = "View activities" },
            new Permission { Name = "activity.update", Description = "Update activities" },
            new Permission { Name = "activity.delete", Description = "Delete activities" },
            new Permission { Name = "activity.execute", Description = "Execute activities" },
            new Permission { Name = "activity.manage", Description = "Full activity management" },
            
            // Monitoring permissions
            new Permission { Name = "monitoring.read", Description = "View monitoring data" },
            new Permission { Name = "monitoring.manage", Description = "Manage monitoring settings" },
            
            // Audit permissions
            new Permission { Name = "audit.read", Description = "View audit logs" },
            new Permission { Name = "audit.manage", Description = "Manage audit settings" }
        };

        foreach (var permission in permissions)
        {
            var existingPermission = await _context.Permissions
                .FirstOrDefaultAsync(p => p.Name == permission.Name);

            if (existingPermission == null)
            {
                _context.Permissions.Add(permission);
                _logger.LogDebug("Added permission: {PermissionName}", permission.Name);
            }
        }
    }

    private async Task SeedSystemRolesAsync()
    {
        // Create SuperAdmin role (system-wide, not tenant-specific)
        var superAdminRole = await _context.Roles
            .FirstOrDefaultAsync(r => r.Name == "SuperAdmin" && r.TenantId == null);

        if (superAdminRole == null)
        {
            superAdminRole = new Role
            {
                Name = "SuperAdmin",
                Description = "Super Administrator with access to all tenants and system functions",
                TenantId = null, // System-wide role
                IsSystemRole = true
            };

            _context.Roles.Add(superAdminRole);
            await _context.SaveChangesAsync(); // Save to get the ID

            // Assign all permissions to SuperAdmin
            var allPermissions = await _context.Permissions.ToListAsync();
            foreach (var permission in allPermissions)
            {
                _context.RolePermissions.Add(new RolePermission
                {
                    RoleId = superAdminRole.Id,
                    PermissionId = permission.Id
                });
            }

            _logger.LogInformation("Created SuperAdmin role with all permissions");
        }

        // Create default tenant-specific roles template
        await SeedTenantRoleTemplatesAsync();
    }

    private async Task SeedTenantRoleTemplatesAsync()
    {
        // These are template roles that will be created for each tenant
        var roleTemplates = new[]
        {
            new { Name = "TenantAdmin", Description = "Tenant Administrator with full access to tenant resources", Permissions = new[]
                {
                    "tenant.read", "tenant.update",
                    "user.manage", "role.manage",
                    "workflow.definition.manage", "workflow.instance.manage",
                    "activity.manage", "monitoring.read", "audit.read"
                }
            },
            new { Name = "WorkflowManager", Description = "Workflow Manager with access to workflow operations", Permissions = new[]
                {
                    "workflow.definition.manage", "workflow.instance.manage",
                    "activity.read", "activity.execute", "monitoring.read"
                }
            },
            new { Name = "WorkflowUser", Description = "Workflow User with basic workflow access", Permissions = new[]
                {
                    "workflow.definition.read", "workflow.instance.create",
                    "workflow.instance.read", "workflow.instance.execute",
                    "activity.read", "activity.execute"
                }
            },
            new { Name = "Viewer", Description = "Read-only access to workflows and activities", Permissions = new[]
                {
                    "workflow.definition.read", "workflow.instance.read",
                    "activity.read", "monitoring.read"
                }
            }
        };

        // Store role templates for later use when creating tenants
        foreach (var template in roleTemplates)
        {
            var existingTemplate = await _context.RoleTemplates
                .FirstOrDefaultAsync(rt => rt.Name == template.Name);

            if (existingTemplate == null)
            {
                var roleTemplate = new RoleTemplate
                {
                    Name = template.Name,
                    Description = template.Description,
                    Permissions = string.Join(",", template.Permissions)
                };

                _context.RoleTemplates.Add(roleTemplate);
                _logger.LogDebug("Added role template: {RoleName}", template.Name);
            }
        }
    }

    public async Task CreateTenantRolesAsync(Guid tenantId)
    {
        var roleTemplates = await _context.RoleTemplates.ToListAsync();
        var allPermissions = await _context.Permissions.ToListAsync();

        foreach (var template in roleTemplates)
        {
            var existingRole = await _context.Roles
                .FirstOrDefaultAsync(r => r.Name == template.Name && r.TenantId == tenantId);

            if (existingRole == null)
            {
                var role = new Role
                {
                    Name = template.Name,
                    Description = template.Description,
                    TenantId = tenantId,
                    IsSystemRole = false
                };

                _context.Roles.Add(role);
                await _context.SaveChangesAsync(); // Save to get the ID

                // Assign permissions based on template
                var permissionNames = template.Permissions.Split(',', StringSplitOptions.RemoveEmptyEntries);
                foreach (var permissionName in permissionNames)
                {
                    var permission = allPermissions.FirstOrDefault(p => p.Name == permissionName.Trim());
                    if (permission != null)
                    {
                        _context.RolePermissions.Add(new RolePermission
                        {
                            RoleId = role.Id,
                            PermissionId = permission.Id
                        });
                    }
                }

                _logger.LogDebug("Created tenant role: {RoleName} for tenant {TenantId}", template.Name, tenantId);
            }
        }

        await _context.SaveChangesAsync();
    }
}
