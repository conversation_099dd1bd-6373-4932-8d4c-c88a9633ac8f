using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using WorkflowEngine.Domain.Entities;
using WorkflowEngine.Domain.Constants;
using WorkflowEngine.Infrastructure.Data;

namespace WorkflowEngine.Infrastructure.Services;

public class DataSeedingService
{
    private readonly WorkflowDbContext _context;
    private readonly ILogger<DataSeedingService> _logger;

    public DataSeedingService(WorkflowDbContext context, ILogger<DataSeedingService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task SeedDefaultDataAsync()
    {
        try
        {
            await SeedPermissionsAsync();
            await SeedSystemRolesAsync();
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("Default data seeding completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred during data seeding");
            throw;
        }
    }

    private async Task SeedPermissionsAsync()
    {
        var permissionDefinitions = PermissionConstants.All.Permissions;

        foreach (var permissionDef in permissionDefinitions)
        {
            var existingPermission = await _context.Permissions
                .FirstOrDefaultAsync(p => p.Name == permissionDef.Name);

            if (existingPermission == null)
            {
                var permission = new Permission
                {
                    Name = permissionDef.Name,
                    Description = permissionDef.Description,
                    Resource = permissionDef.Resource,
                    Action = permissionDef.Action,
                    IsSystemPermission = permissionDef.IsSystemPermission
                };

                _context.Permissions.Add(permission);
                _logger.LogDebug("Added permission: {PermissionName}", permission.Name);
            }
        }
    }

    private async Task SeedSystemRolesAsync()
    {
        // Create SuperAdmin role (system-wide, not tenant-specific)
        var superAdminRole = await _context.Roles
            .FirstOrDefaultAsync(r => r.Name == "SuperAdmin" && r.TenantId == null);

        if (superAdminRole == null)
        {
            superAdminRole = new Role
            {
                Name = "SuperAdmin",
                Description = "Super Administrator with access to all tenants and system functions",
                TenantId = null, // System-wide role
                IsSystemRole = true
            };

            _context.Roles.Add(superAdminRole);
            await _context.SaveChangesAsync(); // Save to get the ID

            // Assign all permissions to SuperAdmin
            var allPermissions = await _context.Permissions.ToListAsync();
            foreach (var permission in allPermissions)
            {
                _context.RolePermissions.Add(new RolePermission
                {
                    RoleId = superAdminRole.Id,
                    PermissionId = permission.Id
                });
            }

            _logger.LogInformation("Created SuperAdmin role with all permissions");
        }

        // Create default tenant-specific roles template
        await SeedTenantRoleTemplatesAsync();
    }

    private async Task SeedTenantRoleTemplatesAsync()
    {
        // These are template roles that will be created for each tenant
        var roleTemplates = new[]
        {
            new { Name = "TenantAdmin", Description = "Tenant Administrator with full access to tenant resources", Permissions = new[]
                {
                    PermissionConstants.Tenant.Read, PermissionConstants.Tenant.Update,
                    PermissionConstants.User.Manage, PermissionConstants.Role.Manage,
                    PermissionConstants.WorkflowDefinition.Manage, PermissionConstants.WorkflowInstance.Manage,
                    PermissionConstants.Activity.Manage, PermissionConstants.Monitoring.Read, PermissionConstants.Audit.Read,
                    PermissionConstants.RetryPolicy.Manage, PermissionConstants.DeadLetterQueue.Manage
                }
            },
            new { Name = "WorkflowManager", Description = "Workflow Manager with access to workflow operations", Permissions = new[]
                {
                    PermissionConstants.WorkflowDefinition.Manage, PermissionConstants.WorkflowInstance.Manage,
                    PermissionConstants.Activity.Read, PermissionConstants.Activity.Execute, PermissionConstants.Activity.Configure,
                    PermissionConstants.Monitoring.Read, PermissionConstants.Monitoring.ViewMetrics,
                    PermissionConstants.RetryPolicy.Read, PermissionConstants.DeadLetterQueue.Read
                }
            },
            new { Name = "WorkflowUser", Description = "Workflow User with basic workflow access", Permissions = new[]
                {
                    PermissionConstants.WorkflowDefinition.Read, PermissionConstants.WorkflowInstance.Create,
                    PermissionConstants.WorkflowInstance.Read, PermissionConstants.WorkflowInstance.Execute,
                    PermissionConstants.WorkflowInstance.Start, PermissionConstants.WorkflowInstance.Stop,
                    PermissionConstants.Activity.Read, PermissionConstants.Activity.Execute,
                    PermissionConstants.User.ViewProfile
                }
            },
            new { Name = "Viewer", Description = "Read-only access to workflows and activities", Permissions = new[]
                {
                    PermissionConstants.WorkflowDefinition.Read, PermissionConstants.WorkflowInstance.Read,
                    PermissionConstants.Activity.Read, PermissionConstants.Monitoring.Read,
                    PermissionConstants.Audit.ViewLogs, PermissionConstants.User.ViewProfile
                }
            }
        };

        // Store role templates for later use when creating tenants
        foreach (var template in roleTemplates)
        {
            var existingTemplate = await _context.RoleTemplates
                .FirstOrDefaultAsync(rt => rt.Name == template.Name);

            if (existingTemplate == null)
            {
                var roleTemplate = new RoleTemplate
                {
                    Name = template.Name,
                    Description = template.Description,
                    Permissions = string.Join(",", template.Permissions)
                };

                _context.RoleTemplates.Add(roleTemplate);
                _logger.LogDebug("Added role template: {RoleName}", template.Name);
            }
        }
    }

    public async Task CreateTenantRolesAsync(Guid tenantId)
    {
        var roleTemplates = await _context.RoleTemplates.ToListAsync();
        var allPermissions = await _context.Permissions.ToListAsync();

        foreach (var template in roleTemplates)
        {
            var existingRole = await _context.Roles
                .FirstOrDefaultAsync(r => r.Name == template.Name && r.TenantId == tenantId);

            if (existingRole == null)
            {
                var role = new Role
                {
                    Name = template.Name,
                    Description = template.Description,
                    TenantId = tenantId,
                    IsSystemRole = false
                };

                _context.Roles.Add(role);
                await _context.SaveChangesAsync(); // Save to get the ID

                // Assign permissions based on template
                var permissionNames = template.Permissions.Split(',', StringSplitOptions.RemoveEmptyEntries);
                foreach (var permissionName in permissionNames)
                {
                    var permission = allPermissions.FirstOrDefault(p => p.Name == permissionName.Trim());
                    if (permission != null)
                    {
                        _context.RolePermissions.Add(new RolePermission
                        {
                            RoleId = role.Id,
                            PermissionId = permission.Id
                        });
                    }
                }

                _logger.LogDebug("Created tenant role: {RoleName} for tenant {TenantId}", template.Name, tenantId);
            }
        }

        await _context.SaveChangesAsync();
    }
}
