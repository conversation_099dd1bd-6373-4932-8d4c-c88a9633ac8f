using Microsoft.EntityFrameworkCore;
using WorkflowEngine.Application.Interfaces;
using WorkflowEngine.Domain.Entities;
using WorkflowEngine.Domain.Enums;
using WorkflowEngine.Infrastructure.Data;

namespace WorkflowEngine.Infrastructure.Repositories;

public class ActivityRepository : BaseTenantRepository<Activity>, IActivityRepository
{
    public ActivityRepository(WorkflowDbContext context) : base(context)
    {
    }

    public async Task<Activity?> GetByNameAndVersionAsync(Guid tenantId, string name, string version, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .FirstOrDefaultAsync(a => a.TenantId == tenantId && a.Name == name && a.Version == version, cancellationToken);
    }

    public async Task<IEnumerable<Activity>> GetByTypeAsync(Guid tenantId, ActivityType type, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(a => a.TenantId == tenantId && a.Type == type)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Activity>> GetActiveActivitiesAsync(Guid tenantId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(a => a.TenantId == tenantId && a.IsActive)
            .ToListAsync(cancellationToken);
    }
}
