using WorkflowEngine.Domain.Common;
using WorkflowEngine.Domain.Enums;

namespace WorkflowEngine.Domain.Entities;

public class WorkflowInstance : BaseEntity, ITenantEntity
{
    public Guid TenantId { get; set; }
    public Guid WorkflowDefinitionId { get; set; }
    public string Name { get; set; } = string.Empty;
    public WorkflowInstanceStatus Status { get; set; } = WorkflowInstanceStatus.NotStarted;
    public Guid? CurrentStepId { get; set; }
    public string? InputData { get; set; }
    public string? OutputData { get; set; }
    public string? Variables { get; set; }
    public DateTime? StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public string? ErrorMessage { get; set; }
    public int RetryCount { get; set; } = 0;
    public DateTime? NextExecutionTime { get; set; }

    // Navigation properties
    public virtual Tenant Tenant { get; set; } = null!;
    public virtual WorkflowDefinition WorkflowDefinition { get; set; } = null!;
    public virtual WorkflowStep? CurrentStep { get; set; }
    public virtual ICollection<ActivityExecution> ActivityExecutions { get; set; } = new List<ActivityExecution>();
}
