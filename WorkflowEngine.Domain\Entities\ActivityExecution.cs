using WorkflowEngine.Domain.Common;
using WorkflowEngine.Domain.Enums;

namespace WorkflowEngine.Domain.Entities;

public class ActivityExecution : BaseEntity, ITenantEntity
{
    public Guid TenantId { get; set; }
    public Guid WorkflowInstanceId { get; set; }
    public Guid WorkflowStepId { get; set; }
    public Guid ActivityId { get; set; }
    public ExecutionStatus Status { get; set; } = ExecutionStatus.Pending;
    public string? InputData { get; set; }
    public string? OutputData { get; set; }
    public string? ErrorMessage { get; set; }
    public string? StackTrace { get; set; }
    public DateTime? StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public int RetryCount { get; set; } = 0;
    public int MaxRetries { get; set; } = 3;
    public DateTime? NextRetryAt { get; set; }
    public TimeSpan? Duration { get; set; }

    // Navigation properties
    public virtual Tenant Tenant { get; set; } = null!;
    public virtual WorkflowInstance WorkflowInstance { get; set; } = null!;
    public virtual WorkflowStep WorkflowStep { get; set; } = null!;
    public virtual Activity Activity { get; set; } = null!;
}
