using WorkflowEngine.Domain.Entities;
using WorkflowEngine.Domain.Enums;

namespace WorkflowEngine.Application.Interfaces;

public interface IActivityExecutionRepository : ITenantRepository<ActivityExecution>
{
    Task<IEnumerable<ActivityExecution>> GetByWorkflowInstanceAsync(Guid tenantId, Guid workflowInstanceId, CancellationToken cancellationToken = default);
    Task<IEnumerable<ActivityExecution>> GetByStatusAsync(Guid tenantId, ExecutionStatus status, CancellationToken cancellationToken = default);
    Task<IEnumerable<ActivityExecution>> GetPendingExecutionsAsync(DateTime beforeTime, CancellationToken cancellationToken = default);
    Task<IEnumerable<ActivityExecution>> GetFailedExecutionsForRetryAsync(DateTime beforeTime, CancellationToken cancellationToken = default);
    Task<ActivityExecution?> GetLatestExecutionAsync(Guid tenantId, Guid workflowInstanceId, Guid workflowStepId, CancellationToken cancellationToken = default);
}
