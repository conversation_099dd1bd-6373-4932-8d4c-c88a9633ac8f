using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.DependencyInjection;
using WorkflowEngine.Infrastructure.Data;

namespace WorkflowEngine.Infrastructure.Attributes;

[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method)]
public class RequireTenantAttribute : Attribute, IAsyncActionFilter
{
    public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
    {
        var tenantContext = context.HttpContext.RequestServices.GetRequiredService<ITenantContext>();
        
        if (tenantContext.TenantId == Guid.Empty)
        {
            context.Result = new BadRequestObjectResult(new
            {
                error = "tenant_required",
                message = "A valid tenant context is required for this operation"
            });
            return;
        }

        await next();
    }
}
