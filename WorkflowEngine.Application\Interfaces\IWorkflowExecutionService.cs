using WorkflowEngine.Domain.Entities;

namespace WorkflowEngine.Application.Interfaces;

public interface IWorkflowExecutionService
{
    Task<WorkflowExecutionResult> ExecuteStepAsync(WorkflowInstance instance, WorkflowStep step, CancellationToken cancellationToken = default);
    Task<ActivityExecutionResult> ExecuteActivityAsync(ActivityExecutionContext context, CancellationToken cancellationToken = default);
    Task<WorkflowStep?> GetNextStepAsync(WorkflowInstance instance, WorkflowStep currentStep, string? outcome = null, CancellationToken cancellationToken = default);
    Task<bool> ShouldProgressToNextStepAsync(WorkflowInstance instance, WorkflowStep currentStep, CancellationToken cancellationToken = default);
    Task UpdateInstanceProgressAsync(WorkflowInstance instance, WorkflowExecutionResult result, CancellationToken cancellationToken = default);
}
